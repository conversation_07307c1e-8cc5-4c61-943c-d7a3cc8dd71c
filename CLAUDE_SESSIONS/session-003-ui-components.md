# Claude Session 003: UI Components

## Mission
Build all UI components for the chat interface.

## Duration: 2 hours

## Prerequisites
- Read CONTEXT.md
- Tailwind CSS already configured
- Can work in parallel with other sessions

## Tasks

### 1. Configure Dark Theme (15 min)
Update `/src/app/globals.css`:
- Dark background colors
- Custom CSS variables
- Tailwind theme extensions

Update `tailwind.config.ts`:
- Dark mode class strategy
- Custom colors matching design

### 2. Layout Components (30 min)
Create `/src/components/layout/header.tsx`:
- JustSimpleChat logo with gradient
- User menu integration point
- Mobile menu button

Create `/src/components/layout/sidebar.tsx`:
- Conversation list
- New chat button
- Collapsible on mobile
- Active conversation highlight

### 3. Chat Components (1 hour)
Create `/src/components/chat/message-list.tsx`:
- Scrollable message container
- Auto-scroll to bottom
- Loading skeleton
- Empty state

Create `/src/components/chat/message.tsx`:
- User vs assistant styling
- Markdown rendering (react-markdown)
- Code syntax highlighting
- Copy button for code blocks
- Model indicator integration

Create `/src/components/chat/message-input.tsx`:
- Auto-expanding textarea
- Send button with loading state
- Ctrl/Cmd+Enter to send
- Character count
- Disabled while streaming

Create `/src/components/chat/model-pill.tsx`:
- Small pill showing model name
- Hover tooltip with routing reason
- Different colors per model

### 4. UI Utilities (15 min)
Create `/src/components/ui/loading.tsx`:
- Spinner component
- Skeleton loaders
- Typing indicator

Install shadcn components:
```bash
npx shadcn-ui@latest add button
npx shadcn-ui@latest add dropdown-menu
npx shadcn-ui@latest add tooltip
npx shadcn-ui@latest add scroll-area
```

## Styling Guidelines
- Dark theme by default (#1a1d23 background)
- Cyan-amber gradient for branding
- Smooth transitions (200ms)
- Mobile-first responsive design
- High contrast for accessibility

## Expected Outputs
- [ ] All layout components
- [ ] All chat components
- [ ] UI utilities
- [ ] Updated global styles
- [ ] shadcn components configured

## Success Criteria
- [ ] Components render without errors
- [ ] Dark theme looks polished
- [ ] Mobile responsive
- [ ] Smooth animations
- [ ] Accessible markup

## Handoff Notes
- Components are ready for integration
- Props interfaces support future features
- State management can be added later