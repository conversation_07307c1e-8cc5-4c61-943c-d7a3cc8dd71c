# Claude Session 002: Authentication Setup

## Mission
Implement Google authentication with NextAuth.js.

## Duration: 45 minutes

## Prerequisites
- Read CONTEXT.md
- Session 001 output (types) helpful but not required

## Tasks

### 1. Create Auth Configuration (20 min)
Create `/src/lib/auth.ts`:
```typescript
import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      // Add user ID to session
      return session;
    },
  },
  pages: {
    signIn: '/',
    error: '/auth/error',
  },
};
```

### 2. Create Auth Route Handler (10 min)
Create `/src/app/api/auth/[...nextauth]/route.ts`:
- Export GET and POST handlers
- Use authOptions from lib/auth.ts

### 3. Create Auth Components (10 min)
Create `/src/components/auth/sign-in-button.tsx`:
- Google sign-in button with logo
- Loading state
- Error handling

Create `/src/components/auth/user-menu.tsx`:
- User avatar and name
- Sign out option
- Dropdown menu

### 4. Add Middleware (5 min)
Create `/src/middleware.ts`:
- Protect /chat routes
- Redirect to sign-in if not authenticated

## Expected Outputs
- [ ] `/src/lib/auth.ts` - NextAuth configuration
- [ ] `/src/app/api/auth/[...nextauth]/route.ts` - Auth route
- [ ] `/src/components/auth/*` - Auth components
- [ ] `/src/middleware.ts` - Route protection

## Success Criteria
- [ ] Google OAuth flow works
- [ ] Session persists on refresh
- [ ] Protected routes redirect properly
- [ ] User info displays correctly

## Handoff Notes
- Auth is now ready for integration
- UI components can use `useSession` hook
- API routes can check session