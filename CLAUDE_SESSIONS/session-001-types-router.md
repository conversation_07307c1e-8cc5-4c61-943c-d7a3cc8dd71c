# Claude Session 001: Types & Router Implementation

## Mission
Create the TypeScript types and implement the core auto-router feature.

## Duration: 1.5 hours

## Prerequisites
- Project already set up with Next.js
- Dependencies installed
- Read CONTEXT.md first

## Tasks

### 1. Create Type Definitions (20 min)
Create `/src/types/index.ts` with:
```typescript
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  model?: string;
  tokens?: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface RouterDecision {
  model: string;
  reason: string;
  estimated_cost?: number;
  fallback_chain?: string[];
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
}

export interface ChatRequest {
  message: string;
  conversationId?: string;
}

export interface ChatResponse {
  message: Message;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_cost: number;
  };
}
```

### 2. Implement Model Configuration (15 min)
Create `/src/lib/ai/models.ts`:
```typescript
export const MODELS = {
  'openai/gpt-3.5-turbo': {
    id: 'openai/gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    maxTokens: 4096,
    inputCostPer1k: 0.001,
    outputCostPer1k: 0.002,
    speed: 'fast',
    capabilities: ['general', 'chat']
  },
  'anthropic/claude-instant-1.2': {
    id: 'anthropic/claude-instant-1.2',
    name: 'Claude Instant',
    maxTokens: 8192,
    inputCostPer1k: 0.002,
    outputCostPer1k: 0.004,
    speed: 'fast',
    capabilities: ['general', 'code', 'analysis']
  },
  'openai/gpt-4-turbo-preview': {
    id: 'openai/gpt-4-turbo-preview',
    name: 'GPT-4 Turbo',
    maxTokens: 8192,
    inputCostPer1k: 0.01,
    outputCostPer1k: 0.03,
    speed: 'medium',
    capabilities: ['general', 'code', 'analysis', 'complex']
  }
} as const;

export type ModelId = keyof typeof MODELS;
```

### 3. Implement Auto Router (30 min)
Create `/src/lib/ai/router.ts`:
- Token estimation function
- Routing logic based on:
  - Query length (<500 tokens = GPT-3.5)
  - Code detection (presence of ``` or "code" = Claude)
  - Complexity (default = GPT-4)
- Fallback chains for each model
- Cost estimation

### 4. Create OpenRouter Client (25 min)
Create `/src/lib/ai/client.ts`:
- Fetch wrapper for OpenRouter API
- Streaming response handler
- Error handling with exponential backoff
- Request/response type safety

### 5. Testing & Documentation (10 min)
- Create simple test cases in comments
- Update CONTEXT.md with implementation notes
- Ensure all exports are properly typed

## Expected Outputs
- [ ] `/src/types/index.ts` - All type definitions
- [ ] `/src/lib/ai/models.ts` - Model configurations
- [ ] `/src/lib/ai/router.ts` - Router implementation
- [ ] `/src/lib/ai/client.ts` - API client
- [ ] Updated CONTEXT.md with notes

## Success Criteria
- [ ] Types compile without errors
- [ ] Router returns correct model for test inputs
- [ ] Client can make API calls (when API key added)
- [ ] All functions have explicit return types

## Handoff Notes
After completion, the next session can start on:
- Authentication setup (Session 002)
- UI components (Session 003)
- API routes (Session 004)

These can run in parallel as they don't depend on each other.