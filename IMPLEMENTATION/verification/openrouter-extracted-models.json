[{"id": "sentientagi/dobby-mini-unhinged-plus-llama-3.1-8b", "name": "SentientAGI: Dobby Mini Plus Llama 3.1 8B", "description": "Dobby-Mini-Leashed-Llama-3.1-8B and Dobby-Mini-Unhinged-Llama-3.1-8B are language models fine-tuned from Llama-3.1-8B-Instruct. Dobby models have a strong conviction towards personal freedom, decentra...", "contextWindow": 131072, "maxOutput": 32768, "inputCost": 0.19999999999999998, "outputCost": 0.19999999999999998, "isFree": false, "originalProvider": "sentientagi", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "thedrummer/valkyrie-49b-v1", "name": "TheDrummer: Valkyrie 49B V1", "description": "Built on top of NVIDIA's Llama 3.3 Nemotron Super 49B, <PERSON><PERSON><PERSON> is TheDrummer's newest model drop for creative writing....", "contextWindow": 131072, "maxOutput": 131072, "inputCost": 0.5, "outputCost": 0.7999999999999999, "isFree": false, "originalProvider": "thedrummer", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "thedrummer/anubis-pro-105b-v1", "name": "TheDrummer: Anubis Pro 105B V1", "description": "Anubis Pro 105B v1 is an expanded and refined variant of Meta’s Llama 3.3 70B, featuring 50% additional layers and further fine-tuning to leverage its increased capacity. Designed for advanced narrati...", "contextWindow": 131072, "maxOutput": 131072, "inputCost": 0.7999999999999999, "outputCost": 1, "isFree": false, "originalProvider": "thedrummer", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "thedrummer/skyfall-36b-v2", "name": "TheDrummer: Skyfall 36B V2", "description": "Skyfall 36B v2 is an enhanced iteration of Mistral Small 2501, specifically fine-tuned for improved creativity, nuanced writing, role-playing, and coherent storytelling....", "contextWindow": 32768, "maxOutput": 32768, "inputCost": 0.5, "outputCost": 0.7999999999999999, "isFree": false, "originalProvider": "thedrummer", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "sao10k/l3.3-euryale-70b", "name": "Sao10K: Llama 3.3 Euryale 70B", "description": "Euryale L3.3 70B is a model focused on creative roleplay from [Sao10k](https://ko-fi.com/sao10k). It is the successor of [Euryale L3 70B v2.2](/models/sao10k/l3-euryale-70b)....", "contextWindow": 131072, "maxOutput": 16384, "inputCost": 0.7, "outputCost": 0.7999999999999999, "isFree": false, "originalProvider": "sao10k", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": "llama3"}}, {"id": "eva-unit-01/eva-llama-3.33-70b", "name": "EVA Llama 3.33 70B", "description": "EVA Llama 3.33 70b is a roleplay and storywriting specialist model. It is a full-parameter finetune of [Llama-3.3-70B-Instruct](https://openrouter.ai/meta-llama/llama-3.3-70b-instruct) on mixture of s...", "contextWindow": 16384, "maxOutput": 4096, "inputCost": 4, "outputCost": 6, "isFree": false, "originalProvider": "eva-unit-01", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": "llama3"}}, {"id": "cognitivecomputations/dolphin3.0-r1-mistral-24b:free", "name": "Dolphin3.0 R1 Mistral 24B (free)", "description": "Dolphin 3.0 R1 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and ge...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "cognitivecomputations", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}}, {"id": "cognitivecomputations/dolphin3.0-mistral-24b:free", "name": "Dolphin3.0 Mistral 24B (free)", "description": "Dolphin 3.0 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and gener...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "cognitivecomputations", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "nousresearch/deephermes-3-mistral-24b-preview:free", "name": "Nous: DeepHermes 3 Mistral 24B Preview (free)", "description": "DeepHermes 3 (Mistral 24B Preview) is an instruction-tuned language model by Nous Research based on Mistral-Small-24B, designed for chat, function calling, and advanced multi-turn reasoning. It introd...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "nousresearch", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "nousresearch/deephermes-3-llama-3-8b-preview:free", "name": "Nous: DeepHermes 3 Llama 3 8B Preview (free)", "description": "DeepHermes 3 Preview is the latest version of our flagship Hermes series of LLMs by Nous Research, and one of the first models in the world to unify Reasoning (long chains of thought that improve answ...", "contextWindow": 131072, "maxOutput": 32768, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "nousresearch", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "ai21/jamba-1.6-large", "name": "AI21: Jamba 1.6 Large", "description": "AI21 Jamba Large 1.6 is a high-performance hybrid foundation model combining State Space Models (Mamba) with Transformer attention mechanisms. Developed by AI21, it excels in extremely long-context ha...", "contextWindow": 256000, "maxOutput": 4096, "inputCost": 2, "outputCost": 8, "isFree": false, "originalProvider": "ai21", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "ai21/jamba-1.6-mini", "name": "AI21: Jamba Mini 1.6", "description": "AI21 Jamba Mini 1.6 is a hybrid foundation model combining State Space Models (Mamba) with Transformer attention mechanisms. With 12 billion active parameters (52 billion total), this model excels in ...", "contextWindow": 256000, "maxOutput": 4096, "inputCost": 0.19999999999999998, "outputCost": 0.39999999999999997, "isFree": false, "originalProvider": "ai21", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "rekaai/reka-flash-3:free", "name": "Reka: <PERSON> 3 (free)", "description": "Reka Flash 3 is a general-purpose, instruction-tuned large language model with 21 billion parameters, developed by Reka. It excels at general chat, coding tasks, instruction-following, and function ca...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "re<PERSON><PERSON>", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "arcee-ai/maestro-reasoning", "name": "Arcee AI: <PERSON><PERSON> Reasoning", "description": "Maestro Reasoning is Arcee's flagship analysis model: a 32 B‑parameter derivative of Qwen 2.5‑32 B tuned with DPO and chain‑of‑thought RL for step‑by‑step logic. Compared to the earlier 7 B preview, t...", "contextWindow": 131072, "maxOutput": 32000, "inputCost": 0.8999999999999999, "outputCost": 3.3000000000000003, "isFree": false, "originalProvider": "arcee-ai", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "arcee-ai/virtuoso-large", "name": "Arcee AI: Virtuoso <PERSON>", "description": "Virtuoso‑Large is Arcee's top‑tier general‑purpose LLM at 72 B parameters, tuned to tackle cross‑domain reasoning, creative writing and enterprise QA. Unlike many 70 B peers, it retains the 128 k cont...", "contextWindow": 131072, "maxOutput": 64000, "inputCost": 0.75, "outputCost": 1.2, "isFree": false, "originalProvider": "arcee-ai", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "arcee-ai/coder-large", "name": "Arcee AI: <PERSON><PERSON>", "description": "Coder‑Large is a 32 B‑parameter offspring of Qwen 2.5‑Instruct that has been further trained on permissively‑licensed GitHub, CodeSearchNet and synthetic bug‑fix corpora. It supports a 32k context win...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0.5, "outputCost": 0.7999999999999999, "isFree": false, "originalProvider": "arcee-ai", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "arcee-ai/caller-large", "name": "Arcee AI: <PERSON><PERSON>", "description": "Caller Large is Arcee's specialist \"function‑calling\" SLM built to orchestrate external tools and APIs. Instead of maximizing next‑token accuracy, training focuses on structured JSON outputs, paramete...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0.55, "outputCost": 0.85, "isFree": false, "originalProvider": "arcee-ai", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "arcee-ai/spotlight", "name": "Arcee AI: Spotlight", "description": "Spotlight is a 7‑billion‑parameter vision‑language model derived from Qwen 2.5‑VL and fine‑tuned by Arcee AI for tight image‑text grounding tasks. It offers a 32 k‑token context window, enabling rich ...", "contextWindow": 131072, "maxOutput": 65537, "inputCost": 0.18, "outputCost": 0.18, "isFree": false, "originalProvider": "arcee-ai", "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "microsoft/phi-4-reasoning-plus:free", "name": "Microsoft: Phi 4 Reasoning Plus (free)", "description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It us...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "microsoft", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "microsoft/phi-4-reasoning-plus", "name": "Microsoft: Phi 4 Reasoning Plus", "description": "Phi-4-reasoning-plus is an enhanced 14B parameter model from Microsoft, fine-tuned from Phi-4 with additional reinforcement learning to boost accuracy on math, science, and code reasoning tasks. It us...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0.07, "outputCost": 0.35, "isFree": false, "originalProvider": "microsoft", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "microsoft/phi-4", "name": "Microsoft: Phi 4", "description": "[Microsoft Research](/microsoft) Phi-4 is designed to perform well in complex reasoning tasks and can operate efficiently in situations with limited memory or where quick responses are needed.   At 14...", "contextWindow": 16384, "maxOutput": 16384, "inputCost": 0.07, "outputCost": 0.14, "isFree": false, "originalProvider": "microsoft", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "microsoft/mai-ds-r1:free", "name": "Microsoft: MAI DS R1 (free)", "description": "MAI-DS-R1 is a post-trained variant of DeepSeek-R1 developed by the Microsoft AI team to improve the model’s responsiveness on previously blocked topics while enhancing its safety profile. Built on to...", "contextWindow": 163840, "maxOutput": 32768, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "microsoft", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": "deepseek-r1"}}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1", "description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived fro...", "contextWindow": 131072, "maxOutput": 32768, "inputCost": 0.13, "outputCost": 0.39999999999999997, "isFree": false, "originalProvider": "nvidia", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "nvidia/llama-3.1-nemotron-ultra-253b-v1", "name": "NVIDIA: Llama 3.1 Nemotron Ultra 253B v1", "description": "Llama-3.1-Nemotron-Ultra-253B-v1 is a large language model (LLM) optimized for advanced reasoning, human-interactive chat, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Me...", "contextWindow": 131072, "maxOutput": 32768, "inputCost": 0.6, "outputCost": 1.7999999999999998, "isFree": false, "originalProvider": "nvidia", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": null}}, {"id": "qwen/qwq-32b:free", "name": "Qwen: QwQ 32B (free)", "description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in d...", "contextWindow": 40000, "maxOutput": 40000, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "qwen", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "qwq"}}, {"id": "qwen/qwq-32b", "name": "Qwen: QwQ 32B", "description": "QwQ is the reasoning model of the Qwen series. Compared with conventional instruction-tuned models, QwQ, which is capable of thinking and reasoning, can achieve significantly enhanced performance in d...", "contextWindow": 131072, "maxOutput": 32768, "inputCost": 0.15, "outputCost": 0.19999999999999998, "isFree": false, "originalProvider": "qwen", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "qwq"}}, {"id": "qwen/qwen3-235b-a22b:free", "name": "Qwen: Qwen3 235B A22B (free)", "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex r...", "contextWindow": 40960, "maxOutput": 10240, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "qwen", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}}, {"id": "qwen/qwen3-235b-a22b", "name": "Qwen: Qwen3 235B A22B", "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex r...", "contextWindow": 40960, "maxOutput": 40960, "inputCost": 0.13, "outputCost": 0.6, "isFree": false, "originalProvider": "qwen", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}}, {"id": "qwen/qwen2.5-vl-72b-instruct", "name": "Qwen: Qwen2.5 VL 72B Instruct", "description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images....", "contextWindow": 32000, "maxOutput": 8000, "inputCost": 0.25, "outputCost": 0.75, "isFree": false, "originalProvider": "qwen", "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}}, {"id": "qwen/qwen-vl-max", "name": "<PERSON>wen: <PERSON><PERSON> VL Max", "description": "Qwen VL Max is a visual understanding model with 7500 tokens context length. It excels in delivering optimal performance for a broader spectrum of complex tasks. ...", "contextWindow": 7500, "maxOutput": 1500, "inputCost": 0.7999999999999999, "outputCost": 3.1999999999999997, "isFree": false, "originalProvider": "qwen", "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}}, {"id": "thudm/glm-z1-rumination-32b", "name": "THUDM: GLM Z1 Rumination 32B ", "description": "THUDM: GLM Z1 Rumination 32B is a 32B-parameter deep reasoning model from the GLM-4-Z1 series, optimized for complex, open-ended tasks requiring prolonged deliberation. It builds upon glm-4-32b-0414 w...", "contextWindow": 32000, "maxOutput": 32000, "inputCost": 0.24, "outputCost": 0.24, "isFree": false, "originalProvider": "thudm", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}}, {"id": "thudm/glm-z1-32b", "name": "THUDM: GLM Z1 32B", "description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific an...", "contextWindow": 32000, "maxOutput": 32000, "inputCost": 0.24, "outputCost": 0.24, "isFree": false, "originalProvider": "thudm", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}}, {"id": "thudm/glm-4-32b", "name": "THUDM: GLM 4 32B", "description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-h...", "contextWindow": 32000, "maxOutput": 32000, "inputCost": 0.24, "outputCost": 0.24, "isFree": false, "originalProvider": "thudm", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "moonshotai/kimi-vl-a3b-thinking:free", "name": "Moonshot AI: <PERSON><PERSON> VL A3B Thinking (free)", "description": "Kimi-VL is a lightweight Mixture-of-Experts vision-language model that activates only 2.8B parameters per step while delivering strong performance on multimodal reasoning and long-context tasks. The K...", "contextWindow": 131072, "maxOutput": 32768, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "moonshotai", "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "moonshotai/moonlight-16b-a3b-instruct:free", "name": "Moonshot AI: Moonlight 16B A3B Instruct (free)", "description": "Moonlight-16B-A3B-Instruct is a 16B-parameter Mixture-of-Experts (MoE) language model developed by Moonshot AI. It is optimized for instruction-following tasks with 3B activated parameters per inferen...", "contextWindow": 8192, "maxOutput": 2048, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "moonshotai", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "sarvamai/sarvam-m:free", "name": "Sarvam AI: <PERSON><PERSON><PERSON><PERSON><PERSON> (free)", "description": "Sarvam-M is a 24 B-parameter, instruction-tuned derivative of Mistral-Small-3.1-24B-Base-2503, post-trained on English plus eleven major Indic languages (bn, hi, kn, gu, mr, ml, or, pa, ta, te). The m...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0, "outputCost": 0, "isFree": true, "originalProvider": "sarva<PERSON>i", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "liquid/lfm-7b", "name": "Liquid: LFM 7B", "description": "LFM-7B, a new best-in-class language model. LFM-7B is designed for exceptional chat capabilities, including languages like Arabic and Japanese. Powered by the Liquid Foundation Model (LFM) architectur...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0.01, "outputCost": 0.01, "isFree": false, "originalProvider": "liquid", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "chatml"}}, {"id": "liquid/lfm-3b", "name": "Liquid: LFM 3B", "description": "Liquid's LFM 3B delivers incredible performance for its size. It positions itself as first place among 3B parameter transformers, hybrids, and RNN models It is also on par with Phi-3.5-mini on multipl...", "contextWindow": 32768, "maxOutput": 8192, "inputCost": 0.02, "outputCost": 0.02, "isFree": false, "originalProvider": "liquid", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "chatml"}}, {"id": "amazon/nova-lite-v1", "name": "Amazon: Nova Lite 1.0", "description": "Amazon Nova Lite 1.0 is a very low-cost multimodal model from Amazon that focused on fast processing of image, video, and text inputs to generate text output. Amazon Nova Lite can handle real-time cus...", "contextWindow": 300000, "maxOutput": 5120, "inputCost": 0.06, "outputCost": 0.24, "isFree": false, "originalProvider": "amazon", "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Nova", "instruct_type": null}}, {"id": "amazon/nova-micro-v1", "name": "Amazon: Nova Micro 1.0", "description": "Amazon Nova Micro 1.0 is a text-only model that delivers the lowest latency responses in the Amazon Nova family of models at a very low cost. With a context length of 128K tokens and optimized for spe...", "contextWindow": 128000, "maxOutput": 5120, "inputCost": 0.035, "outputCost": 0.14, "isFree": false, "originalProvider": "amazon", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Nova", "instruct_type": null}}, {"id": "amazon/nova-pro-v1", "name": "Amazon: Nova Pro 1.0", "description": "Amazon Nova Pro 1.0 is a capable multimodal model from Amazon focused on providing a combination of accuracy, speed, and cost for a wide range of tasks. As of December 2024, it achieves state-of-the-a...", "contextWindow": 300000, "maxOutput": 5120, "inputCost": 0.7999999999999999, "outputCost": 3.1999999999999997, "isFree": false, "originalProvider": "amazon", "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Nova", "instruct_type": null}}, {"id": "inflection/inflection-3-pi", "name": "Inflection: Inflection 3 Pi", "description": "Inflection 3 Pi powers Inflection's [Pi](https://pi.ai) chatbot, including backstory, emotional intelligence, productivity, and safety. It has access to recent news, and excels in scenarios like custo...", "contextWindow": 8000, "maxOutput": 1024, "inputCost": 2.5, "outputCost": 10, "isFree": false, "originalProvider": "inflection", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}, {"id": "inflection/inflection-3-productivity", "name": "Inflection: Inflection 3 Productivity", "description": "Inflection 3 Productivity is optimized for following instructions. It is better for tasks requiring JSON output or precise adherence to provided guidelines. It has access to recent news.  For emotiona...", "contextWindow": 8000, "maxOutput": 1024, "inputCost": 2.5, "outputCost": 10, "isFree": false, "originalProvider": "inflection", "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}}]