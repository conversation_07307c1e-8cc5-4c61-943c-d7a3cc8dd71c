[{"id": "inception/mercury-coder-small-beta", "canonical_slug": "inception/mercury-coder-small-beta", "hugging_face_id": "", "name": "Inception: Mercury Coder Small Beta", "created": 1746033880, "description": "Mercury Coder Small is the first diffusion large language model (dLLM). Applying a breakthrough discrete diffusion approach, the model runs 5-10x faster than even speed optimized models like Claude 3.5 Haiku and GPT-4o Mini while matching their performance. Mercury Coder Small's speed means that developers can stay in the flow while coding, enjoying rapid chat-based iteration and responsive code completion suggestions. On Copilot Arena, Mercury Coder ranks 1st in speed and ties for 2nd in quality. Read more in the [blog post here](https://www.inceptionlabs.ai/introducing-mercury).", "context_length": 32000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.00000025", "completion": "0.000001", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "frequency_penalty", "presence_penalty", "stop"]}, {"id": "tngtech/deepseek-r1t-chimera:free", "canonical_slug": "tngtech/deepseek-r1t-chimera", "hugging_face_id": "tngtech/DeepSeek-R1T-Chimera", "name": "TNG: DeepSeek R1T Chimera (free)", "created": **********, "description": "DeepSeek-R1T-Chimera is created by merging DeepSeek-R1 and DeepSeek-V3 (0324), combining the reasoning capabilities of R1 with the token efficiency improvements of V3. It is based on a DeepSeek-MoE Transformer architecture and is optimized for general text generation tasks.\n\nThe model merges pretrained weights from both source models to balance performance across reasoning, efficiency, and instruction-following tasks. It is released under the MIT license and intended for research and commercial use.", "context_length": 163840, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "shisa-ai/shisa-v2-llama3.3-70b:free", "canonical_slug": "shisa-ai/shisa-v2-llama3.3-70b", "hugging_face_id": "shisa-ai/shisa-v2-llama3.3-70b", "name": "Shisa AI: Shisa V2 Llama 3.3 70B  (free)", "created": **********, "description": "Shisa V2 Llama 3.3 70B is a bilingual Japanese-English chat model fine-tuned by Shisa.AI on Meta’s Llama-3.3-70B-Instruct base. It prioritizes Japanese language performance while retaining strong English capabilities. The model was optimized entirely through post-training, using a refined mix of supervised fine-tuning (SFT) and DPO datasets including regenerated ShareGPT-style data, translation tasks, roleplaying conversations, and instruction-following prompts. Unlike earlier Shisa releases, this version avoids tokenizer modifications or extended pretraining.\n\nShisa V2 70B achieves leading Japanese task performance across a wide range of custom and public benchmarks, including JA MT Bench, ELYZA 100, and Rakuda. It supports a 128K token context length and integrates smoothly with inference frameworks like vLLM and SGLang. While it inherits safety characteristics from its base model, no additional alignment was applied. The model is intended for high-performance bilingual chat, instruction following, and translation tasks across JA/EN.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "alfredpros/codellama-7b-instruct-solidity", "canonical_slug": "alfredpros/codellama-7b-instruct-solidity", "hugging_face_id": "AlfredPros/CodeLlama-7b-Instruct-Solidity", "name": "AlfredPros: CodeLLaMa 7B Instruct Solidity", "created": **********, "description": "A finetuned 7 billion parameters Code LLaMA - Instruct model to generate Solidity smart contract using 4-bit QLoRA finetuning provided by PEFT library.", "context_length": 4096, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "alpaca"}, "pricing": {"prompt": "0.0000008", "completion": "0.0000012", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 4096, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "arliai/qwq-32b-arliai-rpr-v1:free", "canonical_slug": "arliai/qwq-32b-arliai-rpr-v1", "hugging_face_id": "ArliAI/QwQ-32B-ArliAI-RpR-v1", "name": "ArliAI: QwQ 32B RpR v1 (free)", "created": **********, "description": "QwQ-32B-ArliAI-RpR-v1 is a 32B parameter model fine-tuned from Qwen/QwQ-32B using a curated creative writing and roleplay dataset originally developed for the RPMax series. It is designed to maintain coherence and reasoning across long multi-turn conversations by introducing explicit reasoning steps per dialogue turn, generated and refined using the base model itself.\n\nThe model was trained using RS-QLORA+ on 8K sequence lengths and supports up to 128K context windows (with practical performance around 32K). It is optimized for creative roleplay and dialogue generation, with an emphasis on minimizing cross-context repetition while preserving stylistic diversity.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "agentica-org/deepcoder-14b-preview:free", "canonical_slug": "agentica-org/deepcoder-14b-preview", "hugging_face_id": "agentica-org/DeepCoder-14B-Preview", "name": "Agentica: Deepcoder 14B Preview (free)", "created": **********, "description": "DeepCoder-14B-Preview is a 14B parameter code generation model fine-tuned from DeepSeek-R1-Distill-Qwen-14B using reinforcement learning with GRPO+ and iterative context lengthening. It is optimized for long-context program synthesis and achieves strong performance across coding benchmarks, including 60.6% on LiveCodeBench v5, competitive with models like o3-Mini", "context_length": 96000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 96000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "all-hands/openhands-lm-32b-v0.1", "canonical_slug": "all-hands/openhands-lm-32b-v0.1", "hugging_face_id": "all-hands/openhands-lm-32b-v0.1", "name": "OpenHands LM 32B V0.1", "created": **********, "description": "OpenHands LM v0.1 is a 32B open-source coding model fine-tuned from Qwen2.5-Coder-32B-Instruct using reinforcement learning techniques outlined in SWE-Gym. It is optimized for autonomous software development agents and achieves strong performance on SWE-Bench Verified, with a 37.2% resolve rate. The model supports a 128K token context window, making it well-suited for long-horizon code reasoning and large codebase tasks.\n\nOpenHands LM is designed for local deployment and runs on consumer-grade GPUs such as a single 3090. It enables fully offline agent workflows without dependency on proprietary APIs. This release is intended as a research preview, and future updates aim to improve generalizability, reduce repetition, and offer smaller variants.", "context_length": 16384, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000026", "completion": "0.0000034", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 16384, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "thedrummer/skyfall-36b-v2", "canonical_slug": "thedrummer/skyfall-36b-v2", "hugging_face_id": "TheDrummer/Skyfall-36B-v2", "name": "TheDrummer: Skyfall 36B V2", "created": **********, "description": "Skyfall 36B v2 is an enhanced iteration of Mistral Small 2501, specifically fine-tuned for improved creativity, nuanced writing, role-playing, and coherent storytelling.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000008", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k"]}, {"id": "nousresearch/deephermes-3-llama-3-8b-preview:free", "canonical_slug": "nousresearch/deephermes-3-llama-3-8b-preview", "hugging_face_id": "NousResearch/DeepHermes-3-Llama-3-8B-Preview", "name": "Nous: DeepHermes 3 Llama 3 8B Preview (free)", "created": **********, "description": "DeepHermes 3 Preview is the latest version of our flagship Hermes series of LLMs by Nous Research, and one of the first models in the world to unify Reasoning (long chains of thought that improve answer accuracy) and normal LLM response modes into one model. We have also improved LLM annotation, judgement, and function calling.\n\nDeepHermes 3 Preview is one of the first LLM models to unify both \"intuitive\", traditional mode responses and long chain of thought reasoning responses into a single model, toggled by a system prompt.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "cognitivecomputations/dolphin3.0-mistral-24b:free", "canonical_slug": "cognitivecomputations/dolphin3.0-mistral-24b", "hugging_face_id": "cognitivecomputations/Dolphin3.0-Mistral-24B", "name": "Dolphin3.0 Mistral 24B (free)", "created": **********, "description": "Dolphin 3.0 is the next generation of the Dolphin series of instruct-tuned models.  Designed to be the ultimate general purpose local model, enabling coding, math, agentic, function calling, and general use cases.\n\nDolphin aims to be a general purpose instruct model, similar to the models behind <PERSON><PERSON>GP<PERSON>, <PERSON>, <PERSON>. \n\nPart of the [Dolphin 3.0 Collection](https://huggingface.co/collections/cognitivecomputations/dolphin-30-677ab47f73d7ff66743979a3) Curated and trained by [<PERSON>](https://huggingface.co/ehartford), [<PERSON>](https://huggingface.co/bigstorm), [BlouseJury](https://huggingface.co/BlouseJury) and [Cognitive Computations](https://huggingface.co/cognitivecomputations)", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "aion-labs/aion-1.0", "canonical_slug": "aion-labs/aion-1.0", "hugging_face_id": "", "name": "AionLabs: Aion-1.0", "created": **********, "description": "Aion-1.0 is a multi-model system designed for high performance across various tasks, including reasoning and coding. It is built on DeepSeek-R1, augmented with additional models and techniques such as Tree of Thoughts (ToT) and Mixture of Experts (MoE). It is Aion Lab's most powerful reasoning model.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.000004", "completion": "0.000008", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning"]}, {"id": "aion-labs/aion-1.0-mini", "canonical_slug": "aion-labs/aion-1.0-mini", "hugging_face_id": "FuseAI/FuseO1-DeepSeekR1-QwQ-SkyT1-32B-Preview", "name": "AionLabs: Aion-1.0-Mini", "created": **********, "description": "Aion-1.0-Mini 32B parameter model is a distilled version of the DeepSeek-R1 model, designed for strong performance in reasoning domains such as mathematics, coding, and logic. It is a modified variant of a FuseAI model that outperforms R1-Distill-Qwen-32B and R1-Distill-Llama-70B, with benchmark results available on its [Hugging Face page](https://huggingface.co/FuseAI/FuseO1-DeepSeekR1-QwQ-SkyT1-32B-Preview), independently replicated for verification.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000007", "completion": "0.0000014", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning"]}, {"id": "aion-labs/aion-rp-llama-3.1-8b", "canonical_slug": "aion-labs/aion-rp-llama-3.1-8b", "hugging_face_id": "", "name": "AionLabs: Aion-RP 1.0 (8B)", "created": **********, "description": "Aion-RP-Llama-3.1-8B ranks the highest in the character evaluation portion of the RPBench-Auto benchmark, a roleplaying-specific variant of Arena-Hard-Auto, where LLMs evaluate each other’s responses. It is a fine-tuned base model rather than an instruct model, designed to produce more natural and varied writing.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000002", "completion": "0.0000002", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "eva-unit-01/eva-qwen-2.5-72b", "canonical_slug": "eva-unit-01/eva-qwen-2.5-72b", "hugging_face_id": "EVA-UNIT-01/EVA-Qwen2.5-72B-v0.1", "name": "EVA Qwen2.5 72B", "created": **********, "description": "EVA Qwen2.5 72B is a roleplay and storywriting specialist model. It's a full-parameter finetune of Qwen2.5-72B on mixture of synthetic and natural data.\n\nIt uses Celeste 70B 0.1 data mixture, greatly expanding it to improve versatility, creativity and \"flavor\" of the resulting model.", "context_length": 16384, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.000004", "completion": "0.000006", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 16384, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "eva-unit-01/eva-qwen-2.5-32b", "canonical_slug": "eva-unit-01/eva-qwen-2.5-32b", "hugging_face_id": "EVA-UNIT-01/EVA-Qwen2.5-32B-v0.2", "name": "EVA Qwen2.5 32B", "created": **********, "description": "EVA Qwen2.5 32B is a roleplaying/storywriting specialist model. It's a full-parameter finetune of Qwen2.5-32B on mixture of synthetic and natural data.\n\nIt uses Celeste 70B 0.1 data mixture, greatly expanding it to improve versatility, creativity and \"flavor\" of the resulting model.", "context_length": 16384, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0.0000026", "completion": "0.0000034", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 16384, "max_completion_tokens": 4096, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "min_p", "seed"]}, {"id": "deepseek/deepseek-r1-distill-qwen-7b", "canonical_slug": "deepseek/deepseek-r1-distill-qwen-7b", "hugging_face_id": "deepseek-ai/DeepSeek-R1-<PERSON>still-Qwen-7B", "name": "DeepSeek: <PERSON>1 <PERSON><PERSON><PERSON> 7B", "created": **********, "description": "DeepSeek-R1-Distill-Qwen-7B is a 7 billion parameter dense language model distilled from DeepSeek-R1, leveraging reinforcement learning-enhanced reasoning data generated by DeepSeek's larger models. The distillation process transfers advanced reasoning, math, and code capabilities into a smaller, more efficient model architecture based on Qwen2.5-Math-7B. This model demonstrates strong performance across mathematical benchmarks (92.8% pass@1 on MATH-500), coding tasks (Codeforces rating 1189), and general reasoning (49.1% pass@1 on GPQA Diamond), achieving competitive accuracy relative to larger models while maintaining smaller inference costs.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0.0000001", "completion": "0.0000002", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "seed"]}, {"id": "deepseek/deepseek-r1-0528-qwen3-8b:free", "canonical_slug": "deepseek/deepseek-r1-0528-qwen3-8b", "hugging_face_id": "deepseek-ai/deepseek-r1-0528-qwen3-8b", "name": "DeepSeek: Deepseek R1 0528 Qwen3 8B (free)", "created": **********, "description": "DeepSeek-R1-0528 is a lightly upgraded release of DeepSeek R1 that taps more compute and smarter post-training tricks, pushing its reasoning and inference to the brink of flagship models like O3 and Gemini 2.5 Pro.\nIt now tops math, programming, and logic leaderboards, showcasing a step-change in depth-of-thought.\nThe distilled variant, DeepSeek-R1-0528-Qwen3-8B, transfers this chain-of-thought into an 8 B-parameter form, beating standard Qwen3 8B by +10 pp and tying the 235 B “thinking” giant on AIME 2024.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "deepseek/deepseek-r1-0528-qwen3-8b", "canonical_slug": "deepseek/deepseek-r1-0528-qwen3-8b", "hugging_face_id": "deepseek-ai/deepseek-r1-0528-qwen3-8b", "name": "DeepSeek: Deepseek R1 0528 Qwen3 8B", "created": **********, "description": "DeepSeek-R1-0528 is a lightly upgraded release of DeepSeek R1 that taps more compute and smarter post-training tricks, pushing its reasoning and inference to the brink of flagship models like O3 and Gemini 2.5 Pro.\nIt now tops math, programming, and logic leaderboards, showcasing a step-change in depth-of-thought.\nThe distilled variant, DeepSeek-R1-0528-Qwen3-8B, transfers this chain-of-thought into an 8 B-parameter form, beating standard Qwen3 8B by +10 pp and tying the 235 B “thinking” giant on AIME 2024.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0.00000005", "completion": "0.0000001", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 131072, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "stop", "seed", "min_p", "logit_bias"]}, {"id": "deepseek/deepseek-r1-0528:free", "canonical_slug": "deepseek/deepseek-r1-0528", "hugging_face_id": "deepseek-ai/DeepSeek-R1-0528", "name": "DeepSeek: R1 0528 (free)", "created": **********, "description": "May 28th update to the [original DeepSeek R1](/deepseek/deepseek-r1) Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model.", "context_length": 163840, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "deepseek/deepseek-r1-0528", "canonical_slug": "deepseek/deepseek-r1-0528", "hugging_face_id": "deepseek-ai/DeepSeek-R1-0528", "name": "DeepSeek: R1 0528", "created": **********, "description": "May 28th update to the [original DeepSeek R1](/deepseek/deepseek-r1) Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model.", "context_length": 128000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0.0000005", "completion": "0.00000215", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed", "logprobs", "top_logprobs", "tools", "tool_choice", "structured_outputs"]}, {"id": "deepseek/deepseek-prover-v2:free", "canonical_slug": "deepseek/deepseek-prover-v2", "hugging_face_id": "deepseek-ai/DeepSeek-Prover-V2-671B", "name": "DeepSeek: DeepSeek Prover V2 (free)", "created": **********, "description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "context_length": 163840, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "deepseek/deepseek-prover-v2", "canonical_slug": "deepseek/deepseek-prover-v2", "hugging_face_id": "deepseek-ai/DeepSeek-Prover-V2-671B", "name": "DeepSeek: DeepSeek Prover V2", "created": **********, "description": "DeepSeek Prover V2 is a 671B parameter model, speculated to be geared towards logic and mathematics. Likely an upgrade from [DeepSeek-Prover-V1.5](https://huggingface.co/deepseek-ai/DeepSeek-Prover-V1.5-RL) Not much is known about the model yet, as DeepSeek released it on Hugging Face without an announcement or description.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.00000218", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias", "response_format"]}, {"id": "deepseek/deepseek-v3-base:free", "canonical_slug": "deepseek/deepseek-v3-base", "hugging_face_id": "deepseek-ai/Deepseek-v3-base", "name": "DeepSeek: DeepSeek V3 Base (free)", "created": **********, "description": "Note that this is a base model mostly meant for testing, you need to provide detailed prompts for the model to return useful responses. \n\nDeepSeek-V3 Base is a 671B parameter open Mixture-of-Experts (MoE) language model with 37B active parameters per forward pass and a context length of 128K tokens. Trained on 14.8T tokens using FP8 mixed precision, it achieves high training efficiency and stability, with strong performance across language, reasoning, math, and coding tasks. \n\nDeepSeek-V3 Base is the pre-trained model behind [DeepSeek V3](/deepseek/deepseek-chat-v3)", "context_length": 163840, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "deepseek/deepseek-chat-v3-0324:free", "canonical_slug": "deepseek/deepseek-chat-v3-0324", "hugging_face_id": "deepseek-ai/DeepSeek-V3-0324", "name": "DeepSeek: DeepSeek V3 0324 (free)", "created": **********, "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "context_length": 163840, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "top_a"]}, {"id": "deepseek/deepseek-chat-v3-0324", "canonical_slug": "deepseek/deepseek-chat-v3-0324", "hugging_face_id": "deepseek-ai/DeepSeek-V3-0324", "name": "DeepSeek: DeepSeek V3 0324", "created": **********, "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "context_length": 163840, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0.0000003", "completion": "0.00000088", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "logprobs", "top_logprobs", "seed", "min_p"]}, {"id": "mistralai/devstral-small:free", "canonical_slug": "mistralai/devstral-small-2505", "hugging_face_id": "mistralai/Devstral-Small-2505", "name": "Mistral: <PERSON><PERSON><PERSON> (free)", "created": **********, "description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "google/gemma-3n-e4b-it:free", "canonical_slug": "google/gemma-3n-e4b-it", "hugging_face_id": "", "name": "Google: Gemma 3n 4B (free)", "created": **********, "description": "Gemma 3n E4B-it is optimized for efficient execution on mobile and low-resource devices, such as phones, laptops, and tablets. It supports multimodal inputs—including text, visual data, and audio—enabling diverse tasks such as text generation, speech recognition, translation, and image analysis. Leveraging innovations like Per-Layer Embedding (PLE) caching and the MatFormer architecture, Gemma 3n dynamically manages memory usage and computational load by selectively activating model parameters, significantly reducing runtime resource requirements.\n\nThis model supports a wide linguistic range (trained in over 140 languages) and features a flexible 32K token context window. Gemma 3n can selectively load parameters, optimizing memory and computational efficiency based on the task or device capabilities, making it well-suited for privacy-focused, offline-capable applications and on-device AI solutions. [Read more in the blog post](https://developers.googleblog.com/en/introducing-gemma-3n/)", "context_length": 8192, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "response_format"]}, {"id": "meta-llama/llama-3.3-8b-instruct:free", "canonical_slug": "meta-llama/llama-3.3-8b-instruct", "hugging_face_id": "", "name": "Meta: Llama 3.3 8B Instruct (free)", "created": **********, "description": "A lightweight and ultra-fast variant of Llama 3.3 70B, for use when quick response times are needed most.", "context_length": 128000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 4028, "is_moderated": true}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "structured_outputs", "response_format", "repetition_penalty", "top_k"]}, {"id": "microsoft/phi-4-reasoning:free", "canonical_slug": "microsoft/phi-4-reasoning-04-30", "hugging_face_id": "microsoft/Phi-4-reasoning", "name": "Microsoft: Phi 4 Reasoning (free)", "created": **********, "description": "Phi-4-reasoning is a 14B parameter dense decoder-only transformer developed by Microsoft, fine-tuned from Phi-4 to enhance complex reasoning capabilities. It uses a combination of supervised fine-tuning on chain-of-thought traces and reinforcement learning, targeting math, science, and code reasoning tasks. With a 32k context window and high inference efficiency, it is optimized for structured responses in a two-part format: reasoning trace followed by a final solution.\n\nThe model achieves strong results on specialized benchmarks such as AIME, OmniMath, and LiveCodeBench, outperforming many larger models in structured reasoning tasks. It is released under the MIT license and intended for use in latency-constrained, English-only environments requiring reliable step-by-step logic. Recommended usage includes ChatML prompts and structured reasoning format for best results.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "opengvlab/internvl3-14b:free", "canonical_slug": "opengvlab/internvl3-14b", "hugging_face_id": "OpenGVLab/InternVL3-14B", "name": "OpenGVLab: InternVL3 14B (free)", "created": **********, "description": "The 14b version of the InternVL3 series. An advanced multimodal large language model (MLLM) series that demonstrates superior overall performance. Compared to InternVL 2.5, InternVL3 exhibits superior multimodal perception and reasoning capabilities, while further extending its multimodal capabilities to encompass tool usage, GUI agents, industrial image analysis, 3D vision perception, and more.", "context_length": 12288, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 12288, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "opengvlab/internvl3-2b:free", "canonical_slug": "opengvlab/internvl3-2b", "hugging_face_id": "OpenGVLab/InternVL3-2B", "name": "OpenGVLab: InternVL3 2B (free)", "created": **********, "description": "The 2b version of the InternVL3 series, for an even higher inference speed and very reasonable performance. An advanced multimodal large language model (MLLM) series that demonstrates superior overall performance. Compared to InternVL 2.5, InternVL3 exhibits superior multimodal perception and reasoning capabilities, while further extending its multimodal capabilities to encompass tool usage, GUI agents, industrial image analysis, 3D vision perception, and more.", "context_length": 12288, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 12288, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p"]}, {"id": "qwen/qwen3-30b-a3b:free", "canonical_slug": "qwen/qwen3-30b-a3b-04-28", "hugging_face_id": "Qwen/Qwen3-30B-A3B", "name": "Qwen: Qwen3 30B A3B (free)", "created": **********, "description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-8b:free", "canonical_slug": "qwen/qwen3-8b-04-28", "hugging_face_id": "Qwen/Qwen3-8B", "name": "Qwen: <PERSON>wen3 8B (free)", "created": **********, "description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": 40960, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-14b:free", "canonical_slug": "qwen/qwen3-14b-04-28", "hugging_face_id": "Qwen/Qwen3-14B", "name": "Qwen: <PERSON>wen3 14B (free)", "created": **********, "description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen3-32b:free", "canonical_slug": "qwen/qwen3-32b-04-28", "hugging_face_id": "Qwen/Qwen3-32B", "name": "Qwen: <PERSON>wen3 32B (free)", "created": **********, "description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "thudm/glm-z1-32b:free", "canonical_slug": "thudm/glm-z1-32b-0414", "hugging_face_id": "THUDM/GLM-Z1-32B-0414", "name": "THUDM: GLM Z1 32B (free)", "created": **********, "description": "GLM-Z1-32B-0414 is an enhanced reasoning variant of GLM-4-32B, built for deep mathematical, logical, and code-oriented problem solving. It applies extended reinforcement learning—both task-specific and general pairwise preference-based—to improve performance on complex multi-step tasks. Compared to the base GLM-4-32B model, Z1 significantly boosts capabilities in structured reasoning and formal domains.\n\nThe model supports enforced “thinking” steps via prompt engineering and offers improved coherence for long-form outputs. It’s optimized for use in agentic workflows, and includes support for long context (via YaRN), JSON tool calling, and fine-grained sampling configuration for stable inference. Ideal for use cases requiring deliberate, multi-step reasoning or formal derivations.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "thudm/glm-4-32b:free", "canonical_slug": "thudm/glm-4-32b-0414", "hugging_face_id": "THUDM/GLM-4-32B-0414", "name": "THUDM: GLM 4 32B (free)", "created": **********, "description": "GLM-4-32B-0414 is a 32B bilingual (Chinese-English) open-weight language model optimized for code generation, function calling, and agent-style tasks. Pretrained on 15T of high-quality and reasoning-heavy data, it was further refined using human preference alignment, rejection sampling, and reinforcement learning. The model excels in complex reasoning, artifact generation, and structured output tasks, achieving performance comparable to GPT-4o and DeepSeek-V3-0324 across several benchmarks.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1:free", "canonical_slug": "nvidia/llama-3.3-nemotron-super-49b-v1", "hugging_face_id": "nvidia/Llama-3_3-Nemotron-Super-49B-v1", "name": "NVIDIA: Llama 3.3 Nemotron Super 49B v1 (free)", "created": **********, "description": "Llama-3.3-Nemotron-Super-49B-v1 is a large language model (LLM) optimized for advanced reasoning, conversational interactions, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta's Llama-3.3-70B-Instruct, it employs a Neural Architecture Search (NAS) approach, significantly enhancing efficiency and reducing memory requirements. This allows the model to support a context length of up to 128K tokens and fit efficiently on single high-performance GPUs, such as NVIDIA H200.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", "canonical_slug": "nvidia/llama-3.1-nemotron-ultra-253b-v1", "hugging_face_id": "nvidia/Llama-3_1-Nemotron-Ultra-253B-v1", "name": "NVIDIA: Llama 3.1 Nemotron Ultra 253B v1 (free)", "created": **********, "description": "Llama-3.1-Nemotron-Ultra-253B-v1 is a large language model (LLM) optimized for advanced reasoning, human-interactive chat, retrieval-augmented generation (RAG), and tool-calling tasks. Derived from Meta’s Llama-3.1-405B-Instruct, it has been significantly customized using Neural Architecture Search (NAS), resulting in enhanced efficiency, reduced memory usage, and improved inference latency. The model supports a context length of up to 128K tokens and can operate efficiently on an 8x NVIDIA H100 node.\n\nNote: you must include `detailed thinking on` in the system prompt to enable reasoning. Please see [Usage Recommendations](https://huggingface.co/nvidia/Llama-3_1-Nemotron-Ultra-253B-v1#quick-start-and-usage-recommendations) for more.", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "meta-llama/llama-4-maverick:free", "canonical_slug": "meta-llama/llama-4-maverick-17b-128e-instruct", "hugging_face_id": "meta-llama/Llama-4-Maverick-17B-128E-Instruct", "name": "Meta: Llama 4 Maverick (free)", "created": **********, "description": "Llama 4 Maverick 17B Instruct (128E) is a high-capacity multimodal language model from Meta, built on a mixture-of-experts (MoE) architecture with 128 experts and 17 billion active parameters per forward pass (400B total). It supports multilingual text and image input, and produces multilingual text and code output across 12 supported languages. Optimized for vision-language tasks, Maverick is instruction-tuned for assistant-like behavior, image reasoning, and general-purpose multimodal interaction.\n\nMaverick features early fusion for native multimodality and a 1 million token context window. It was trained on a curated mixture of public, licensed, and Meta-platform data, covering ~22 trillion tokens, with a knowledge cutoff in August 2024. Released on April 5, 2025 under the Llama 4 Community License, Maverick is suited for research and commercial applications requiring advanced multimodal understanding and high model throughput.", "context_length": 128000, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Llama4", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs", "tools", "tool_choice"]}, {"id": "arcee-ai/virtuoso-medium-v2", "canonical_slug": "arcee-ai/virtuoso-medium-v2", "hugging_face_id": "arcee-ai/Virtuoso-Medium-v2", "name": "Arcee AI: Virtuoso Medium V2", "created": **********, "description": "Virtuoso‑Medium‑v2 is a 32 B model distilled from DeepSeek‑v3 logits and merged back onto a Qwen 2.5 backbone, yielding a sharper, more factual successor to the original Virtuoso Medium. The team harvested ~1.1 B logit tokens and applied \"fusion‑merging\" plus DPO alignment, which pushed scores past Arcee‑Nova 2024 and many 40 B‑plus peers on MMLU‑Pro, MATH and HumanEval. With a 128 k context and aggressive quantization options (from BF16 down to 4‑bit GGUF), it balances capability with deployability on single‑GPU nodes. Typical use cases include enterprise chat assistants, technical writing aids and medium‑complexity code drafting where Virtuoso‑Large would be overkill. ", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000008", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 32768, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "arcee-ai/arcee-blitz", "canonical_slug": "arcee-ai/arcee-blitz", "hugging_face_id": "arcee-ai/arcee-blitz", "name": "Arcee AI: <PERSON><PERSON>", "created": **********, "description": "Arcee Blitz is a 24 B‑parameter dense model distilled from DeepSeek and built on Mistral architecture for \"everyday\" chat. The distillation‑plus‑refinement pipeline trims compute while keeping DeepSeek‑style reasoning, so Blitz punches above its weight on MMLU, GSM‑8K and BBH compared with other mid‑size open models. With a default 128 k context window and competitive throughput, it serves as a cost‑efficient workhorse for summarization, brainstorming and light code help. Internally, Arcee uses Blitz as the default writer in Conductor pipelines when the heavier Virtuoso line is not required. Users therefore get near‑70 B quality at ~⅓ the latency and price. ", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.00000045", "completion": "0.00000075", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "qwen/qwen3-30b-a3b", "canonical_slug": "qwen/qwen3-30b-a3b-04-28", "hugging_face_id": "Qwen/Qwen3-30B-A3B", "name": "Qwen: Qwen3 30B A3B", "created": **********, "description": "Qwen3, the latest generation in the Qwen large language model series, features both dense and mixture-of-experts (MoE) architectures to excel in reasoning, multilingual support, and advanced agent tasks. Its unique ability to switch seamlessly between a thinking mode for complex reasoning and a non-thinking mode for efficient dialogue ensures versatile, high-quality performance.\n\nSignificantly outperforming prior models like QwQ and Qwen2.5, Qwen3 delivers superior mathematics, coding, commonsense reasoning, creative writing, and interactive dialogue capabilities. The Qwen3-30B-A3B variant includes 30.5 billion parameters (3.3 billion activated), 48 layers, 128 experts (8 activated per task), and supports up to 131K token contexts with YaRN, setting a new standard among open-source models.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0.00000008", "completion": "0.00000029", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": 40960, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "response_format", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "top_k", "seed", "min_p", "structured_outputs", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "qwen/qwen3-8b", "canonical_slug": "qwen/qwen3-8b-04-28", "hugging_face_id": "Qwen/Qwen3-8B", "name": "Qwen: <PERSON>wen3 8B", "created": **********, "description": "Qwen3-8B is a dense 8.2B parameter causal language model from the Qwen3 series, designed for both reasoning-heavy tasks and efficient dialogue. It supports seamless switching between \"thinking\" mode for math, coding, and logical inference, and \"non-thinking\" mode for general conversation. The model is fine-tuned for instruction-following, agent integration, creative writing, and multilingual use across 100+ languages and dialects. It natively supports a 32K token context window and can extend to 131K tokens with YaRN scaling.", "context_length": 128000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0.000000035", "completion": "0.000000138", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 20000, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logit_bias"]}, {"id": "qwen/qwen3-14b", "canonical_slug": "qwen/qwen3-14b-04-28", "hugging_face_id": "Qwen/Qwen3-14B", "name": "Qwen: <PERSON>wen3 14B", "created": **********, "description": "Qwen3-14B is a dense 14.8B parameter causal language model from the Qwen3 series, designed for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, programming, and logical inference, and a \"non-thinking\" mode for general-purpose conversation. The model is fine-tuned for instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0.00000006", "completion": "0.00000024", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": 40960, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "tools", "tool_choice", "stop", "response_format", "seed", "min_p", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "qwen/qwen3-32b", "canonical_slug": "qwen/qwen3-32b-04-28", "hugging_face_id": "Qwen/Qwen3-32B", "name": "Qwen: <PERSON>wen3 32B", "created": **********, "description": "Qwen3-32B is a dense 32.8B parameter causal language model from the Qwen3 series, optimized for both complex reasoning and efficient dialogue. It supports seamless switching between a \"thinking\" mode for tasks like math, coding, and logical inference, and a \"non-thinking\" mode for faster, general-purpose conversation. The model demonstrates strong performance in instruction-following, agent tool use, creative writing, and multilingual tasks across 100+ languages and dialects. It natively handles 32K token contexts and can extend to 131K tokens using YaRN-based scaling. ", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0.0000001", "completion": "0.0000003", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "response_format", "top_logprobs", "logprobs", "logit_bias", "seed", "tools", "tool_choice", "repetition_penalty", "top_k", "min_p", "structured_outputs"]}, {"id": "qwen/qwen3-235b-a22b", "canonical_slug": "qwen/qwen3-235b-a22b-04-28", "hugging_face_id": "Qwen/Qwen3-235B-A22B", "name": "Qwen: Qwen3 235B A22B", "created": **********, "description": "Qwen3-235B-A22B is a 235B parameter mixture-of-experts (MoE) model developed by Qwen, activating 22B parameters per forward pass. It supports seamless switching between a \"thinking\" mode for complex reasoning, math, and code tasks, and a \"non-thinking\" mode for general conversational efficiency. The model demonstrates strong reasoning ability, multilingual support (100+ languages and dialects), advanced instruction-following, and agent tool-calling capabilities. It natively handles a 32K token context window and extends up to 131K tokens using YaRN-based scaling.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Qwen3", "instruct_type": "qwen3"}, "pricing": {"prompt": "0.00000013", "completion": "0.0000006", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": 40960, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "seed", "presence_penalty", "frequency_penalty", "repetition_penalty", "top_k", "tools", "tool_choice", "stop", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs", "min_p"]}, {"id": "qwen/qwen2.5-vl-3b-instruct:free", "canonical_slug": "qwen/qwen2.5-vl-3b-instruct", "hugging_face_id": "Qwen/Qwen2.5-VL-3B-Instruct", "name": "Qwen: Qwen2.5 VL 3B Instruct (free)", "created": **********, "description": "Qwen2.5 VL 3B is a multimodal LLM from the Qwen Team with the following key enhancements:\n\n- SoTA understanding of images of various resolution & ratio: Qwen2.5-VL achieves state-of-the-art performance on visual understanding benchmarks, including MathVista, DocVQA, RealWorldQA, MTVQA, etc.\n\n- Agent that can operate your mobiles, robots, etc.: with the abilities of complex reasoning and decision making, Qwen2.5-VL can be integrated with devices like mobile phones, robots, etc., for automatic operation based on visual environment and text instructions.\n\n- Multilingual Support: to serve global users, besides English and Chinese, Qwen2.5-VL now supports the understanding of texts in different languages inside images, including most European languages, Japanese, Korean, Arabic, Vietnamese, etc.\n\nFor more details, see this [blog post](https://qwenlm.github.io/blog/qwen2-vl/) and [GitHub repo](https://github.com/QwenLM/Qwen2-VL).\n\nUsage of this model is subject to [Tongyi Qianwen LICENSE AGREEMENT](https://huggingface.co/Qwen/Qwen1.5-110B-Chat/blob/main/LICENSE).", "context_length": 64000, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 64000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen2.5-vl-32b-instruct:free", "canonical_slug": "qwen/qwen2.5-vl-32b-instruct", "hugging_face_id": "Qwen/Qwen2.5-VL-32B-Instruct", "name": "Qwen: Qwen2.5 VL 32B Instruct (free)", "created": **********, "description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "context_length": 8192, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty", "stop", "frequency_penalty", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "qwen/qwen2.5-vl-32b-instruct", "canonical_slug": "qwen/qwen2.5-vl-32b-instruct", "hugging_face_id": "Qwen/Qwen2.5-VL-32B-Instruct", "name": "Qwen: Qwen2.5 VL 32B Instruct", "created": **********, "description": "Qwen2.5-VL-32B is a multimodal vision-language model fine-tuned through reinforcement learning for enhanced mathematical reasoning, structured outputs, and visual problem-solving capabilities. It excels at visual analysis tasks, including object recognition, textual interpretation within images, and precise event localization in extended videos. Qwen2.5-VL-32B demonstrates state-of-the-art performance across multimodal benchmarks such as MMMU, MathVista, and VideoMME, while maintaining strong reasoning and clarity in text-based tasks like MMLU, mathematical problem-solving, and code generation.", "context_length": 128000, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.0000009", "completion": "0.0000009", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "response_format", "structured_outputs", "logit_bias", "logprobs", "top_logprobs"]}, {"id": "qwen/qwen-vl-plus", "canonical_slug": "qwen/qwen-vl-plus", "hugging_face_id": "", "name": "Qwen: Qwen VL Plus", "created": **********, "description": "Qwen's Enhanced Large Visual Language Model. Significantly upgraded for detailed recognition capabilities and text recognition abilities, supporting ultra-high pixel resolutions up to millions of pixels and extreme aspect ratios for image input. It delivers significant performance across a broad range of visual tasks.\n", "context_length": 7500, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.00000021", "completion": "0.00000063", "request": "0", "image": "0.0002688", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 7500, "max_completion_tokens": 1500, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "qwen/qwen-turbo", "canonical_slug": "qwen/qwen-turbo-2024-11-01", "hugging_face_id": "", "name": "Qwen: <PERSON><PERSON>-<PERSON>", "created": **********, "description": "Qwen-Turbo, based on Qwen2.5, is a 1M context model that provides fast speed and low cost, suitable for simple tasks.", "context_length": 1000000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.00000005", "completion": "0.0000002", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.00000002"}, "top_provider": {"context_length": 1000000, "max_completion_tokens": 8192, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty"]}, {"id": "nvidia/llama-3.1-nemotron-70b-instruct", "canonical_slug": "nvidia/llama-3.1-nemotron-70b-instruct", "hugging_face_id": "nvidia/Llama-3.1-Nemotron-70B-Instruct-HF", "name": "NVIDIA: Llama 3.1 Nemotron 70B Instruct", "created": **********, "description": "NVIDIA's Llama 3.1 Nemotron 70B is a language model designed for generating precise and useful responses. Leveraging [Llama 3.1 70B](/models/meta-llama/llama-3.1-70b-instruct) architecture and Reinforcement Learning from Human Feedback (RLHF), it excels in automatic alignment benchmarks. This model is tailored for applications requiring high accuracy in helpfulness and response generation, suitable for diverse user queries across multiple domains.\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.00000012", "completion": "0.0000003", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 131072, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logit_bias", "logprobs", "top_logprobs", "response_format", "min_p", "repetition_penalty", "top_k"]}, {"id": "qwen/qwen2.5-vl-72b-instruct:free", "canonical_slug": "qwen/qwen2.5-vl-72b-instruct", "hugging_face_id": "Qwen/Qwen2.5-VL-72B-Instruct", "name": "Qwen: Qwen2.5 VL 72B Instruct (free)", "created": **********, "description": "Qwen2.5-VL is proficient in recognizing common objects such as flowers, birds, fish, and insects. It is also highly capable of analyzing texts, charts, icons, graphics, and layouts within images.", "context_length": 131072, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "seed", "response_format", "presence_penalty", "stop", "frequency_penalty", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "x-ai/grok-2-vision-1212", "canonical_slug": "x-ai/grok-2-vision-1212", "hugging_face_id": "", "name": "xAI: Grok 2 Vision 1212", "created": **********, "description": "Grok 2 Vision 1212 advances image-based AI with stronger visual comprehension, refined instruction-following, and multilingual support. From object recognition to style analysis, it empowers developers to build more intuitive, visually aware applications. Its enhanced steerability and reasoning establish a robust foundation for next-generation image solutions.\n\nTo read more about this model, check out [xAI's announcement](https://x.ai/blog/grok-1212).", "context_length": 32768, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Grok", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.00001", "request": "0", "image": "0.0036", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "x-ai/grok-vision-beta", "canonical_slug": "x-ai/grok-vision-beta", "hugging_face_id": "", "name": "xAI: Grok Vision Beta", "created": **********, "description": "Grok Vision Beta is xAI's experimental language model with vision capability.\n\n", "context_length": 8192, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Grok", "instruct_type": null}, "pricing": {"prompt": "0.000005", "completion": "0.000015", "request": "0", "image": "0.009", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 8192, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "meta-llama/llama-3.2-90b-vision-instruct", "canonical_slug": "meta-llama/llama-3.2-90b-vision-instruct", "hugging_face_id": "meta-llama/Llama-3.2-90B-Vision-Instruct", "name": "Meta: Llama 3.2 90B Vision Instruct", "created": **********, "description": "The Llama 90B Vision model is a top-tier, 90-billion-parameter multimodal model designed for the most challenging visual reasoning and language tasks. It offers unparalleled accuracy in image captioning, visual question answering, and advanced image-text comprehension. Pre-trained on vast multimodal datasets and fine-tuned with human feedback, the Llama 90B Vision is engineered to handle the most demanding image-based AI tasks.\n\nThis model is perfect for industries requiring cutting-edge multimodal AI capabilities, particularly those dealing with complex, real-time visual and textual analysis.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0.0000012", "completion": "0.0000012", "request": "0", "image": "0.001734", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format", "seed"]}, {"id": "meta-llama/llama-3.2-11b-vision-instruct:free", "canonical_slug": "meta-llama/llama-3.2-11b-vision-instruct", "hugging_face_id": "meta-llama/Llama-3.2-11B-Vision-Instruct", "name": "Meta: Llama 3.2 11B Vision Instruct (free)", "created": **********, "description": "Llama 3.2 11B Vision is a multimodal model with 11 billion parameters, designed to handle tasks combining visual and textual data. It excels in tasks such as image captioning and visual question answering, bridging the gap between language generation and visual reasoning. Pre-trained on a massive dataset of image-text pairs, it performs well in complex, high-accuracy image analysis.\n\nIts ability to integrate visual understanding with language processing makes it an ideal solution for industries requiring comprehensive visual-linguistic AI applications, such as content creation, AI-driven customer service, and research.\n\nClick here for the [original model card](https://github.com/meta-llama/llama-models/blob/main/models/llama3_2/MODEL_CARD_VISION.md).\n\nUsage of this model is subject to [Meta's Acceptable Use Policy](https://www.llama.com/llama3/use-policy/).", "context_length": 131072, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image"], "output_modalities": ["text"], "tokenizer": "Llama3", "instruct_type": "llama3"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": 2048, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "top_k", "repetition_penalty", "logit_bias", "min_p", "response_format"]}, {"id": "mistralai/devstral-small", "canonical_slug": "mistralai/devstral-small-2505", "hugging_face_id": "mistralai/Devstral-Small-2505", "name": "Mistral: <PERSON><PERSON><PERSON> Small", "created": **********, "description": "Devstral-Small-2505 is a 24B parameter agentic LLM fine-tuned from Mistral-Small-3.1, jointly developed by Mistral AI and All Hands AI for advanced software engineering tasks. It is optimized for codebase exploration, multi-file editing, and integration into coding agents, achieving state-of-the-art results on SWE-Bench Verified (46.8%).\n\nDevstral supports a 128k context window and uses a custom Tekken tokenizer. It is text-only, with the vision encoder removed, and is suitable for local deployment on high-end consumer hardware (e.g., RTX 4090, 32GB RAM Macs). Devstral is best used in agentic workflows via the OpenHands scaffold and is compatible with inference frameworks like vLLM, Transformers, and Ollama. It is released under the Apache 2.0 license.", "context_length": 128000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": null}, "pricing": {"prompt": "0.00000006", "completion": "0.00000012", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "repetition_penalty", "response_format", "top_k", "seed", "min_p", "tools", "tool_choice", "structured_outputs"]}, {"id": "openai/codex-mini", "canonical_slug": "openai/codex-mini", "hugging_face_id": "", "name": "OpenAI: Codex Mini", "created": **********, "description": "codex-mini-latest is a fine-tuned version of o4-mini specifically for use in Codex CLI. For direct use in the API, we recommend starting with gpt-4.1.", "context_length": 200000, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text"], "output_modalities": ["text"], "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.0000015", "completion": "0.000006", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.000000375"}, "top_provider": {"context_length": 200000, "max_completion_tokens": 100000, "is_moderated": true}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "seed", "max_tokens", "response_format", "structured_outputs"]}, {"id": "open-r1/olympiccoder-32b:free", "canonical_slug": "open-r1/olympiccoder-32b", "hugging_face_id": "open-r1/OlympicCoder-32B", "name": "OlympicCoder 32B (free)", "created": **********, "description": "OlympicCoder-32B is a high-performing open-source model fine-tuned using the CodeForces-CoTs dataset, containing approximately 100,000 chain-of-thought programming samples. It excels at complex competitive programming benchmarks, such as IOI 2024 and Codeforces-style challenges, frequently surpassing state-of-the-art closed-source models. OlympicCoder-32B provides advanced reasoning, coherent multi-step problem-solving, and robust code generation capabilities, demonstrating significant potential for olympiad-level competitive programming applications.", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Other", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/codestral-2501", "canonical_slug": "mistralai/codestral-2501", "hugging_face_id": "", "name": "Mistral: Codestral 2501", "created": **********, "description": "[Mistral](/mistralai)'s cutting-edge language model for coding. Codestral specializes in low-latency, high-frequency tasks such as fill-in-the-middle (FIM), code correction and test generation. \n\nLearn more on their blog post: https://mistral.ai/news/codestral-2501/", "context_length": 262144, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.0000003", "completion": "0.0000009", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 262144, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "response_format", "structured_outputs", "seed"]}, {"id": "qwen/qwen-2.5-coder-32b-instruct:free", "canonical_slug": "qwen/qwen-2.5-coder-32b-instruct", "hugging_face_id": "Qwen/Qwen2.5-Coder-32B-Instruct", "name": "Qwen2.5 Coder 32B Instruct (free)", "created": **********, "description": "Qwen2.5-Coder is the latest series of Code-Specific Qwen large language models (formerly known as CodeQwen). Qwen2.5-Coder brings the following improvements upon CodeQwen1.5:\n\n- Significantly improvements in **code generation**, **code reasoning** and **code fixing**. \n- A more comprehensive foundation for real-world applications such as **Code Agents**. Not only enhancing coding capabilities but also maintaining its strengths in mathematics and general competencies.\n\nTo read more about its evaluation results, check out [<PERSON>wen 2.5 Coder's blog](https://qwenlm.github.io/blog/qwen2.5-coder-family/).", "context_length": 32768, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON>", "instruct_type": "chatml"}, "pricing": {"prompt": "0", "completion": "0", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "top_k", "min_p", "repetition_penalty", "logprobs", "logit_bias", "top_logprobs"]}, {"id": "mistralai/magistral-small-2506", "canonical_slug": "mistralai/magistral-small-2506", "hugging_face_id": "mistralai/Magistral-Small-2506", "name": "Mistral: Magistral Small 2506", "created": **********, "description": "Magistral Small is a 24B parameter instruction-tuned model based on Mistral-Small-3.1 (2503), enhanced through supervised fine-tuning on traces from Magistral Medium and further refined via reinforcement learning. It is optimized for reasoning and supports a wide multilingual range, including over 20 languages.", "context_length": 40000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.0000005", "completion": "0.0000015", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40000, "max_completion_tokens": 40000, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "mistralai/magistral-medium-2506", "canonical_slug": "mistralai/magistral-medium-2506", "hugging_face_id": "", "name": "Mistral: Magistral Medium 2506", "created": **********, "description": "Magistral is Mistral's first reasoning model. It is ideal for general purpose use requiring longer thought processing and better accuracy than with non-reasoning LLMs. From legal research and financial forecasting to software development and creative storytelling — this model solves multi-step challenges where transparency and precision are critical.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.000005", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": 40000, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "mistralai/magistral-medium-2506:thinking", "canonical_slug": "mistralai/magistral-medium-2506", "hugging_face_id": "", "name": "Mistral: Magistral Medium 2506 (thinking)", "created": **********, "description": "Magistral is Mistral's first reasoning model. It is ideal for general purpose use requiring longer thought processing and better accuracy than with non-reasoning LLMs. From legal research and financial forecasting to software development and creative storytelling — this model solves multi-step challenges where transparency and precision are critical.", "context_length": 40960, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "<PERSON><PERSON><PERSON>", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.000005", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 40960, "max_completion_tokens": 40000, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "google/gemini-2.5-pro-preview", "canonical_slug": "google/gemini-2.5-pro-preview-06-05", "hugging_face_id": "", "name": "Google: Gemini 2.5 Pro Preview 06-05", "created": **********, "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.\n", "context_length": 1048576, "architecture": {"modality": "text+image->text", "input_modalities": ["file", "image", "text"], "output_modalities": ["text"], "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.00000125", "completion": "0.00001", "request": "0", "image": "0.00516", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.00000031", "input_cache_write": "0.000001625"}, "top_provider": {"context_length": 1048576, "max_completion_tokens": 65536, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "google/gemini-2.5-flash-preview-05-20", "canonical_slug": "google/gemini-2.5-flash-preview-05-20", "hugging_face_id": "", "name": "Google: Gemini 2.5 Flash Preview 05-20", "created": **********, "description": "Gemini 2.5 Flash May 20th Checkpoint is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "request": "0", "image": "0.0006192", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.**********", "input_cache_write": "0.**********"}, "top_provider": {"context_length": 1048576, "max_completion_tokens": 65535, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "google/gemini-2.5-flash-preview-05-20:thinking", "canonical_slug": "google/gemini-2.5-flash-preview-05-20", "hugging_face_id": "", "name": "Google: Gemini 2.5 Flash Preview 05-20 (thinking)", "created": **********, "description": "Gemini 2.5 Flash May 20th Checkpoint is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000035", "request": "0", "image": "0.0006192", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.**********", "input_cache_write": "0.**********"}, "top_provider": {"context_length": 1048576, "max_completion_tokens": 65535, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "structured_outputs", "response_format", "stop", "frequency_penalty", "presence_penalty", "seed"]}, {"id": "google/gemini-2.5-pro-preview-05-06", "canonical_slug": "google/gemini-2.5-pro-preview-03-25", "hugging_face_id": "", "name": "Google: Gemini 2.5 Pro Preview 05-06", "created": **********, "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "context_length": 1048576, "architecture": {"modality": "text+image->text", "input_modalities": ["text", "image", "file"], "output_modalities": ["text"], "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.00000125", "completion": "0.00001", "request": "0", "image": "0.00516", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.00000031", "input_cache_write": "0.000001625"}, "top_provider": {"context_length": 1048576, "max_completion_tokens": 65535, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "seed", "response_format", "structured_outputs"]}, {"id": "google/gemini-2.5-flash-preview", "canonical_slug": "google/gemini-2.5-flash-preview-04-17", "hugging_face_id": "", "name": "Google: Gemini 2.5 Flash Preview 04-17", "created": **********, "description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "request": "0", "image": "0.0006192", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.**********", "input_cache_write": "0.**********"}, "top_provider": {"context_length": 1048576, "max_completion_tokens": 65535, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "response_format", "structured_outputs"]}, {"id": "google/gemini-2.5-flash-preview:thinking", "canonical_slug": "google/gemini-2.5-flash-preview-04-17", "hugging_face_id": "", "name": "Google: Gemini 2.5 Flash Preview 04-17 (thinking)", "created": **********, "description": "Gemini 2.5 Flash is Google's state-of-the-art workhorse model, specifically designed for advanced reasoning, coding, mathematics, and scientific tasks. It includes built-in \"thinking\" capabilities, enabling it to provide responses with greater accuracy and nuanced context handling. \n\nNote: This model is available in two variants: thinking and non-thinking. The output pricing varies significantly depending on whether the thinking capability is active. If you select the standard variant (without the \":thinking\" suffix), the model will explicitly avoid generating thinking tokens. \n\nTo utilize the thinking capability and receive thinking tokens, you must choose the \":thinking\" variant, which will then incur the higher thinking-output pricing. \n\nAdditionally, Gemini 2.5 Flash is configurable through the \"max tokens for reasoning\" parameter, as described in the documentation (https://openrouter.ai/docs/use-cases/reasoning-tokens#max-tokens-for-reasoning).", "context_length": 1048576, "architecture": {"modality": "text+image->text", "input_modalities": ["image", "text", "file"], "output_modalities": ["text"], "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000035", "request": "0", "image": "0.0006192", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.**********", "input_cache_write": "0.**********"}, "top_provider": {"context_length": 1048576, "max_completion_tokens": 65535, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["max_tokens", "temperature", "top_p", "tools", "tool_choice", "stop", "response_format", "structured_outputs"]}, {"id": "x-ai/grok-3-mini-beta", "canonical_slug": "x-ai/grok-3-mini-beta", "hugging_face_id": "", "name": "xAI: Grok 3 Mini Beta", "created": **********, "description": "Grok 3 Mini is a lightweight, smaller thinking model. Unlike traditional models that generate answers immediately, Grok 3 Mini thinks before responding. It’s ideal for reasoning-heavy tasks that don’t demand extensive domain knowledge, and shines in math-specific and quantitative use cases, such as solving challenging puzzles or math problems.\n\nTransparent \"thinking\" traces accessible. Defaults to low reasoning, can boost with setting `reasoning: { effort: \"high\" }`\n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Grok", "instruct_type": null}, "pricing": {"prompt": "0.0000003", "completion": "0.0000005", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.000000075"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "reasoning", "include_reasoning", "stop", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "x-ai/grok-3-beta", "canonical_slug": "x-ai/grok-3-beta", "hugging_face_id": "", "name": "xAI: Grok 3 Beta", "created": **********, "description": "Grok 3 is the latest model from xAI. It's their flagship model that excels at enterprise use cases like data extraction, coding, and text summarization. Possesses deep domain knowledge in finance, healthcare, law, and science.\n\nExcels in structured tasks and benchmarks like GPQA, LCB, and MMLU-Pro where it outperforms Grok 3 Mini even on high thinking. \n\nNote: That there are two xAI endpoints for this model. By default when using this model we will always route you to the base endpoint. If you want the fast endpoint you can add `provider: { sort: throughput}`, to sort by throughput instead. \n", "context_length": 131072, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "Grok", "instruct_type": null}, "pricing": {"prompt": "0.000003", "completion": "0.000015", "request": "0", "image": "0", "web_search": "0", "internal_reasoning": "0", "input_cache_read": "0.00000075"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null, "supported_parameters": ["tools", "tool_choice", "max_tokens", "temperature", "top_p", "stop", "frequency_penalty", "presence_penalty", "seed", "logprobs", "top_logprobs", "response_format"]}, {"id": "openai/gpt-4o-mini-search-preview", "canonical_slug": "openai/gpt-4o-mini-search-preview-2025-03-11", "hugging_face_id": "", "name": "OpenAI: GPT-4o-mini Search Preview", "created": **********, "description": "GPT-4o mini Search Preview is a specialized model for web search in Chat Completions. It is trained to understand and execute web search queries.", "context_length": 128000, "architecture": {"modality": "text->text", "input_modalities": ["text"], "output_modalities": ["text"], "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.00000015", "completion": "0.0000006", "request": "0.0275", "image": "0.000217", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 16384, "is_moderated": true}, "per_request_limits": null, "supported_parameters": ["web_search_options", "max_tokens", "response_format", "structured_outputs"]}]