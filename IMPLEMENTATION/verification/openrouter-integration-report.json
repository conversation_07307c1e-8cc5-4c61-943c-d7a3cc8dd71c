{"summary": {"totalModels": 75, "byProvider": {"inception": 1, "tngtech": 1, "shisa-ai": 1, "alfredpros": 1, "arliai": 1, "agentica-org": 1, "all-hands": 1, "thedrummer": 1, "nousresearch": 1, "cognitivecomputations": 1, "aion-labs": 3, "eva-unit-01": 2, "deepseek": 10, "mistralai": 6, "google": 7, "meta-llama": 4, "microsoft": 1, "opengvlab": 2, "qwen": 16, "thudm": 2, "nvidia": 3, "arcee-ai": 2, "x-ai": 4, "openai": 2, "open-r1": 1}, "byTier": {"basic": 22, "free": 32, "pro": 12, "enterprise": 9}}, "categories": {"freeModels": ["tngtech/deepseek-r1t-chimera:free", "shisa-ai/shisa-v2-llama3.3-70b:free", "arliai/qwq-32b-arliai-rpr-v1:free", "agentica-org/deepcoder-14b-preview:free", "nousresearch/deephermes-3-llama-3-8b-preview:free", "cognitivecomputations/dolphin3.0-mistral-24b:free", "deepseek/deepseek-r1-0528-qwen3-8b:free", "deepseek/deepseek-r1-0528:free", "deepseek/deepseek-prover-v2:free", "deepseek/deepseek-v3-base:free", "deepseek/deepseek-chat-v3-0324:free", "mistralai/devstral-small:free", "google/gemma-3n-e4b-it:free", "meta-llama/llama-3.3-8b-instruct:free", "microsoft/phi-4-reasoning:free", "opengvlab/internvl3-14b:free", "opengvlab/internvl3-2b:free", "qwen/qwen3-30b-a3b:free", "qwen/qwen3-8b:free", "qwen/qwen3-14b:free", "qwen/qwen3-32b:free", "thudm/glm-z1-32b:free", "thudm/glm-4-32b:free", "nvidia/llama-3.3-nemotron-super-49b-v1:free", "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", "meta-llama/llama-4-maverick:free", "qwen/qwen2.5-vl-3b-instruct:free", "qwen/qwen2.5-vl-32b-instruct:free", "qwen/qwen2.5-vl-72b-instruct:free", "meta-llama/llama-3.2-11b-vision-instruct:free", "open-r1/olympiccoder-32b:free", "qwen/qwen-2.5-coder-32b-instruct:free"], "exclusiveModels": ["inception/mercury-coder-small-beta", "tngtech/deepseek-r1t-chimera:free", "shisa-ai/shisa-v2-llama3.3-70b:free", "alfredpros/codellama-7b-instruct-solidity", "arliai/qwq-32b-arliai-rpr-v1:free", "agentica-org/deepcoder-14b-preview:free", "all-hands/openhands-lm-32b-v0.1", "thedrummer/skyfall-36b-v2", "nousresearch/deephermes-3-llama-3-8b-preview:free", "cognitivecomputations/dolphin3.0-mistral-24b:free", "aion-labs/aion-1.0", "aion-labs/aion-1.0-mini", "aion-labs/aion-rp-llama-3.1-8b", "eva-unit-01/eva-qwen-2.5-72b", "eva-unit-01/eva-qwen-2.5-32b"], "reasoningModels": ["tngtech/deepseek-r1t-chimera:free", "arliai/qwq-32b-arliai-rpr-v1:free", "all-hands/openhands-lm-32b-v0.1", "nousresearch/deephermes-3-llama-3-8b-preview:free", "aion-labs/aion-1.0", "aion-labs/aion-1.0-mini", "deepseek/deepseek-r1-distill-qwen-7b", "deepseek/deepseek-r1-0528-qwen3-8b:free", "deepseek/deepseek-r1-0528-qwen3-8b", "deepseek/deepseek-r1-0528:free", "deepseek/deepseek-r1-0528", "deepseek/deepseek-v3-base:free", "microsoft/phi-4-reasoning:free", "opengvlab/internvl3-14b:free", "opengvlab/internvl3-2b:free", "qwen/qwen3-30b-a3b:free", "qwen/qwen3-8b:free", "qwen/qwen3-14b:free", "qwen/qwen3-32b:free", "thudm/glm-z1-32b:free", "thudm/glm-4-32b:free", "nvidia/llama-3.3-nemotron-super-49b-v1:free", "nvidia/llama-3.1-nemotron-ultra-253b-v1:free", "meta-llama/llama-4-maverick:free", "arcee-ai/arcee-blitz", "qwen/qwen3-30b-a3b", "qwen/qwen3-8b", "qwen/qwen3-14b", "qwen/qwen3-32b", "qwen/qwen3-235b-a22b", "qwen/qwen2.5-vl-3b-instruct:free", "qwen/qwen2.5-vl-32b-instruct:free", "qwen/qwen2.5-vl-32b-instruct", "x-ai/grok-2-vision-1212", "meta-llama/llama-3.2-90b-vision-instruct", "meta-llama/llama-3.2-11b-vision-instruct:free", "open-r1/olympiccoder-32b:free", "qwen/qwen-2.5-coder-32b-instruct:free", "mistralai/magistral-small-2506", "mistralai/magistral-medium-2506", "mistralai/magistral-medium-2506:thinking", "google/gemini-2.5-pro-preview", "google/gemini-2.5-flash-preview-05-20", "google/gemini-2.5-flash-preview-05-20:thinking", "google/gemini-2.5-pro-preview-05-06", "google/gemini-2.5-flash-preview", "google/gemini-2.5-flash-preview:thinking", "x-ai/grok-3-mini-beta"], "visionModels": ["mistralai/devstral-small:free", "opengvlab/internvl3-14b:free", "opengvlab/internvl3-2b:free", "meta-llama/llama-4-maverick:free", "qwen/qwen2.5-vl-3b-instruct:free", "qwen/qwen2.5-vl-32b-instruct:free", "qwen/qwen2.5-vl-32b-instruct", "qwen/qwen-vl-plus", "qwen/qwen2.5-vl-72b-instruct:free", "x-ai/grok-2-vision-1212", "x-ai/grok-vision-beta", "meta-llama/llama-3.2-90b-vision-instruct", "meta-llama/llama-3.2-11b-vision-instruct:free", "mistralai/devstral-small"], "codingModels": ["inception/mercury-coder-small-beta", "alfredpros/codellama-7b-instruct-solidity", "agentica-org/deepcoder-14b-preview:free", "all-hands/openhands-lm-32b-v0.1", "deepseek/deepseek-r1-distill-qwen-7b", "mistralai/devstral-small:free", "microsoft/phi-4-reasoning:free", "thudm/glm-z1-32b:free", "thudm/glm-4-32b:free", "meta-llama/llama-4-maverick:free", "arcee-ai/virtuoso-medium-v2", "arcee-ai/arcee-blitz", "qwen/qwen3-235b-a22b", "qwen/qwen2.5-vl-32b-instruct:free", "qwen/qwen2.5-vl-32b-instruct", "mistralai/devstral-small", "openai/codex-mini", "open-r1/olympiccoder-32b:free", "mistralai/codestral-2501", "qwen/qwen-2.5-coder-32b-instruct:free"]}}