const fs = require('fs');

// Read OpenRouter catalog
const catalog = JSON.parse(fs.readFileSync('/home/<USER>/justsimplechat/IMPLEMENTATION/verification/openrouter-models.json', 'utf8'));

// Read existing models
const registryContent = fs.readFileSync('/home/<USER>/justsimplechat/src/lib/ai/models/registry.ts', 'utf8');
const existingModels = new Set();

// Extract existing model IDs
const idMatches = registryContent.match(/id: '([^']+)'/g) || [];
idMatches.forEach(match => {
  const id = match.match(/id: '([^']+)'/)[1];
  existingModels.add(id);
  // Also add without openrouter prefix if it exists
  if (id.startsWith('openrouter/')) {
    existingModels.add(id.substring('openrouter/'.length));
  }
});

// Categorize OpenRouter models
const categories = {
  exclusive: [],
  deepseek: [],
  arcee: [],
  qwen: [],
  nvidia: [],
  free: [],
  reasoning: [],
  vision: [],
  coding: [],
  community: [],
  latest: []
};

// Community providers
const communityProviders = [
  'sentientagi', 'thedrummer', 'sao10k', 'eva-unit-01', 'undi95',
  'nousresearch', 'cognitivecomputations', 'alpindale', 'anthracite-org',
  'aion-labs', 'neversleep', 'shisa-ai', 'arliai', 'agentica-org',
  'all-hands', 'tianqiu', 'tngtech', 'inception', 'alfredpros'
];

catalog.data.forEach(model => {
  const id = model.id;
  const provider = id.split('/')[0];
  
  // Skip if already exists (check both with and without openrouter prefix)
  if (existingModels.has(id) || existingModels.has(`openrouter/${id}`)) {
    return;
  }
  
  // Categorize
  if (communityProviders.includes(provider)) {
    categories.exclusive.push(model);
    categories.community.push(model);
  }
  
  if (provider === 'deepseek') {
    categories.deepseek.push(model);
  }
  
  if (provider === 'arcee-ai') {
    categories.arcee.push(model);
  }
  
  if (provider === 'qwen') {
    categories.qwen.push(model);
  }
  
  if (provider === 'nvidia') {
    categories.nvidia.push(model);
  }
  
  if (id.includes(':free')) {
    categories.free.push(model);
  }
  
  if (id.includes('reasoning') || id.includes('r1') || id.includes('thinking') || id.includes('rumination')) {
    categories.reasoning.push(model);
  }
  
  if (id.includes('vision') || id.includes('vl') || id.includes('internvl')) {
    categories.vision.push(model);
  }
  
  if (id.includes('code') || id.includes('coder') || id.includes('devstral')) {
    categories.coding.push(model);
  }
  
  // Latest models (2025 or recent versions)
  if (id.includes('2506') || id.includes('v2') || id.includes('preview') || id.includes('beta')) {
    categories.latest.push(model);
  }
});

// Print summary
console.log('OpenRouter Unique Models Analysis');
console.log('=================================');
console.log(`Total models in catalog: ${catalog.data.length}`);
console.log(`Existing models: ${existingModels.size}`);
console.log('\nUnique models by category:');
Object.entries(categories).forEach(([cat, models]) => {
  console.log(`${cat}: ${models.length}`);
});

// Select top models for implementation
const selected = new Map();

// Priority 1: Exclusive community models (top 15)
categories.exclusive.slice(0, 15).forEach(m => selected.set(m.id, m));

// Priority 2: DeepSeek reasoning models (top 10)
categories.deepseek.filter(m => !selected.has(m.id)).slice(0, 10).forEach(m => selected.set(m.id, m));

// Priority 3: Free models (top 15)
categories.free.filter(m => !selected.has(m.id)).slice(0, 15).forEach(m => selected.set(m.id, m));

// Priority 4: Arcee AI models (top 5)
categories.arcee.filter(m => !selected.has(m.id)).slice(0, 5).forEach(m => selected.set(m.id, m));

// Priority 5: Qwen models (top 10)
categories.qwen.filter(m => !selected.has(m.id)).slice(0, 10).forEach(m => selected.set(m.id, m));

// Priority 6: NVIDIA models (top 5)
categories.nvidia.filter(m => !selected.has(m.id)).slice(0, 5).forEach(m => selected.set(m.id, m));

// Priority 7: Vision models (top 5)
categories.vision.filter(m => !selected.has(m.id)).slice(0, 5).forEach(m => selected.set(m.id, m));

// Priority 8: Coding models (top 5)
categories.coding.filter(m => !selected.has(m.id)).slice(0, 5).forEach(m => selected.set(m.id, m));

// Priority 9: Latest/preview models (fill to 75)
const remaining = 75 - selected.size;
categories.latest.filter(m => !selected.has(m.id)).slice(0, remaining).forEach(m => selected.set(m.id, m));

console.log(`\nSelected ${selected.size} models for implementation`);

// Save selected models
fs.writeFileSync('/home/<USER>/justsimplechat/IMPLEMENTATION/verification/selected-openrouter-models.json', 
  JSON.stringify(Array.from(selected.values()), null, 2));

// Print selection summary by provider
const providerCount = {};
selected.forEach(m => {
  const provider = m.id.split('/')[0];
  providerCount[provider] = (providerCount[provider] || 0) + 1;
});

console.log('\nSelected models by provider:');
Object.entries(providerCount).sort((a, b) => b[1] - a[1]).forEach(([provider, count]) => {
  console.log(`${provider}: ${count}`);
});