const fs = require('fs');

// Read selected models
const models = JSON.parse(fs.readFileSync('/home/<USER>/justsimplechat/IMPLEMENTATION/verification/selected-openrouter-models.json', 'utf8'));

// Analyze by category
const analysis = {
  byProvider: {},
  byTier: {},
  freeModels: [],
  exclusiveModels: [],
  reasoningModels: [],
  visionModels: [],
  codingModels: [],
  communityModels: []
};

// Community providers
const communityProviders = [
  'sentientagi', 'thedrummer', 'sao10k', 'eva-unit-01', 'undi95',
  'nousresearch', 'cognitivecomputations', 'alpindale', 'anthracite-org',
  'aion-labs', 'neversleep', 'shisa-ai', 'arliai', 'agentica-org',
  'all-hands', 'tianqiu', 'tngtech', 'inception', 'alfredpros'
];

models.forEach(model => {
  const provider = model.id.split('/')[0];
  
  // Count by provider
  analysis.byProvider[provider] = (analysis.byProvider[provider] || 0) + 1;
  
  // Count by tier
  const cost = (model.pricing?.prompt || 0) * 1000000;
  const tier = cost === 0 ? 'free' : cost < 0.5 ? 'basic' : cost < 2 ? 'pro' : 'enterprise';
  analysis.byTier[tier] = (analysis.byTier[tier] || 0) + 1;
  
  // Categorize
  if (model.id.includes(':free')) {
    analysis.freeModels.push(model.id);
  }
  
  if (communityProviders.includes(provider)) {
    analysis.exclusiveModels.push(model.id);
    analysis.communityModels.push(model.id);
  }
  
  const desc = (model.description || '').toLowerCase();
  const name = model.name.toLowerCase();
  
  if (model.id.includes('reasoning') || model.id.includes('r1') || 
      model.id.includes('thinking') || desc.includes('reasoning')) {
    analysis.reasoningModels.push(model.id);
  }
  
  if (model.id.includes('vision') || model.id.includes('vl') || 
      model.id.includes('internvl') || desc.includes('vision')) {
    analysis.visionModels.push(model.id);
  }
  
  if (model.id.includes('code') || model.id.includes('coder') || 
      desc.includes('code')) {
    analysis.codingModels.push(model.id);
  }
});

// Print report
console.log('OpenRouter Model Integration Analysis');
console.log('=====================================');
console.log(`Total models added: ${models.length}`);
console.log('\nModels by Provider:');
Object.entries(analysis.byProvider)
  .sort((a, b) => b[1] - a[1])
  .forEach(([provider, count]) => {
    console.log(`  ${provider}: ${count}`);
  });

console.log('\nModels by Tier:');
Object.entries(analysis.byTier).forEach(([tier, count]) => {
  console.log(`  ${tier}: ${count}`);
});

console.log(`\nSpecial Categories:`);
console.log(`  Free models: ${analysis.freeModels.length}`);
console.log(`  Community/Exclusive: ${analysis.exclusiveModels.length}`);
console.log(`  Reasoning models: ${analysis.reasoningModels.length}`);
console.log(`  Vision models: ${analysis.visionModels.length}`);
console.log(`  Coding models: ${analysis.codingModels.length}`);

// Save detailed report
const report = {
  summary: {
    totalModels: models.length,
    byProvider: analysis.byProvider,
    byTier: analysis.byTier
  },
  categories: {
    freeModels: analysis.freeModels,
    exclusiveModels: analysis.exclusiveModels,
    reasoningModels: analysis.reasoningModels,
    visionModels: analysis.visionModels,
    codingModels: analysis.codingModels
  }
};

fs.writeFileSync('/home/<USER>/justsimplechat/IMPLEMENTATION/verification/openrouter-integration-report.json', 
  JSON.stringify(report, null, 2));