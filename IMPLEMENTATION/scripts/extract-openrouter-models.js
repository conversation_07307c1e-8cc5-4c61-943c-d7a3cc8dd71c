#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

// Load OpenRouter models
const modelsPath = path.join(__dirname, '../verification/openrouter-models.json');
const data = JSON.parse(fs.readFileSync(modelsPath, 'utf-8'));

// List of priority models to extract
const priorityModels = [
  // Exclusive/Community models
  'sentientagi/dobby-mini-unhinged-plus-llama-3.1-8b',
  'thedrummer/valkyrie-49b-v1',
  'thedrummer/anubis-pro-105b-v1',
  'thedrummer/skyfall-36b-v2',
  'sao10k/l3.3-euryale-70b',
  'eva-unit-01/eva-llama-3.33-70b',
  'cognitivecomputations/dolphin3.0-r1-mistral-24b:free',
  'cognitivecomputations/dolphin3.0-mistral-24b:free',
  'nousresearch/deephermes-3-mistral-24b-preview:free',
  'nousresearch/deephermes-3-llama-3-8b-preview:free',
  
  // Extended provider access
  'ai21/jamba-1.6-large',
  'ai21/jamba-1.6-mini',
  'rekaai/reka-flash-3:free',
  'arcee-ai/maestro-reasoning',
  'arcee-ai/virtuoso-large',
  'arcee-ai/coder-large',
  'arcee-ai/caller-large',
  'arcee-ai/spotlight',
  'microsoft/phi-4-reasoning-plus:free',
  'microsoft/phi-4-reasoning-plus',
  'microsoft/phi-4',
  'microsoft/mai-ds-r1:free',
  'nvidia/llama-3.3-nemotron-super-49b-v1',
  'nvidia/llama-3.1-nemotron-ultra-253b-v1',
  
  // Specialized models
  'qwen/qwq-32b:free',
  'qwen/qwq-32b',
  'qwen/qwen3-235b-a22b:free',
  'qwen/qwen3-235b-a22b',
  'qwen/qwen2.5-vl-72b-instruct',
  'qwen/qwen-vl-max',
  'thudm/glm-z1-rumination-32b',
  'thudm/glm-z1-32b',
  'thudm/glm-4-32b',
  'moonshotai/kimi-vl-a3b-thinking:free',
  'moonshotai/moonlight-16b-a3b-instruct:free',
  
  // Other unique models
  'sarvamai/sarvam-m:free',
  'liquid/lfm-7b',
  'liquid/lfm-3b',
  'amazon/nova-lite-v1',
  'amazon/nova-micro-v1',
  'amazon/nova-pro-v1',
  'inflection/inflection-3-pi',
  'inflection/inflection-3-productivity',
  'alpindale/mistral-nemo-12b-2411-abliterated',
  'anthracite-org/magnum-72b-2411',
];

// Extract details for each model
const extractedModels = [];
for (const modelId of priorityModels) {
  const model = data.data.find(m => m.id === modelId);
  if (model) {
    const inputCost = parseFloat(model.pricing.prompt) * 1000000; // Convert to per million
    const outputCost = parseFloat(model.pricing.completion) * 1000000;
    
    extractedModels.push({
      id: model.id,
      name: model.name,
      description: model.description.replace(/\n/g, ' ').substring(0, 200) + '...',
      contextWindow: model.context_length,
      maxOutput: model.top_provider?.max_completion_tokens || Math.min(model.context_length / 4, 32768),
      inputCost: inputCost,
      outputCost: outputCost,
      isFree: modelId.includes(':free'),
      originalProvider: modelId.split('/')[0],
      architecture: model.architecture,
    });
  }
}

// Output formatted for easy copy-paste
console.log('Extracted', extractedModels.length, 'models:');
console.log(JSON.stringify(extractedModels, null, 2));

// Save to file
fs.writeFileSync(
  path.join(__dirname, '../verification/openrouter-extracted-models.json'),
  JSON.stringify(extractedModels, null, 2)
);