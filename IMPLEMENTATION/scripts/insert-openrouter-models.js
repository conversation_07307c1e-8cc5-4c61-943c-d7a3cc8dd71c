const fs = require('fs');

// Read the registry file
const registryPath = '/home/<USER>/justsimplechat/src/lib/ai/models/registry.ts';
const registry = fs.readFileSync(registryPath, 'utf8');

// Read the generated models
const generatedModels = fs.readFileSync('/home/<USER>/justsimplechat/IMPLEMENTATION/verification/openrouter-models-code.ts', 'utf8');

// Find the insertion point
const insertMarker = '  // OPENROUTER CURATED MODELS - PHASE 5 PLACEHOLDER';
const insertIndex = registry.indexOf(insertMarker);

if (insertIndex === -1) {
  console.error('Could not find insertion marker');
  process.exit(1);
}

// Remove the first two blank lines from generated models
const modelsToInsert = generatedModels.replace(/^\n+/, '');

// Insert the models
const newRegistry = registry.slice(0, insertIndex) + modelsToInsert + '\n' + registry.slice(insertIndex);

// Write back
fs.writeFileSync(registryPath, newRegistry);

console.log('Successfully inserted OpenRouter models into registry');