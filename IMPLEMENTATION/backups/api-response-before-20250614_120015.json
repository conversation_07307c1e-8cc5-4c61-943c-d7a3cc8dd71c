{"total": 25, "providers": [{"name": "anthropic", "models": [{"id": "claude-opus-4-20250514", "name": "Claude 4 Opus", "description": "Latest Anthropic flagship model with advanced reasoning", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 15, "outputCost": 75, "speed": "medium", "tier": "enterprise", "capabilities": ["general_chat", "code_generation", "creative_writing", "analysis", "long_context", "reasoning"], "tags": ["flagship", "reasoning", "latest"], "recommendedFor": ["complex-reasoning", "creative-writing", "analysis"], "deprecated": false}, {"id": "claude-sonnet-4-20250514", "name": "Claude 4 Sonnet", "description": "Coding champion with 72.7% SWE-bench accuracy", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 3, "outputCost": 15, "speed": "medium", "tier": "enterprise", "capabilities": ["code_generation", "general_chat", "analysis", "reasoning"], "tags": ["coding", "flagship", "latest"], "recommendedFor": ["coding", "software-development", "technical-analysis"], "deprecated": false}, {"id": "claude-3-5-sonnet-20241022", "name": "Claude 3.5 Sonnet", "description": "Anthropic flagship for reasoning and coding", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 3, "outputCost": 15, "speed": "medium", "tier": "enterprise", "capabilities": ["general_chat", "code_generation", "creative_writing", "analysis", "long_context"], "tags": ["reasoning", "coding"], "recommendedFor": ["coding", "complex-reasoning"], "deprecated": false}, {"id": "claude-3-5-haiku-20241022", "name": "Claude 3.5 Haiku", "description": "Fast and efficient Claude model", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 0.25, "outputCost": 1.25, "speed": "fast", "tier": "pro", "capabilities": ["general_chat", "code_generation", "analysis"], "tags": ["fast", "efficient"], "recommendedFor": ["quick-tasks", "development"], "deprecated": false}, {"id": "claude-3-opus-20240229", "name": "Claude 3 Opus", "description": "Previous generation flagship", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 15, "outputCost": 75, "speed": "slow", "tier": "starter", "capabilities": ["general_chat", "code_generation", "creative_writing", "analysis", "long_context"], "tags": ["premium", "legacy"], "recommendedFor": ["complex-analysis"], "deprecated": false}, {"id": "claude-3-sonnet-20240229", "name": "Claude 3 Sonnet", "description": "Balanced previous generation", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 3, "outputCost": 15, "speed": "medium", "tier": "starter", "capabilities": ["general_chat", "code_generation", "analysis"], "tags": ["balanced", "legacy"], "recommendedFor": ["general-purpose"], "deprecated": false}, {"id": "claude-3-haiku-20240307", "name": "Claude 3 Haiku", "description": "Fast legacy model", "contextWindow": 200000, "maxOutput": 4096, "inputCost": 0.25, "outputCost": 1.25, "speed": "fast", "tier": "free", "capabilities": ["general_chat", "code_generation"], "tags": ["fast", "legacy", "budget"], "recommendedFor": ["quick-tasks"], "deprecated": false}]}, {"name": "cohere", "models": [{"id": "command-r-plus", "name": "Command R+", "description": "Cohere enterprise RAG model", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 2.5, "outputCost": 10, "speed": "medium", "tier": "pro", "capabilities": ["general_chat", "analysis", "analysis", "translation"], "tags": ["rag", "enterprise", "multilingual"], "recommendedFor": ["rag", "enterprise"], "deprecated": false}, {"id": "command-r", "name": "Command R", "description": "Efficient RAG model", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 0.5, "outputCost": 1.5, "speed": "fast", "tier": "pro", "capabilities": ["general_chat", "analysis", "analysis"], "tags": ["rag", "efficient"], "recommendedFor": ["rag", "cost-effective"], "deprecated": false}]}, {"name": "google", "models": [{"id": "gemini-2.5-flash-preview-05-20", "name": "Gemini 2.5 Flash", "description": "Google latest 2.5 Flash model (API verified)", "contextWindow": 1048576, "maxOutput": 8192, "inputCost": 0.075, "outputCost": 0.3, "speed": "fast", "tier": "pro", "capabilities": ["general_chat", "code_generation", "analysis", "vision"], "tags": ["experimental", "multimodal", "fast"], "recommendedFor": ["development", "testing"], "deprecated": false}, {"id": "gemini-2.5-pro-preview-06-05", "name": "Gemini 2.5 Pro", "description": "Google flagship 2.5 Pro model (API verified)", "contextWindow": 1048576, "maxOutput": 8192, "inputCost": 7, "outputCost": 21, "speed": "medium", "tier": "enterprise", "capabilities": ["general_chat", "code_generation", "analysis", "vision", "long_context", "reasoning"], "tags": ["flagship", "latest", "verified"], "recommendedFor": ["complex-analysis", "reasoning"], "deprecated": false}, {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "description": "Google flagship model", "contextWindow": 1048576, "maxOutput": 8192, "inputCost": 3.5, "outputCost": 10.5, "speed": "medium", "tier": "enterprise", "capabilities": ["general_chat", "code_generation", "analysis", "vision", "long_context"], "tags": ["flagship", "multimodal", "long-context"], "recommendedFor": ["complex-analysis", "multimodal"], "deprecated": false}, {"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash", "description": "Fast multimodal model", "contextWindow": 1048576, "maxOutput": 8192, "inputCost": 0.075, "outputCost": 0.3, "speed": "fast", "tier": "pro", "capabilities": ["general_chat", "code_generation", "analysis", "vision"], "tags": ["fast", "multimodal", "affordable"], "recommendedFor": ["cost-effective", "multimodal"], "deprecated": false}, {"id": "gemini-pro", "name": "Gemini Pro", "description": "Legacy Gemini model", "contextWindow": 32768, "maxOutput": 2048, "inputCost": 0.5, "outputCost": 1.5, "speed": "medium", "tier": "free", "capabilities": ["general_chat", "code_generation", "analysis"], "tags": ["legacy", "stable"], "recommendedFor": ["simple-tasks"], "deprecated": false}]}, {"name": "mistral", "models": [{"id": "mistral-large-latest", "name": "Mistral Large", "description": "Mistral flagship model", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 2, "outputCost": 6, "speed": "medium", "tier": "enterprise", "capabilities": ["general_chat", "code_generation", "analysis", "translation"], "tags": ["flagship", "multilingual"], "recommendedFor": ["multilingual", "coding"], "deprecated": false}, {"id": "mistral-small-latest", "name": "Mistra<PERSON> Small", "description": "Efficient Mistral model", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 0.2, "outputCost": 0.6, "speed": "fast", "tier": "pro", "capabilities": ["general_chat", "code_generation", "translation"], "tags": ["efficient", "multilingual", "affordable"], "recommendedFor": ["cost-effective", "multilingual"], "deprecated": false}, {"id": "codestral-latest", "name": "Codestral", "description": "Mistral specialized coding model", "contextWindow": 32768, "maxOutput": 4096, "inputCost": 0.2, "outputCost": 0.6, "speed": "fast", "tier": "pro", "capabilities": ["code_generation", "analysis"], "tags": ["coding", "specialized"], "recommendedFor": ["code-generation", "development"], "deprecated": false}]}, {"name": "openai", "models": [{"id": "o3-pro-2025-06-10", "name": "O3 Pro", "description": "Most advanced reasoning model (API verified)", "contextWindow": 128000, "maxOutput": 32768, "inputCost": 20, "outputCost": 100, "speed": "slow", "tier": "enterprise", "capabilities": ["reasoning", "code_generation", "analysis", "math"], "tags": ["reasoning", "premium", "verified"], "recommendedFor": ["complex-reasoning", "research", "mathematics"], "deprecated": false}, {"id": "gpt-4o", "name": "GPT-4o", "description": "OpenAI flagship multimodal model", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 5, "outputCost": 15, "speed": "medium", "tier": "enterprise", "capabilities": ["general_chat", "code_generation", "analysis", "vision"], "tags": ["multimodal", "flagship"], "recommendedFor": ["general-purpose", "multimodal"], "deprecated": false}, {"id": "gpt-4o-mini", "name": "GPT-4o Mini", "description": "Affordable multimodal model", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 0.15, "outputCost": 0.6, "speed": "fast", "tier": "pro", "capabilities": ["general_chat", "code_generation", "analysis", "vision"], "tags": ["affordable", "multimodal"], "recommendedFor": ["cost-effective", "general-purpose"], "deprecated": false}, {"id": "gpt-4-turbo", "name": "GPT-4 Turbo", "description": "Previous generation flagship", "contextWindow": 128000, "maxOutput": 4096, "inputCost": 10, "outputCost": 30, "speed": "medium", "tier": "starter", "capabilities": ["general_chat", "code_generation", "analysis"], "tags": ["legacy", "stable"], "recommendedFor": ["stable-workloads"], "deprecated": false}, {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo", "description": "Legacy affordable model", "contextWindow": 16385, "maxOutput": 4096, "inputCost": 0.5, "outputCost": 1.5, "speed": "fast", "tier": "free", "capabilities": ["general_chat", "code_generation"], "tags": ["legacy", "budget"], "recommendedFor": ["simple-tasks"], "deprecated": false}]}, {"name": "perplexity", "models": [{"id": "llama-3.1-sonar-large-128k-online", "name": "Sonar Large Online", "description": "Perplexity search-enhanced model", "contextWindow": 127072, "maxOutput": 4096, "inputCost": 3, "outputCost": 15, "speed": "slow", "tier": "pro", "capabilities": ["general_chat", "web_search", "analysis"], "tags": ["search", "real-time", "web-access"], "recommendedFor": ["research", "current-events"], "deprecated": false}, {"id": "llama-3.1-sonar-small-128k-online", "name": "Sonar Small Online", "description": "Efficient search model", "contextWindow": 127072, "maxOutput": 4096, "inputCost": 0.2, "outputCost": 1, "speed": "medium", "tier": "pro", "capabilities": ["general_chat", "web_search"], "tags": ["search", "efficient", "web-access"], "recommendedFor": ["research", "cost-effective"], "deprecated": false}]}, {"name": "xai", "models": [{"id": "grok-beta", "name": "Grok Beta", "description": "xAI flagship model with real-time data", "contextWindow": 131072, "maxOutput": 4096, "inputCost": 5, "outputCost": 15, "speed": "medium", "tier": "pro", "capabilities": ["general_chat", "code_generation", "analysis", "web_search"], "tags": ["real-time", "web-access"], "recommendedFor": ["current-events", "web-search"], "deprecated": false}]}], "defaultModel": "claude-opus-4-20250514", "userTier": "enterprise"}