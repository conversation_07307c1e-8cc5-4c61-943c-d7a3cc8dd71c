import { z } from 'zod';
import { Model, ModelCapability } from '@/types';

// Re-export ModelCapability enum as ModelCapabilities for convenience
export const ModelCapabilities = ModelCapability;

// Check if we're in test mode (all models available)
const TEST_MODE = process.env.NEXT_PUBLIC_TEST_MODE === 'true';

// COMPREHENSIVE model registry with ALL real models that exist in June 2025
// Built from actual API responses and verified endpoints
export const MODEL_REGISTRY: Record<string, Model> = {
  // ============================================================
  // OPENAI MODELS (COMPLETE LIST FROM API - JUNE 2025)
  // ============================================================
  
  // O3 Series - Latest reasoning models
  'o3-pro': {
    id: 'o3-pro',
    provider: 'openai',
    name: 'O3 Pro',
    description: 'Most advanced reasoning model from OpenAI',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.MATH,
    ],
    contextWindow: 200000,
    maxOutput: 32768,
    inputCost: 20,
    outputCost: 100,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['reasoning', 'premium', 'latest'],
    recommendedFor: ['complex-reasoning', 'research', 'mathematics'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'o3': {
    id: 'o3',
    provider: 'openai',
    name: 'O3',
    description: 'Advanced reasoning model with breakthrough performance',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.MATH,
    ],
    contextWindow: 200000,
    maxOutput: 32768,
    inputCost: 15,
    outputCost: 60,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['reasoning', 'premium', 'latest'],
    recommendedFor: ['complex-reasoning', 'mathematics', 'coding'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'o3-mini': {
    id: 'o3-mini',
    provider: 'openai',
    name: 'O3 Mini',
    description: 'Compact reasoning model for efficient processing',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 1,
    outputCost: 4,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['reasoning', 'efficient', 'latest'],
    recommendedFor: ['reasoning-tasks', 'cost-effective'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'o4-mini': {
    id: 'o4-mini',
    provider: 'openai',
    name: 'O4 Mini',
    description: 'Next-generation compact reasoning model',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 1.2,
    outputCost: 4.8,
    avgLatency: 550,
    avgTTFT: 220,
    tags: ['reasoning', 'multimodal', 'latest'],
    recommendedFor: ['reasoning-tasks', 'multimodal-analysis'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  // O1 Series - Previous generation reasoning
  'o1-pro': {
    id: 'o1-pro',
    provider: 'openai',
    name: 'O1 Pro',
    description: 'Professional reasoning model with advanced capabilities',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.MATH,
    ],
    contextWindow: 128000,
    maxOutput: 32768,
    inputCost: 15,
    outputCost: 60,
    avgLatency: 1000,
    avgTTFT: 450,
    tags: ['reasoning', 'professional'],
    recommendedFor: ['complex-reasoning', 'professional-tasks'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'o1': {
    id: 'o1',
    provider: 'openai',
    name: 'O1',
    description: 'Reasoning model for complex problem solving',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.MATH,
    ],
    contextWindow: 128000,
    maxOutput: 32768,
    inputCost: 15,
    outputCost: 60,
    avgLatency: 1000,
    avgTTFT: 450,
    tags: ['reasoning', 'problem-solving'],
    recommendedFor: ['reasoning-tasks', 'problem-solving'],
    available: true,
    speed: 'slow' as const,
    tier: 'pro',
  },

  'o1-mini': {
    id: 'o1-mini',
    provider: 'openai',
    name: 'O1 Mini',
    description: 'Efficient reasoning model for everyday tasks',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 3,
    outputCost: 12,
    avgLatency: 700,
    avgTTFT: 300,
    tags: ['reasoning', 'efficient'],
    recommendedFor: ['reasoning-tasks', 'cost-effective'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'o1-preview': {
    id: 'o1-preview',
    provider: 'openai',
    name: 'O1 Preview',
    description: 'Preview version of O1 reasoning capabilities',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 128000,
    maxOutput: 32768,
    inputCost: 15,
    outputCost: 60,
    avgLatency: 1100,
    avgTTFT: 500,
    tags: ['reasoning', 'preview'],
    recommendedFor: ['testing', 'preview-features'],
    available: true,
    speed: 'slow' as const,
    tier: 'pro',
  },

  // GPT-4.1 Series - Latest flagship
  'gpt-4.1': {
    id: 'gpt-4.1',
    provider: 'openai',
    name: 'GPT-4.1',
    description: 'Latest flagship with 1M context and major improvements',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.LONG_CONTEXT,
      ModelCapabilities.VISION,
    ],
    contextWindow: 1000000,
    maxOutput: 16384,
    inputCost: 10,
    outputCost: 30,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['flagship', 'long-context', 'latest'],
    recommendedFor: ['long-documents', 'complex-analysis', 'general-purpose'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'gpt-4.1-mini': {
    id: 'gpt-4.1-mini',
    provider: 'openai',
    name: 'GPT-4.1 Mini',
    description: 'Compact version of GPT-4.1 for efficiency',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.15,
    outputCost: 0.60,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['efficient', 'compact', 'latest'],
    recommendedFor: ['cost-effective', 'general-purpose'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'gpt-4.1-nano': {
    id: 'gpt-4.1-nano',
    provider: 'openai',
    name: 'GPT-4.1 Nano',
    description: 'Ultra-efficient version for high-volume use',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 32768,
    maxOutput: 8192,
    inputCost: 0.05,
    outputCost: 0.20,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['ultra-efficient', 'high-volume', 'latest'],
    recommendedFor: ['high-volume', 'simple-tasks'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
  },

  // GPT-4.5 Preview
  'gpt-4.5-preview': {
    id: 'gpt-4.5-preview',
    provider: 'openai',
    name: 'GPT-4.5 Preview',
    description: 'Preview of next-generation capabilities (deprecated July 2025)',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 12,
    outputCost: 36,
    avgLatency: 900,
    avgTTFT: 400,
    tags: ['preview', 'experimental', 'deprecated'],
    recommendedFor: ['testing', 'preview-features'],
    available: true,
    deprecated: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  // GPT-4o Series - Multimodal flagship
  'chatgpt-4o-latest': {
    id: 'chatgpt-4o-latest',
    provider: 'openai',
    name: 'ChatGPT-4o Latest',
    description: 'Latest ChatGPT-4o with real-time capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.AUDIO_PROCESSING,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 5,
    outputCost: 15,
    avgLatency: 700,
    avgTTFT: 300,
    tags: ['multimodal', 'realtime', 'latest'],
    recommendedFor: ['interactive-chat', 'multimodal'],
    available: true,
    speed: 'fast' as const,
    tier: 'enterprise',
  },

  'gpt-4o': {
    id: 'gpt-4o',
    provider: 'openai',
    name: 'GPT-4o',
    description: 'OpenAI flagship multimodal model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 5,
    outputCost: 15,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['multimodal', 'flagship'],
    recommendedFor: ['general-purpose', 'multimodal'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'gpt-4o-audio-preview': {
    id: 'gpt-4o-audio-preview',
    provider: 'openai',
    name: 'GPT-4o Audio Preview',
    description: 'GPT-4o with advanced audio processing capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.AUDIO_PROCESSING,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 5,
    outputCost: 15,
    avgLatency: 900,
    avgTTFT: 400,
    tags: ['multimodal', 'audio', 'preview'],
    recommendedFor: ['audio-processing', 'multimodal'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'gpt-4o-realtime-preview': {
    id: 'gpt-4o-realtime-preview',
    provider: 'openai',
    name: 'GPT-4o Realtime Preview',
    description: 'Real-time conversational AI with multimodal capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.AUDIO_PROCESSING,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 6,
    outputCost: 18,
    avgLatency: 200,
    avgTTFT: 100,
    tags: ['realtime', 'conversational', 'preview'],
    recommendedFor: ['real-time-chat', 'interactive-applications'],
    available: true,
    speed: 'fast' as const,
    tier: 'enterprise',
  },

  'gpt-4o-search-preview': {
    id: 'gpt-4o-search-preview',
    provider: 'openai',
    name: 'GPT-4o Search Preview',
    description: 'GPT-4o with enhanced web search capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.WEB_SEARCH,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 6,
    outputCost: 18,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['search', 'web-access', 'preview'],
    recommendedFor: ['research', 'current-events'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'gpt-4o-mini': {
    id: 'gpt-4o-mini',
    provider: 'openai',
    name: 'GPT-4o Mini',
    description: 'Affordable multimodal model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.15,
    outputCost: 0.60,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['affordable', 'multimodal'],
    recommendedFor: ['cost-effective', 'general-purpose'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'gpt-4o-mini-audio-preview': {
    id: 'gpt-4o-mini-audio-preview',
    provider: 'openai',
    name: 'GPT-4o Mini Audio Preview',
    description: 'Compact model with audio processing capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.AUDIO_PROCESSING,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.20,
    outputCost: 0.80,
    avgLatency: 700,
    avgTTFT: 300,
    tags: ['affordable', 'multimodal', 'audio'],
    recommendedFor: ['audio-processing', 'cost-effective'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'gpt-4o-mini-realtime-preview': {
    id: 'gpt-4o-mini-realtime-preview',
    provider: 'openai',
    name: 'GPT-4o Mini Realtime Preview',
    description: 'Efficient real-time conversational model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.AUDIO_PROCESSING,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.20,
    outputCost: 0.80,
    avgLatency: 300,
    avgTTFT: 150,
    tags: ['realtime', 'efficient', 'conversational'],
    recommendedFor: ['real-time-chat', 'cost-effective'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  // GPT-4 Turbo Series
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    provider: 'openai',
    name: 'GPT-4 Turbo',
    description: 'Previous generation flagship with improved efficiency',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 4096,
    inputCost: 10,
    outputCost: 30,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['legacy', 'stable', 'multimodal'],
    recommendedFor: ['stable-workloads', 'general-purpose'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  // GPT-3.5 Series
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    provider: 'openai',
    name: 'GPT-3.5 Turbo',
    description: 'Legacy affordable model for simple tasks',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
    ],
    contextWindow: 16385,
    maxOutput: 4096,
    inputCost: 0.5,
    outputCost: 1.5,
    avgLatency: 500,
    avgTTFT: 200,
    tags: ['legacy', 'budget', 'fast'],
    recommendedFor: ['simple-tasks', 'cost-effective'],
    available: true,
    speed: 'fast' as const,
    tier: 'free',
  },

  'gpt-3.5-turbo-instruct': {
    id: 'gpt-3.5-turbo-instruct',
    provider: 'openai',
    name: 'GPT-3.5 Turbo Instruct',
    description: 'Instruction-following version of GPT-3.5',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
    ],
    contextWindow: 4096,
    maxOutput: 4096,
    inputCost: 1.5,
    outputCost: 2.0,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['legacy', 'instruct', 'fast'],
    recommendedFor: ['instruction-following', 'simple-tasks'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
  },

  // Computer Use Models
  'computer-use-preview': {
    id: 'computer-use-preview',
    provider: 'openai',
    name: 'Computer Use Preview',
    description: 'Experimental model for computer interaction tasks',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.TOOL_USE,
      ModelCapabilities.VISION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 8,
    outputCost: 24,
    avgLatency: 1500,
    avgTTFT: 600,
    tags: ['experimental', 'computer-use', 'preview'],
    recommendedFor: ['automation', 'computer-interaction'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },


  // ============================================================
  // ANTHROPIC MODELS (COMPLETE LIST FROM API - JUNE 2025)
  // ============================================================

  // Claude 4 Series - Latest generation
  'claude-opus-4-20250514': {
    id: 'claude-opus-4-20250514',
    provider: 'anthropic',
    name: 'Claude 4 Opus',
    description: 'Latest Anthropic flagship model with advanced reasoning',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.LONG_CONTEXT,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 200000,
    maxOutput: 8192,
    inputCost: 15,
    outputCost: 75,
    avgLatency: 1000,
    avgTTFT: 400,
    tags: ['flagship', 'reasoning', 'latest'],
    recommendedFor: ['complex-reasoning', 'creative-writing', 'analysis'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'claude-sonnet-4-20250514': {
    id: 'claude-sonnet-4-20250514',
    provider: 'anthropic',
    name: 'Claude 4 Sonnet',
    description: 'Coding champion with 72.7% SWE-bench accuracy',
    capabilities: [
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 200000,
    maxOutput: 8192,
    inputCost: 3,
    outputCost: 15,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['coding', 'flagship', 'latest'],
    recommendedFor: ['coding', 'software-development', 'technical-analysis'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  // Claude 3.7 Series - Enhanced models
  'claude-3-7-sonnet-20250219': {
    id: 'claude-3-7-sonnet-20250219',
    provider: 'anthropic',
    name: 'Claude 3.7 Sonnet',
    description: 'Enhanced Sonnet with improved reasoning capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
      ModelCapabilities.THINKING_MODE,
    ],
    contextWindow: 200000,
    maxOutput: 8192,
    inputCost: 3,
    outputCost: 15,
    avgLatency: 850,
    avgTTFT: 375,
    tags: ['enhanced', 'reasoning', 'thinking'],
    recommendedFor: ['complex-reasoning', 'thinking-tasks'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'claude-3-5-sonnet-20241022': {
    id: 'claude-3-5-sonnet-20241022',
    provider: 'anthropic',
    name: 'Claude 3.5 Sonnet',
    description: 'Anthropic flagship for reasoning and coding',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 200000,
    maxOutput: 4096,
    inputCost: 3,
    outputCost: 15,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['reasoning', 'coding'],
    recommendedFor: ['coding', 'complex-reasoning'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'claude-3-5-haiku-20241022': {
    id: 'claude-3-5-haiku-20241022',
    provider: 'anthropic',
    name: 'Claude 3.5 Haiku',
    description: 'Fast and efficient Claude model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 200000,
    maxOutput: 4096,
    inputCost: 0.25,
    outputCost: 1.25,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['fast', 'efficient'],
    recommendedFor: ['quick-tasks', 'development'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'claude-3-opus-20240229': {
    id: 'claude-3-opus-20240229',
    provider: 'anthropic',
    name: 'Claude 3 Opus',
    description: 'Previous generation flagship',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 200000,
    maxOutput: 4096,
    inputCost: 15,
    outputCost: 75,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['premium', 'legacy'],
    recommendedFor: ['complex-analysis'],
    available: true,
    speed: 'slow' as const,
    tier: 'starter',
  },

  'claude-3-sonnet-20240229': {
    id: 'claude-3-sonnet-20240229',
    provider: 'anthropic',
    name: 'Claude 3 Sonnet',
    description: 'Balanced previous generation',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 200000,
    maxOutput: 4096,
    inputCost: 3,
    outputCost: 15,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['balanced', 'legacy'],
    recommendedFor: ['general-purpose'],
    available: true,
    speed: 'medium' as const,
    tier: 'starter',
  },

  'claude-3-haiku-20240307': {
    id: 'claude-3-haiku-20240307',
    provider: 'anthropic',
    name: 'Claude 3 Haiku',
    description: 'Fast legacy model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
    ],
    contextWindow: 200000,
    maxOutput: 4096,
    inputCost: 0.25,
    outputCost: 1.25,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['fast', 'legacy', 'budget'],
    recommendedFor: ['quick-tasks'],
    available: true,
    speed: 'fast' as const,
    tier: 'free',
  },

  // ============================================================
  // GOOGLE MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'gemini-2.5-flash-preview-05-20': {
    id: 'gemini-2.5-flash-preview-05-20',
    provider: 'google',
    name: 'Gemini 2.5 Flash',
    description: 'Google latest 2.5 Flash model (API verified)',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 1048576,
    maxOutput: 8192,
    inputCost: 0.075,
    outputCost: 0.30,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['experimental', 'multimodal', 'fast'],
    recommendedFor: ['development', 'testing'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'gemini-2.5-pro-preview-06-05': {
    id: 'gemini-2.5-pro-preview-06-05',
    provider: 'google',
    name: 'Gemini 2.5 Pro',
    description: 'Google flagship 2.5 Pro model (API verified)',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.LONG_CONTEXT,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 1048576,
    maxOutput: 8192,
    inputCost: 7,
    outputCost: 21,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['flagship', 'latest', 'verified'],
    recommendedFor: ['complex-analysis', 'reasoning'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    provider: 'google',
    name: 'Gemini 1.5 Pro',
    description: 'Google flagship model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 1048576,
    maxOutput: 8192,
    inputCost: 3.5,
    outputCost: 10.5,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['flagship', 'multimodal', 'long-context'],
    recommendedFor: ['complex-analysis', 'multimodal'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    provider: 'google',
    name: 'Gemini 1.5 Flash',
    description: 'Fast multimodal model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.VISION,
    ],
    contextWindow: 1048576,
    maxOutput: 8192,
    inputCost: 0.075,
    outputCost: 0.30,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['fast', 'multimodal', 'affordable'],
    recommendedFor: ['cost-effective', 'multimodal'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'gemini-pro': {
    id: 'gemini-pro',
    provider: 'google',
    name: 'Gemini Pro',
    description: 'Legacy Gemini model',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 32768,
    maxOutput: 2048,
    inputCost: 0.5,
    outputCost: 1.5,
    avgLatency: 700,
    avgTTFT: 300,
    tags: ['legacy', 'stable'],
    recommendedFor: ['simple-tasks'],
    available: true,
    speed: 'medium' as const,
    tier: 'free',
  },

  // ============================================================
  // XAI MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'grok-3': {
    id: 'grok-3',
    provider: 'xai',
    name: 'Grok 3',
    description: 'Latest xAI flagship with 1M context and advanced reasoning',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.REASONING,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 1000000,
    maxOutput: 32768,
    inputCost: 40, // Estimated based on context window size
    outputCost: 120,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['flagship', 'reasoning', 'long-context', 'personality', 'latest'],
    recommendedFor: ['complex-reasoning', 'long-documents', 'personality-driven-chat'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'grok-beta': {
    id: 'grok-beta',
    provider: 'xai',
    name: 'Grok Beta',
    description: 'xAI flagship model with real-time data and personality',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 131072,
    maxOutput: 32768,
    inputCost: 38.15, // Based on research: $5 per 131k tokens
    outputCost: 114.45, // Based on research: $15 per 131k tokens
    avgLatency: 900,
    avgTTFT: 400,
    tags: ['real-time', 'web-access', 'personality', 'witty'],
    recommendedFor: ['current-events', 'web-search', 'personality-driven-chat'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'grok-2': {
    id: 'grok-2',
    provider: 'xai',
    name: 'Grok 2',
    description: 'Previous generation xAI model with balanced performance',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.WEB_SEARCH,
    ],
    contextWindow: 131072,
    maxOutput: 16384,
    inputCost: 35,
    outputCost: 105,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['balanced', 'personality', 'web-access'],
    recommendedFor: ['general-purpose', 'web-search'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'grok-2-1212': {
    id: 'grok-2-1212',
    provider: 'xai',
    name: 'Grok 2 (1212)',
    description: 'Updated Grok 2 model with improved capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 131072,
    maxOutput: 16384,
    inputCost: 36,
    outputCost: 108,
    avgLatency: 750,
    avgTTFT: 325,
    tags: ['updated', 'personality', 'web-access', 'reasoning'],
    recommendedFor: ['general-purpose', 'reasoning-tasks'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  // ============================================================
  // MISTRAL MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'mistral-large-2411': {
    id: 'mistral-large-2411',
    provider: 'mistral',
    name: 'Mistral Large 24.11',
    description: 'Latest Mistral flagship with 123B parameters and enhanced capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
      ModelCapabilities.REASONING,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 2,
    outputCost: 6,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['flagship', 'multilingual', 'reasoning', 'latest'],
    recommendedFor: ['multilingual', 'coding', 'complex-reasoning'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'mistral-large-latest': {
    id: 'mistral-large-latest',
    provider: 'mistral',
    name: 'Mistral Large',
    description: 'Mistral flagship model with strong multilingual capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 128000,
    maxOutput: 8192,
    inputCost: 2,
    outputCost: 6,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['flagship', 'multilingual', 'european'],
    recommendedFor: ['multilingual', 'coding', 'european-languages'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'mistral-small-latest': {
    id: 'mistral-small-latest',
    provider: 'mistral',
    name: 'Mistral Small',
    description: 'Efficient Mistral model with excellent multilingual support',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 128000,
    maxOutput: 8192,
    inputCost: 0.2,
    outputCost: 0.6,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['efficient', 'multilingual', 'affordable', 'european'],
    recommendedFor: ['cost-effective', 'multilingual', 'european-languages'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'codestral-2501': {
    id: 'codestral-2501',
    provider: 'mistral',
    name: 'Codestral 25.01',
    description: 'Latest Codestral with 256k context and 80+ programming languages',
    capabilities: [
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 256000,
    maxOutput: 32768,
    inputCost: 1,
    outputCost: 3,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['coding', 'specialized', 'long-context', 'latest', '80-languages'],
    recommendedFor: ['code-generation', 'development', 'code-completion'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'codestral-latest': {
    id: 'codestral-latest',
    provider: 'mistral',
    name: 'Codestral',
    description: 'Specialized coding model fluent in 80+ programming languages',
    capabilities: [
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 1,
    outputCost: 3,
    avgLatency: 500,
    avgTTFT: 200,
    tags: ['coding', 'specialized', '80-languages'],
    recommendedFor: ['code-generation', 'development', 'code-review'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'mistral-medium': {
    id: 'mistral-medium',
    provider: 'mistral',
    name: 'Mistral Medium',
    description: 'Balanced Mistral model for general-purpose tasks',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 32768,
    maxOutput: 8192,
    inputCost: 1,
    outputCost: 3,
    avgLatency: 650,
    avgTTFT: 275,
    tags: ['balanced', 'multilingual', 'general-purpose'],
    recommendedFor: ['general-purpose', 'balanced-performance'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  // ============================================================
  // COHERE MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'command-r-plus-08-2024': {
    id: 'command-r-plus-08-2024',
    provider: 'cohere',
    name: 'Command R+ (08-2024)',
    description: 'Latest Cohere enterprise RAG model with 50% higher throughput',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
      ModelCapabilities.TOOL_USE,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 2.5, // Based on research
    outputCost: 10, // Based on research
    avgLatency: 600, // 25% lower than previous
    avgTTFT: 300,
    tags: ['rag', 'enterprise', 'multilingual', 'tool-use', 'latest'],
    recommendedFor: ['rag', 'enterprise', 'multi-step-agents'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'command-r-08-2024': {
    id: 'command-r-08-2024',
    provider: 'cohere',
    name: 'Command R (08-2024)',
    description: 'Updated efficient RAG model with 50% higher throughput',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
      ModelCapabilities.TOOL_USE,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.15, // Based on research
    outputCost: 0.60, // Based on research
    avgLatency: 480, // 20% lower than previous
    avgTTFT: 200,
    tags: ['rag', 'efficient', 'multilingual', 'tool-use', 'latest'],
    recommendedFor: ['rag', 'cost-effective', 'function-calling'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'command-r-plus': {
    id: 'command-r-plus',
    provider: 'cohere',
    name: 'Command R+',
    description: 'Cohere enterprise RAG model with 104B parameters',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
      ModelCapabilities.TOOL_USE,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 128000,
    maxOutput: 8192,
    inputCost: 3,
    outputCost: 15,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['rag', 'enterprise', 'multilingual', 'tool-use'],
    recommendedFor: ['rag', 'enterprise', 'complex-workflows'],
    available: true,
    speed: 'medium' as const,
    tier: 'enterprise',
  },

  'command-r': {
    id: 'command-r',
    provider: 'cohere',
    name: 'Command R',
    description: 'Efficient RAG model with high precision retrieval',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
      ModelCapabilities.TOOL_USE,
    ],
    contextWindow: 128000,
    maxOutput: 8192,
    inputCost: 0.5,
    outputCost: 1.5,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['rag', 'efficient', 'multilingual'],
    recommendedFor: ['rag', 'cost-effective', 'retrieval-tasks'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'command': {
    id: 'command',
    provider: 'cohere',
    name: 'Command',
    description: 'General-purpose Cohere model for business applications',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 32768,
    maxOutput: 8192,
    inputCost: 1,
    outputCost: 2,
    avgLatency: 500,
    avgTTFT: 200,
    tags: ['general-purpose', 'business', 'multilingual'],
    recommendedFor: ['business-applications', 'general-purpose'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  // ============================================================
  // PERPLEXITY MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'sonar-pro': {
    id: 'sonar-pro',
    provider: 'perplexity',
    name: 'Sonar Pro',
    description: 'Perplexity flagship search model with 200k context and real-time web access',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.LONG_CONTEXT,
    ],
    contextWindow: 200000,
    maxOutput: 8192,
    inputCost: 5, // Based on research: $5 per 1000 searches
    outputCost: 5, // Simplified unified pricing
    avgLatency: 800, // Blazing fast at 1200 tokens/second
    avgTTFT: 100,
    tags: ['search', 'flagship', 'real-time', 'web-access', 'citations'],
    recommendedFor: ['research', 'current-events', 'complex-queries'],
    available: true,
    speed: 'fast' as const,
    tier: 'enterprise',
  },

  'sonar': {
    id: 'sonar',
    provider: 'perplexity',
    name: 'Sonar',
    description: 'Built on Llama 3.3 70B with enhanced factuality for search',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 127000,
    maxOutput: 8192,
    inputCost: 3, // Based on research pricing structure
    outputCost: 3,
    avgLatency: 600,
    avgTTFT: 120,
    tags: ['search', 'factual', 'real-time', 'web-access', 'citations'],
    recommendedFor: ['research', 'fact-checking', 'current-events'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'llama-3.1-sonar-large-128k-online': {
    id: 'llama-3.1-sonar-large-128k-online',
    provider: 'perplexity',
    name: 'Sonar Large Online',
    description: 'Large Perplexity search model with real-time web access',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 127072,
    maxOutput: 8192,
    inputCost: 1, // Estimated per 1M tokens
    outputCost: 1,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['search', 'real-time', 'web-access', 'citations'],
    recommendedFor: ['research', 'current-events', 'detailed-analysis'],
    available: true,
    speed: 'slow' as const,
    tier: 'pro',
  },

  'llama-3.1-sonar-small-128k-online': {
    id: 'llama-3.1-sonar-small-128k-online',
    provider: 'perplexity',
    name: 'Sonar Small Online',
    description: 'Efficient search model with real-time web access',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.WEB_SEARCH,
    ],
    contextWindow: 127072,
    maxOutput: 8192,
    inputCost: 0.5, // More cost-effective
    outputCost: 0.5,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['search', 'efficient', 'web-access', 'citations'],
    recommendedFor: ['research', 'cost-effective', 'quick-searches'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'llama-3.1-sonar-huge-128k-online': {
    id: 'llama-3.1-sonar-huge-128k-online',
    provider: 'perplexity',
    name: 'Sonar Huge Online',
    description: 'Most capable Perplexity search model for complex research',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.WEB_SEARCH,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 127072,
    maxOutput: 8192,
    inputCost: 2,
    outputCost: 2,
    avgLatency: 1500,
    avgTTFT: 600,
    tags: ['search', 'premium', 'web-access', 'citations', 'reasoning'],
    recommendedFor: ['complex-research', 'academic-research', 'detailed-analysis'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  // ============================================================
  // DEEPSEEK MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'deepseek-v3': {
    id: 'deepseek-v3',
    provider: 'deepseek',
    name: 'DeepSeek V3',
    description: 'DeepSeek flagship with 671B parameters and MoE architecture',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
      ModelCapabilities.MATH,
    ],
    contextWindow: 128000,
    maxOutput: 32768,
    inputCost: 0.14, // Based on research: $0.14 per 1M tokens
    outputCost: 0.28, // Based on research: $0.28 per 1M tokens
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['flagship', 'reasoning', 'moe', 'math', 'affordable'],
    recommendedFor: ['reasoning-tasks', 'coding', 'mathematics'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'deepseek-r1': {
    id: 'deepseek-r1',
    provider: 'deepseek',
    name: 'DeepSeek R1',
    description: 'First-generation reasoning model competing with OpenAI o1',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.MATH,
    ],
    contextWindow: 128000,
    maxOutput: 32768,
    inputCost: 0.55, // Based on research for R1-0528
    outputCost: 2.19, // Based on research for R1-0528
    avgLatency: 1200, // Reasoning models are slower
    avgTTFT: 500,
    tags: ['reasoning', 'chain-of-thought', 'math', 'breakthrough'],
    recommendedFor: ['complex-reasoning', 'mathematics', 'problem-solving'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'deepseek-r1-0528': {
    id: 'deepseek-r1-0528',
    provider: 'deepseek',
    name: 'DeepSeek R1 (0528)',
    description: 'Updated reasoning model with system prompts and function calling',
    capabilities: [
      ModelCapabilities.REASONING,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.MATH,
      ModelCapabilities.TOOL_USE,
    ],
    contextWindow: 130000,
    maxOutput: 32768,
    inputCost: 0.55, // Based on research
    outputCost: 2.19, // Based on research
    avgLatency: 1100,
    avgTTFT: 450,
    tags: ['reasoning', 'latest', 'function-calling', 'system-prompts'],
    recommendedFor: ['complex-reasoning', 'agentic-ai', 'mathematics'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'deepseek-coder-v2': {
    id: 'deepseek-coder-v2',
    provider: 'deepseek',
    name: 'DeepSeek Coder V2',
    description: 'Specialized coding model with advanced programming capabilities',
    capabilities: [
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
    ],
    contextWindow: 128000,
    maxOutput: 32768,
    inputCost: 0.14,
    outputCost: 0.28,
    avgLatency: 600,
    avgTTFT: 250,
    tags: ['coding', 'specialized', 'programming', 'affordable'],
    recommendedFor: ['code-generation', 'programming', 'software-development'],
    available: true,
    speed: 'fast' as const,
    tier: 'pro',
  },

  'deepseek-math': {
    id: 'deepseek-math',
    provider: 'deepseek',
    name: 'DeepSeek Math',
    description: 'Specialized mathematical reasoning model',
    capabilities: [
      ModelCapabilities.MATH,
      ModelCapabilities.REASONING,
      ModelCapabilities.ANALYSIS,
    ],
    contextWindow: 64000,
    maxOutput: 16384,
    inputCost: 0.14,
    outputCost: 0.28,
    avgLatency: 700,
    avgTTFT: 300,
    tags: ['math', 'specialized', 'reasoning', 'academic'],
    recommendedFor: ['mathematics', 'academic-research', 'problem-solving'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  // ============================================================
  // META LLAMA MODELS (VERIFIED AS OF JUNE 2025)
  // ============================================================

  'llama-3.3-70b': {
    id: 'llama-3.3-70b',
    provider: 'meta',
    name: 'Llama 3.3 70B',
    description: 'Latest Meta model with 405B-level performance at 70B size',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
      ModelCapabilities.LONG_CONTEXT,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.5, // Open source, pricing varies by provider
    outputCost: 0.5,
    avgLatency: 700,
    avgTTFT: 300,
    tags: ['open-source', 'latest', 'efficient', 'multilingual'],
    recommendedFor: ['general-purpose', 'cost-effective', 'multilingual'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'llama-3.1-405b': {
    id: 'llama-3.1-405b',
    provider: 'meta',
    name: 'Llama 3.1 405B',
    description: 'Massive Meta flagship model with frontier capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.CREATIVE_WRITING,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
      ModelCapabilities.LONG_CONTEXT,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 3, // Estimated for large model hosting
    outputCost: 3,
    avgLatency: 1200,
    avgTTFT: 500,
    tags: ['open-source', 'flagship', 'massive', 'frontier'],
    recommendedFor: ['complex-reasoning', 'creative-writing', 'flagship-performance'],
    available: true,
    speed: 'slow' as const,
    tier: 'enterprise',
  },

  'llama-3.1-70b': {
    id: 'llama-3.1-70b',
    provider: 'meta',
    name: 'Llama 3.1 70B',
    description: 'Balanced Meta model with strong general capabilities',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.REASONING,
      ModelCapabilities.LONG_CONTEXT,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.8,
    outputCost: 0.8,
    avgLatency: 800,
    avgTTFT: 350,
    tags: ['open-source', 'balanced', 'capable', 'multilingual'],
    recommendedFor: ['general-purpose', 'balanced-performance', 'reasoning'],
    available: true,
    speed: 'medium' as const,
    tier: 'pro',
  },

  'llama-3.1-8b': {
    id: 'llama-3.1-8b',
    provider: 'meta',
    name: 'Llama 3.1 8B',
    description: 'Efficient small Meta model for resource-conscious deployments',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
      ModelCapabilities.ANALYSIS,
      ModelCapabilities.TRANSLATION,
    ],
    contextWindow: 128000,
    maxOutput: 16384,
    inputCost: 0.2,
    outputCost: 0.2,
    avgLatency: 400,
    avgTTFT: 150,
    tags: ['open-source', 'efficient', 'small', 'fast'],
    recommendedFor: ['cost-effective', 'simple-tasks', 'high-throughput'],
    available: true,
    speed: 'fast' as const,
    tier: 'starter',
  },

  'llama-3.2-3b': {
    id: 'llama-3.2-3b',
    provider: 'meta',
    name: 'Llama 3.2 3B',
    description: 'Ultra-efficient small model for edge and mobile deployments',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
      ModelCapabilities.CODE_GENERATION,
    ],
    contextWindow: 128000,
    maxOutput: 8192,
    inputCost: 0.1,
    outputCost: 0.1,
    avgLatency: 300,
    avgTTFT: 100,
    tags: ['open-source', 'ultra-efficient', 'edge', 'mobile'],
    recommendedFor: ['edge-deployment', 'mobile-apps', 'ultra-low-cost'],
    available: true,
    speed: 'fast' as const,
    tier: 'free',
  },

  'llama-3.2-1b': {
    id: 'llama-3.2-1b',
    provider: 'meta',
    name: 'Llama 3.2 1B',
    description: 'Tiny model for embedded systems and IoT applications',
    capabilities: [
      ModelCapabilities.GENERAL_CHAT,
    ],
    contextWindow: 128000,
    maxOutput: 4096,
    inputCost: 0.05,
    outputCost: 0.05,
    avgLatency: 200,
    avgTTFT: 50,
    tags: ['open-source', 'tiny', 'embedded', 'iot'],
    recommendedFor: ['iot-applications', 'embedded-systems', 'minimal-resources'],
    available: true,
    speed: 'fast' as const,
    tier: 'free',
  },
};

// Get models available for user tier
export function getAvailableModels(userTier: 'free' | 'starter' | 'pro' | 'enterprise' = 'free'): Model[] {
  // In test mode, all models are available
  if (TEST_MODE) {
    return Object.values(MODEL_REGISTRY).filter(model => model.available && !model.deprecated);
  }

  // Map user tiers to actual model tiers in registry
  const tierHierarchy = {
    free: ['free'],                           // Free users get free models only
    starter: ['free', 'starter'],             // Starter adds starter models
    pro: ['free', 'starter', 'pro'],          // Pro adds pro models
    enterprise: ['free', 'starter', 'pro', 'enterprise'] // Enterprise gets all models
  };

  const allowedTiers = tierHierarchy[userTier] || ['free'];
  
  return Object.values(MODEL_REGISTRY)
    .filter(model => model.available && !model.deprecated)
    .filter(model => {
      // If model doesn't have a tier specified, it's available to all
      if (!model.tier) return true;
      return allowedTiers.includes(model.tier);
    });
}

export function getDefaultModel(userTier: 'free' | 'starter' | 'pro' | 'enterprise' = 'free'): Model {
  // Default models optimized for each tier (using verified model IDs)
  const defaultModels = {
    free: 'gemini-1.5-flash',                   // Free tier gets fast, capable model
    starter: 'gpt-4o-mini',                     // Starter gets performance model
    pro: 'gemini-2.5-flash-preview-05-20',     // Pro gets latest fast model
    enterprise: 'claude-opus-4-20250514'       // Enterprise gets flagship Claude 4
  };
  
  const modelId = defaultModels[userTier] || defaultModels.free;
  const model = MODEL_REGISTRY[modelId];
  
  if (!model) {
    // Fallback to first available model for the tier
    const availableModels = getAvailableModels(userTier);
    return availableModels[0] || MODEL_REGISTRY['gpt-3.5-turbo'];
  }
  
  return model;
}

export function getModelById(id: string): Model | undefined {
  return MODEL_REGISTRY[id];
}

export function getAllModels(): Model[] {
  return Object.values(MODEL_REGISTRY);
}

export function getModelsByProvider(provider: string): Model[] {
  return Object.values(MODEL_REGISTRY).filter(model => model.provider === provider);
}

export function searchModels(query: string): Model[] {
  const lowerQuery = query.toLowerCase();
  return Object.values(MODEL_REGISTRY).filter(model => {
    return (
      model.id.toLowerCase().includes(lowerQuery) ||
      model.name.toLowerCase().includes(lowerQuery) ||
      model.description?.toLowerCase().includes(lowerQuery) ||
      model.provider.toLowerCase().includes(lowerQuery) ||
      model.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  });
}

export function getTopModels(limit: number = 10): Model[] {
  return Object.values(MODEL_REGISTRY)
    .filter(model => model.available || TEST_MODE)
    .sort((a, b) => {
      // Sort by tier priority: enterprise > pro > starter > free
      const tierPriority = { enterprise: 4, pro: 3, starter: 2, free: 1 };
      const aTier = tierPriority[a.tier as keyof typeof tierPriority] || 0;
      const bTier = tierPriority[b.tier as keyof typeof tierPriority] || 0;
      
      if (aTier !== bTier) return bTier - aTier;
      
      // Within same tier, sort by input cost (lower is better)
      return a.inputCost - b.inputCost;
    })
    .slice(0, limit);
}

// Export registry stats for monitoring
export function getRegistryStats() {
  const models = Object.values(MODEL_REGISTRY);
  const availableModels = models.filter(m => m.available);
  
  return {
    totalModels: models.length,
    availableModels: availableModels.length,
    modelsByProvider: models.reduce((acc, model) => {
      acc[model.provider] = (acc[model.provider] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    modelsByTier: models.reduce((acc, model) => {
      const tier = model.tier || 'unassigned';
      acc[tier] = (acc[tier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    averageCost: {
      input: availableModels.reduce((sum, m) => sum + m.inputCost, 0) / availableModels.length,
      output: availableModels.reduce((sum, m) => sum + m.outputCost, 0) / availableModels.length,
    },
  };
}