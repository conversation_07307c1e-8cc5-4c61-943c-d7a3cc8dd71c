# 🚀 Wave 1.9: AWS-Scale MVP - Ship in 24 Hours

**Duration**: 24 hours  
**Priority**: SHIP TODAY WITH TOMORROW'S ARCHITECTURE 🎯  
**Goal**: Magical UI that JUST WORKS, built on AWS foundation

## 🏗️ Day 1 Architecture: Simple Frontend, Scalable Backend

```
┌─────────────────────────────────────────────────────────────┐
│                    Cloudflare (DDoS + CDN)                   │
│                         DNS + WAF                            │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                    Vercel Edge Network                       │
│                 Next.js 15 + React 19                        │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                  AWS API Gateway + WAF                       │
│              Rate Limiting + Request Validation              │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                     Lambda Functions                         │
│  ┌─────────────┐ ┌──────────────┐ ┌──────────────────────┐ │
│  │    Auth     │ │     Chat     │ │   Router Engine      │ │
│  │  Handler    │ │   Handler    │ │   (3 Models)         │ │
│  └─────────────┘ └──────────────┘ └──────────────────────┘ │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                       Data Layer                             │
│  ┌─────────────┐ ┌──────────────┐ ┌──────────────────────┐ │
│  │Aurora MySQL │ │  DynamoDB    │ │    OpenRouter        │ │
│  │(Users/Auth) │ │(Conversations)│ │   (AI Models)        │ │
│  └─────────────┘ └──────────────┘ └──────────────────────┘ │
└──────────────────────────────────────────────────────────────┘
```

## 📋 Day 1 Implementation (Ship Today!)

### Step 1: Future-Proof Database Schema (2 hours)

#### Aurora MySQL Schema - Built for Scale
```sql
-- Users table (Aurora MySQL)
CREATE TABLE users (
  id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255),
  image VARCHAR(500),
  plan ENUM('free', 'starter', 'pro', 'team', 'enterprise') DEFAULT 'free',
  credits INT DEFAULT 10000,
  
  -- Denormalized stats for performance
  total_messages INT DEFAULT 0,
  total_tokens BIGINT DEFAULT 0,
  total_cost DECIMAL(10,6) DEFAULT 0,
  
  -- Metadata
  settings JSON,
  features JSON,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,
  
  -- Indexes
  INDEX idx_email (email),
  INDEX idx_plan (plan),
  INDEX idx_created (created_at),
  INDEX idx_active (last_active_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Projects table (for future teams/folders)
CREATE TABLE projects (
  id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
  user_id CHAR(36) NOT NULL,
  team_id CHAR(36) NULL, -- For future teams
  name VARCHAR(255) NOT NULL,
  description TEXT,
  icon VARCHAR(10) DEFAULT '📁',
  color VARCHAR(7) DEFAULT '#3B82F6',
  sort_order INT DEFAULT 0,
  settings JSON,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_project (user_id, name),
  INDEX idx_user_sort (user_id, sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API Keys table (for future API access)
CREATE TABLE api_keys (
  id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
  user_id CHAR(36) NOT NULL,
  key_hash VARCHAR(64) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  last_used_at TIMESTAMP NULL,
  expires_at TIMESTAMP NULL,
  scopes JSON,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  revoked_at TIMESTAMP NULL,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_key_hash (key_hash),
  INDEX idx_user (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Billing table (for future payments)
CREATE TABLE billing_customers (
  id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
  user_id CHAR(36) UNIQUE NOT NULL,
  stripe_customer_id VARCHAR(255) UNIQUE,
  stripe_subscription_id VARCHAR(255),
  current_period_end TIMESTAMP NULL,
  cancel_at TIMESTAMP NULL,
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_stripe_customer (stripe_customer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### DynamoDB Schema - For Conversations & Messages
```typescript
// DynamoDB Tables Design

// 1. Conversations Table
{
  TableName: 'conversations',
  KeySchema: [
    { AttributeName: 'pk', KeyType: 'HASH' },  // USER#userId
    { AttributeName: 'sk', KeyType: 'RANGE' }  // CONV#timestamp#convId
  ],
  GlobalSecondaryIndexes: [
    {
      IndexName: 'conversation-messages',
      KeySchema: [
        { AttributeName: 'conversation_id', KeyType: 'HASH' },
        { AttributeName: 'created_at', KeyType: 'RANGE' }
      ]
    }
  ],
  AttributeDefinitions: [
    { AttributeName: 'pk', AttributeType: 'S' },
    { AttributeName: 'sk', AttributeType: 'S' },
    { AttributeName: 'conversation_id', AttributeType: 'S' },
    { AttributeName: 'created_at', AttributeType: 'S' }
  ]
}

// Conversation Item Structure
{
  pk: "USER#123",                    // Partition key
  sk: "CONV#2024-01-01#456",        // Sort key
  conversation_id: "456",            // GSI partition key
  user_id: "123",
  project_id: "789",                 // Optional
  title: "Chat about AI",
  message_count: 10,
  total_tokens: 1500,
  total_cost: "0.0045",
  last_model: "gpt-3.5-turbo",
  created_at: "2024-01-01T10:00:00Z",
  updated_at: "2024-01-01T11:00:00Z",
  metadata: {
    starred: false,
    archived: false,
    tags: ["ai", "coding"],
    sharing: null
  }
}

// 2. Messages Table
{
  TableName: 'messages',
  KeySchema: [
    { AttributeName: 'conversation_id', KeyType: 'HASH' },
    { AttributeName: 'created_at', KeyType: 'RANGE' }
  ]
}

// Message Item Structure
{
  conversation_id: "456",
  created_at: "2024-01-01T10:00:00.123Z",
  message_id: "msg-789",
  user_id: "123",
  role: "user",
  content: "How do I build a chat app?",
  model: null,
  tokens: { prompt: 0, completion: 0, total: 0 },
  cost: "0",
  metadata: {
    edited: false,
    feedback: null,
    attachments: []
  }
}

// 3. Usage Analytics Table (Hot partition pattern)
{
  TableName: 'usage_analytics',
  KeySchema: [
    { AttributeName: 'pk', KeyType: 'HASH' },  // USER#userId#YYYY-MM-DD
    { AttributeName: 'sk', KeyType: 'RANGE' }  // timestamp
  ]
}
```

### Step 2: Simple Day 1 Backend (3 hours)

#### Lambda Function for Chat
```typescript
// lambda/chat-handler.ts
import { APIGatewayProxyHandler } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';

const client = new DynamoDBClient({});
const docClient = DynamoDBDocumentClient.from(client);

// Simple router for Day 1 (3 models)
const MODELS = {
  fast: 'gpt-3.5-turbo',
  smart: 'claude-3-haiku-20240307',
  creative: 'gemini-1.5-flash-latest'
};

function selectModel(query: string): string {
  const lower = query.toLowerCase();
  
  if (lower.includes('code') || lower.includes('debug')) {
    return MODELS.smart; // Claude for code
  }
  if (lower.includes('story') || lower.includes('creative')) {
    return MODELS.creative; // Gemini for creative
  }
  return MODELS.fast; // GPT-3.5 default
}

export const handler: APIGatewayProxyHandler = async (event) => {
  const { userId } = event.requestContext.authorizer;
  const { conversationId, message } = JSON.parse(event.body);
  
  // Create conversation if needed
  let convId = conversationId;
  if (!convId) {
    convId = uuidv4();
    await docClient.send(new PutCommand({
      TableName: 'conversations',
      Item: {
        pk: `USER#${userId}`,
        sk: `CONV#${new Date().toISOString()}#${convId}`,
        conversation_id: convId,
        user_id: userId,
        title: message.substring(0, 50) + '...',
        message_count: 0,
        total_tokens: 0,
        total_cost: '0',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metadata: { starred: false, archived: false, tags: [] }
      }
    }));
  }
  
  // Save user message
  await docClient.send(new PutCommand({
    TableName: 'messages',
    Item: {
      conversation_id: convId,
      created_at: new Date().toISOString(),
      message_id: uuidv4(),
      user_id: userId,
      role: 'user',
      content: message,
      tokens: { prompt: 0, completion: 0, total: 0 },
      cost: '0'
    }
  }));
  
  // Select model
  const model = selectModel(message);
  
  // Stream response from OpenRouter
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model,
      messages: [{ role: 'user', content: message }],
      stream: true
    })
  });
  
  // Return streaming response
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'text/event-stream',
      'X-Model-Used': model,
      'X-Conversation-ID': convId
    },
    body: response.body,
    isBase64Encoded: false
  };
};
```

### Step 3: Magical UI That Just Works (4 hours)

#### Main Chat Component
```typescript
// src/app/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sparkles, Send, Plus, Menu } from 'lucide-react';

export default function Home() {
  const { data: session } = useSession();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState(null);
  
  // Beautiful gradient animation
  const gradients = [
    'from-purple-600 to-pink-600',
    'from-blue-600 to-cyan-600',
    'from-green-600 to-teal-600',
  ];
  const [gradientIndex, setGradientIndex] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setGradientIndex((prev) => (prev + 1) % gradients.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);
  
  const sendMessage = async () => {
    if (!message.trim() || isLoading) return;
    
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: message.trim()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId,
          message: userMessage.content
        })
      });
      
      if (!response.ok) throw new Error('Failed to send message');
      
      // Get conversation ID from headers
      const convId = response.headers.get('X-Conversation-ID');
      if (convId) setConversationId(convId);
      
      // Stream response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      let assistantMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: '',
        model: response.headers.get('X-Model-Used')
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.choices?.[0]?.delta?.content) {
                assistantMessage.content += data.choices[0].delta.content;
                setMessages(prev => {
                  const updated = [...prev];
                  updated[updated.length - 1] = { ...assistantMessage };
                  return updated;
                });
              }
            } catch (e) {
              // Ignore parse errors
            }
          }
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!session) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <h1 className="text-6xl font-bold mb-8">
            <span className={`bg-gradient-to-r ${gradients[gradientIndex]} bg-clip-text text-transparent transition-all duration-1000`}>
              JustSimpleChat
            </span>
          </h1>
          <p className="text-gray-400 mb-8">The AI chat that picks the perfect model for you</p>
          <button
            onClick={() => signIn('google')}
            className="px-8 py-3 bg-white text-black rounded-full font-medium hover:scale-105 transition-transform"
          >
            Sign in with Google
          </button>
        </motion.div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-black text-white flex">
      {/* Sidebar */}
      <div className="w-64 bg-gray-900 border-r border-gray-800 p-4 hidden md:block">
        <button className="w-full mb-4 py-2 px-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center gap-2">
          <Plus className="w-4 h-4" />
          New Chat
        </button>
        
        <div className="space-y-2">
          <div className="text-xs text-gray-500 uppercase tracking-wider mb-2">Today</div>
          {/* Conversation list would go here */}
        </div>
      </div>
      
      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-800 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <button className="md:hidden">
              <Menu className="w-5 h-5" />
            </button>
            <h2 className="font-semibold">New Chat</h2>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Sparkles className="w-4 h-4" />
            AI Router Active
          </div>
        </div>
        
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4">
          <AnimatePresence>
            {messages.map((msg) => (
              <motion.div
                key={msg.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`mb-4 ${msg.role === 'user' ? 'text-right' : 'text-left'}`}
              >
                <div className={`inline-block max-w-[80%] ${
                  msg.role === 'user' 
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white' 
                    : 'bg-gray-800 text-gray-100'
                } rounded-2xl px-4 py-2`}>
                  <p className="whitespace-pre-wrap">{msg.content}</p>
                  {msg.model && (
                    <p className="text-xs opacity-70 mt-1">via {msg.model}</p>
                  )}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center gap-2 text-gray-400"
            >
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200" />
              </div>
              Thinking...
            </motion.div>
          )}
        </div>
        
        {/* Input */}
        <div className="border-t border-gray-800 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                placeholder="Ask anything..."
                className="w-full bg-gray-800 text-white rounded-full px-6 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-purple-600 transition-all"
                disabled={isLoading}
              />
              <button
                onClick={sendMessage}
                disabled={!message.trim() || isLoading}
                className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 transition-all"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Step 4: Deploy Day 1 (2 hours)

#### Infrastructure as Code
```yaml
# serverless.yml - Deploy backend to AWS
service: simplechat-api

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  environment:
    OPENROUTER_API_KEY: ${env:OPENROUTER_API_KEY}
    
functions:
  chat:
    handler: lambda/chat-handler.handler
    events:
      - http:
          path: /chat
          method: post
          cors: true
    timeout: 60
    
resources:
  Resources:
    ConversationsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: conversations
        BillingMode: PAY_PER_REQUEST
        KeySchema:
          - AttributeName: pk
            KeyType: HASH
          - AttributeName: sk
            KeyType: RANGE
        AttributeDefinitions:
          - AttributeName: pk
            AttributeType: S
          - AttributeName: sk
            AttributeType: S
    
    MessagesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: messages
        BillingMode: PAY_PER_REQUEST
        KeySchema:
          - AttributeName: conversation_id
            KeyType: HASH
          - AttributeName: created_at
            KeyType: RANGE
        AttributeDefinitions:
          - AttributeName: conversation_id
            AttributeType: S
          - AttributeName: created_at
            AttributeType: S
```

#### Deploy Commands
```bash
# Backend
npm install -g serverless
serverless deploy

# Frontend
vercel --prod

# Setup Cloudflare
# 1. Add domain to Cloudflare
# 2. Enable DDoS protection
# 3. Setup WAF rules
# 4. Point to Vercel
```

## ✅ Day 1 Deliverables

1. **Beautiful UI** that feels magical ✨
2. **3 AI Models** with smart routing
3. **Google Sign-in** only (simple)
4. **AWS Backend** ready for scale
5. **DynamoDB** for conversations
6. **Aurora MySQL** for users (ready)
7. **Cloudflare** protection
8. **Live at justsimple.chat**

## 📈 Day 2+ Improvements

- **Day 2**: Add Redis caching, more models
- **Day 3**: Conversation history UI
- **Day 4**: Usage analytics dashboard  
- **Day 5**: Team workspaces
- **Day 6**: Payment integration
- **Day 7**: 20+ models

## 🎯 Why This Architecture Wins

1. **Looks Amazing** - Premium feel from day 1
2. **Just Works** - No complexity for users
3. **Scales to Millions** - AWS infrastructure
4. **Future-Proof** - Database schema ready
5. **Fast Iteration** - Can improve daily

**Ship today, scale tomorrow! 🚀**