# 🗄️ Wave 1.8: Database-First Day 1 MVP

**Duration**: Day 1 (24 hours)  
**Priority**: CRITICAL FOUNDATION 🏗️  
**Goal**: Ship MVP with proper database from hour 1

## 🎯 Why Database on Day 1 is Non-Negotiable

1. **Data Integrity**: No lost conversations when users refresh
2. **Multi-Device**: Users expect to access chats from anywhere
3. **Analytics**: Can't improve what we don't measure
4. **Monetization**: Need usage tracking for billing from day 1
5. **Trust**: Professional product = persistent data

## 📋 Revised Day 1 Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Vercel Edge Network                       │
│                   (Global CDN + Functions)                   │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                    Next.js 15 App                            │
│            (React Server Components + Edge API)              │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                     Supabase Stack                           │
│  ┌─────────────┐ ┌──────────────┐ ┌──────────────────────┐ │
│  │ PostgreSQL  │ │ Auth Service │ │  Realtime (WebSocket)│ │
│  │   (Prisma)  │ │   (OAuth)    │ │  (Live Updates)      │ │
│  └─────────────┘ └──────────────┘ └──────────────────────┘ │
└─────────────────────┬───────────────────┬───────────────────┘
                      │                   │
┌─────────────────────▼───────────────────▼───────────────────┐
│                    AI Layer (OpenRouter)                     │
│               15+ Models with Smart Routing                  │
└──────────────────────────────────────────────────────────────┘
```

## 🚀 Implementation Plan (24 Hours)

### Hour 1-2: Database & Auth Setup

#### Step 1: Supabase Project Setup (30 min)
```bash
# 1. Create Supabase project at supabase.com
# 2. Get connection string and anon key
# 3. Install dependencies
npm install @supabase/supabase-js @prisma/client prisma
npm install -D @types/node

# 4. Initialize Prisma with Supabase
npx prisma init
```

#### Step 2: Database Schema (45 min)
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String   @id @default(uuid())
  email         String   @unique
  name          String?
  image         String?
  credits       Int      @default(10000) // Start with 10k credits
  
  conversations Conversation[]
  messages      Message[]
  
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  lastSeen      DateTime @default(now())
  
  @@index([email])
  @@map("users")
}

model Conversation {
  id            String   @id @default(uuid())
  userId        String
  title         String   @default("New Chat")
  
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages      Message[]
  
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  @@index([userId, updatedAt(sort: Desc)])
  @@map("conversations")
}

model Message {
  id             String   @id @default(uuid())
  conversationId String
  userId         String
  role           String   // 'user' | 'assistant' | 'system'
  content        String   @db.Text
  model          String?  // Which AI model was used
  tokens         Int      @default(0)
  cost           Decimal  @default(0) @db.Decimal(10, 6)
  
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  createdAt      DateTime @default(now())
  
  @@index([conversationId, createdAt])
  @@map("messages")
}

model UsageLog {
  id        String   @id @default(uuid())
  userId    String
  action    String   // 'chat', 'search', 'generation'
  tokens    Int
  cost      Decimal  @db.Decimal(10, 6)
  model     String
  metadata  Json?
  
  createdAt DateTime @default(now())
  
  @@index([userId, createdAt])
  @@index([createdAt])
  @@map("usage_logs")
}
```

#### Step 3: Supabase Auth Setup (45 min)
```typescript
// src/lib/supabase/client.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});
```

```typescript
// src/lib/supabase/auth.ts
import { supabase } from './client';

export async function signInWithGoogle() {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/auth/callback`,
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    },
  });
  
  if (error) throw error;
  return data;
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
}

export async function getSession() {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) throw error;
  return session;
}
```

### Hour 3-4: Core Database Operations

#### Database Client with Connection Pooling
```typescript
// src/lib/db/client.ts
import { PrismaClient } from '@prisma/client';

const globalForPrisma = global as unknown as { prisma: PrismaClient };

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  });

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Connection pool settings for scale
prisma.$connect().then(() => {
  console.log('Database connected successfully');
});
```

#### Conversation Service
```typescript
// src/lib/services/conversation.service.ts
import { prisma } from '@/lib/db/client';
import { Conversation, Message } from '@prisma/client';

export class ConversationService {
  static async create(userId: string, title?: string): Promise<Conversation> {
    return prisma.conversation.create({
      data: {
        userId,
        title: title || 'New Chat',
      },
    });
  }
  
  static async list(userId: string, limit = 20): Promise<Conversation[]> {
    return prisma.conversation.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
      take: limit,
      select: {
        id: true,
        title: true,
        updatedAt: true,
        _count: {
          select: { messages: true },
        },
      },
    });
  }
  
  static async get(id: string, userId: string): Promise<Conversation & { messages: Message[] }> {
    const conversation = await prisma.conversation.findFirst({
      where: { id, userId },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
        },
      },
    });
    
    if (!conversation) {
      throw new Error('Conversation not found');
    }
    
    return conversation;
  }
  
  static async addMessage(
    conversationId: string,
    userId: string,
    content: string,
    role: 'user' | 'assistant',
    metadata?: {
      model?: string;
      tokens?: number;
      cost?: number;
    }
  ): Promise<Message> {
    // Use transaction for consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create message
      const message = await tx.message.create({
        data: {
          conversationId,
          userId,
          content,
          role,
          model: metadata?.model,
          tokens: metadata?.tokens || 0,
          cost: metadata?.cost || 0,
        },
      });
      
      // Update conversation timestamp
      await tx.conversation.update({
        where: { id: conversationId },
        data: { updatedAt: new Date() },
      });
      
      // Log usage if assistant message
      if (role === 'assistant' && metadata?.tokens) {
        await tx.usageLog.create({
          data: {
            userId,
            action: 'chat',
            tokens: metadata.tokens,
            cost: metadata.cost || 0,
            model: metadata.model || 'unknown',
            metadata: { conversationId },
          },
        });
        
        // Update user credits
        await tx.user.update({
          where: { id: userId },
          data: {
            credits: {
              decrement: metadata.tokens,
            },
          },
        });
      }
      
      return message;
    });
    
    return result;
  }
  
  static async updateTitle(id: string, userId: string, title: string): Promise<void> {
    await prisma.conversation.update({
      where: { id, userId },
      data: { title },
    });
  }
  
  static async delete(id: string, userId: string): Promise<void> {
    await prisma.conversation.delete({
      where: { id, userId },
    });
  }
}
```

### Hour 5-6: Smart Router with Database

#### Router with Usage Tracking
```typescript
// src/lib/ai/router-with-db.ts
import { prisma } from '@/lib/db/client';

interface RouterConfig {
  models: ModelConfig[];
  userId: string;
  conversationId: string;
}

interface ModelConfig {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google';
  costPerKToken: number;
  speed: 'fast' | 'medium' | 'slow';
  capabilities: string[];
}

export class DatabaseRouter {
  private models: ModelConfig[] = [
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: 'openai',
      costPerKToken: 0.002,
      speed: 'fast',
      capabilities: ['general', 'code', 'creative'],
    },
    {
      id: 'claude-3-haiku',
      name: 'Claude 3 Haiku',
      provider: 'anthropic',
      costPerKToken: 0.0008,
      speed: 'fast',
      capabilities: ['general', 'code', 'analysis'],
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'google',
      costPerKToken: 0.0007,
      speed: 'medium',
      capabilities: ['general', 'multimodal', 'long-context'],
    },
  ];
  
  async selectModel(
    query: string,
    userId: string,
    options?: { preferredModel?: string }
  ): Promise<{ model: ModelConfig; reasoning: string }> {
    // Check user preferences
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { credits: true },
    });
    
    if (!user || user.credits <= 0) {
      throw new Error('Insufficient credits');
    }
    
    // Simple routing logic for MVP
    const queryLower = query.toLowerCase();
    const wordCount = query.split(' ').length;
    
    // Override with preferred model if specified
    if (options?.preferredModel) {
      const preferred = this.models.find(m => m.id === options.preferredModel);
      if (preferred) {
        return {
          model: preferred,
          reasoning: 'User preferred model',
        };
      }
    }
    
    // Route based on query characteristics
    if (queryLower.includes('code') || queryLower.includes('debug') || queryLower.includes('function')) {
      return {
        model: this.models.find(m => m.id === 'claude-3-haiku')!,
        reasoning: 'Code-related query - Claude excels at programming',
      };
    }
    
    if (wordCount > 100) {
      return {
        model: this.models.find(m => m.id === 'gemini-1.5-flash')!,
        reasoning: 'Long context query - Gemini handles long text well',
      };
    }
    
    if (queryLower.includes('creative') || queryLower.includes('story') || queryLower.includes('write')) {
      return {
        model: this.models.find(m => m.id === 'gpt-3.5-turbo')!,
        reasoning: 'Creative task - GPT-3.5 is great for creative writing',
      };
    }
    
    // Default to cheapest fast model
    return {
      model: this.models.find(m => m.id === 'gemini-1.5-flash')!,
      reasoning: 'General query - using most cost-effective model',
    };
  }
  
  async trackUsage(
    userId: string,
    model: string,
    tokens: number,
    cost: number
  ): Promise<void> {
    await prisma.usageLog.create({
      data: {
        userId,
        action: 'chat',
        model,
        tokens,
        cost,
      },
    });
  }
}
```

### Hour 7-8: API with Database Integration

#### Streaming Chat API
```typescript
// src/app/api/chat/route.ts
import { NextRequest } from 'next/server';
import { getServerSession } from '@/lib/supabase/auth';
import { DatabaseRouter } from '@/lib/ai/router-with-db';
import { ConversationService } from '@/lib/services/conversation.service';
import { OpenRouterClient } from '@/lib/ai/openrouter';

export async function POST(request: NextRequest) {
  try {
    // 1. Auth check
    const session = await getServerSession();
    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    // 2. Parse request
    const { conversationId, message } = await request.json();
    
    // 3. Create or get conversation
    let convId = conversationId;
    if (!convId) {
      const conv = await ConversationService.create(session.user.id);
      convId = conv.id;
    }
    
    // 4. Add user message to DB
    await ConversationService.addMessage(
      convId,
      session.user.id,
      message,
      'user'
    );
    
    // 5. Route to best model
    const router = new DatabaseRouter();
    const { model, reasoning } = await router.selectModel(
      message,
      session.user.id
    );
    
    // 6. Stream response
    const client = new OpenRouterClient();
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Send metadata first
          controller.enqueue(encoder.encode(
            `data: ${JSON.stringify({
              type: 'metadata',
              model: model.id,
              reasoning,
              conversationId: convId,
            })}\n\n`
          ));
          
          let fullResponse = '';
          let tokenCount = 0;
          
          // Stream from AI
          await client.streamChat({
            model: model.id,
            messages: [{ role: 'user', content: message }],
            onChunk: (chunk) => {
              fullResponse += chunk;
              tokenCount += Math.ceil(chunk.length / 4); // Rough estimate
              
              controller.enqueue(encoder.encode(
                `data: ${JSON.stringify({
                  type: 'chunk',
                  content: chunk,
                })}\n\n`
              ));
            },
          });
          
          // Save assistant message to DB
          const cost = (tokenCount / 1000) * model.costPerKToken;
          await ConversationService.addMessage(
            convId,
            session.user.id,
            fullResponse,
            'assistant',
            {
              model: model.id,
              tokens: tokenCount,
              cost,
            }
          );
          
          // Send completion
          controller.enqueue(encoder.encode(
            `data: ${JSON.stringify({
              type: 'done',
              tokens: tokenCount,
              cost,
            })}\n\n`
          ));
          
          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });
    
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
    
  } catch (error) {
    console.error('Chat error:', error);
    return new Response('Internal error', { status: 500 });
  }
}
```

### Hour 9-10: UI with Database Integration

#### Chat UI with Real Data
```typescript
// src/components/chat/database-chat.tsx
'use client';

import { useEffect, useState } from 'react';
import { useSupabase } from '@/hooks/use-supabase';
import { ConversationList } from './conversation-list';
import { ChatMessages } from './chat-messages';
import { ChatInput } from './chat-input';

export function DatabaseChat() {
  const { user } = useSupabase();
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Load conversations
  useEffect(() => {
    if (user) {
      loadConversations();
    }
  }, [user]);
  
  // Load messages when conversation changes
  useEffect(() => {
    if (activeConversation) {
      loadMessages(activeConversation.id);
    }
  }, [activeConversation]);
  
  const loadConversations = async () => {
    const res = await fetch('/api/conversations');
    const data = await res.json();
    setConversations(data);
    
    // Select first conversation or create new
    if (data.length > 0) {
      setActiveConversation(data[0]);
    } else {
      createNewConversation();
    }
  };
  
  const loadMessages = async (conversationId: string) => {
    const res = await fetch(`/api/conversations/${conversationId}`);
    const data = await res.json();
    setMessages(data.messages);
  };
  
  const createNewConversation = async () => {
    const res = await fetch('/api/conversations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ title: 'New Chat' }),
    });
    const data = await res.json();
    setActiveConversation(data);
    setMessages([]);
    await loadConversations();
  };
  
  const sendMessage = async (content: string) => {
    if (!activeConversation || isLoading) return;
    
    setIsLoading(true);
    
    // Optimistic update
    const tempMessage = {
      id: `temp-${Date.now()}`,
      role: 'user',
      content,
      createdAt: new Date(),
    };
    setMessages(prev => [...prev, tempMessage]);
    
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId: activeConversation.id,
          message: content,
        }),
      });
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      let assistantMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: '',
        model: '',
        createdAt: new Date(),
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'metadata') {
                assistantMessage.model = data.model;
              } else if (data.type === 'chunk') {
                assistantMessage.content += data.content;
                setMessages(prev => {
                  const updated = [...prev];
                  updated[updated.length - 1] = { ...assistantMessage };
                  return updated;
                });
              }
            } catch (e) {
              // Ignore parse errors
            }
          }
        }
      }
      
      // Reload to get real IDs from database
      await loadMessages(activeConversation.id);
      
    } catch (error) {
      console.error('Send message error:', error);
      // Remove optimistic messages on error
      setMessages(prev => prev.slice(0, -2));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <div className="w-64 border-r bg-gray-50 dark:bg-gray-900">
        <div className="p-4">
          <button
            onClick={createNewConversation}
            className="w-full py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            New Chat
          </button>
        </div>
        <ConversationList
          conversations={conversations}
          activeId={activeConversation?.id}
          onSelect={setActiveConversation}
        />
      </div>
      
      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        <ChatMessages messages={messages} />
        <ChatInput
          onSend={sendMessage}
          isLoading={isLoading}
          disabled={!activeConversation}
        />
      </div>
    </div>
  );
}
```

### Hour 11-12: Deployment & Monitoring

#### Database Migrations
```bash
# Create migration
npx prisma migrate dev --name init

# Deploy to production
npx prisma migrate deploy
```

#### Environment Variables
```env
# .env.local
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."
NEXT_PUBLIC_SUPABASE_URL="https://xxx.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="xxx"
OPENROUTER_API_KEY="xxx"
```

#### Monitoring Setup
```typescript
// src/lib/monitoring/metrics.ts
import { prisma } from '@/lib/db/client';

export class Metrics {
  static async track(event: string, properties?: any) {
    // Send to analytics service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', event, properties);
    }
    
    // Also log to database for internal analytics
    if (event.startsWith('api.')) {
      await prisma.usageLog.create({
        data: {
          userId: properties?.userId || 'anonymous',
          action: event,
          tokens: 0,
          cost: 0,
          model: 'system',
          metadata: properties,
        },
      });
    }
  }
  
  static async getDailyStats(userId: string) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const stats = await prisma.usageLog.aggregate({
      where: {
        userId,
        createdAt: { gte: today },
      },
      _sum: {
        tokens: true,
        cost: true,
      },
      _count: true,
    });
    
    return {
      messagestoday: stats._count,
      tokensToday: stats._sum.tokens || 0,
      costToday: stats._sum.cost || 0,
    };
  }
}
```

## ✅ Day 1 Database-First Deliverables

1. **PostgreSQL Database** via Supabase (free tier)
2. **User Authentication** with Google OAuth
3. **Conversation Persistence** - Never lose a chat
4. **Message History** - Full conversation tracking
5. **Usage Tracking** - Every token counted
6. **Credit System** - 10k free credits to start
7. **Real-time Updates** - Via Supabase subscriptions
8. **Analytics Ready** - Usage logs from day 1
9. **Multi-device Sync** - Works everywhere
10. **Professional Foundation** - Ready to scale

## 🎯 Why This Architecture Scales

1. **Supabase** = PostgreSQL + Auth + Realtime + Storage
2. **Prisma** = Type-safe queries + Migrations
3. **Edge Functions** = Global low latency
4. **Connection Pooling** = Handles 1000s of users
5. **Indexed Queries** = Fast even with millions of messages
6. **Usage Tracking** = Built-in analytics & billing

## 🚀 Next Steps After Day 1

- **Day 2**: Add caching layer (Redis)
- **Day 3**: Implement full-text search
- **Day 4**: Add team workspaces
- **Day 5**: Payment integration
- **Day 6**: Advanced analytics
- **Day 7**: Performance optimization

**With database from Day 1, we build on rock, not sand! 🪨**