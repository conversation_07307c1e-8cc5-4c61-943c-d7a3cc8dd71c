{"sources": {"lmsys-arena": {"name": "LMSYS Chatbot Arena", "url": "https://huggingface.co/spaces/lmsys/chatbot-arena-leaderboard", "tier": 1, "frequency": "daily", "categories": ["general_chat"], "dataFormat": "elo", "extraction": {"method": "firecrawl", "selector": "table", "columns": {"model": 0, "elo": 1}}, "confidence": "high", "enabled": true}, "arc-agi": {"name": "ARC-AGI Leaderboard", "url": "https://arcprize.org/leaderboard", "tier": 1, "frequency": "daily", "categories": ["reasoning"], "dataFormat": "percentage", "extraction": {"method": "firecrawl", "patterns": {"model": "| ([^|]+) |", "score": "| ([0-9.]+)% |", "cost": "\\$([0-9.]+)"}}, "confidence": "high", "enabled": true}, "huggingface-open-llm": {"name": "Hugging Face Open LLM Leaderboard", "url": "https://huggingface.co/spaces/open-llm-leaderboard/open_llm_leaderboard", "tier": 1, "frequency": "daily", "categories": ["general_chat", "reasoning", "factual_qa"], "dataFormat": "aggregate", "extraction": {"method": "firecrawl", "api": "https://huggingface.co/api/spaces/open-llm-leaderboard/open_llm_leaderboard/gradio/", "components": ["ARC", "HellaSwag", "MMLU", "TruthfulQA"]}, "confidence": "high", "enabled": true}, "livecodebench": {"name": "LiveCodeBench", "url": "https://livecodebench.github.io/leaderboard.html", "tier": 2, "frequency": "weekly", "categories": ["coding", "debugging"], "dataFormat": "percentage", "extraction": {"method": "firecrawl", "fallback": "perplexity"}, "confidence": "high", "enabled": true}, "humaneval": {"name": "HumanEval", "url": null, "tier": 2, "frequency": "weekly", "categories": ["coding"], "dataFormat": "pass@k", "extraction": {"method": "perplexity", "query": "latest HumanEval benchmark results pass@1 scores 2024"}, "confidence": "high", "enabled": true}, "helm": {"name": "HELM (Holistic Evaluation)", "url": "https://crfm.stanford.edu/helm/latest/", "tier": 2, "frequency": "weekly", "categories": ["general_chat", "reasoning", "coding", "summarization", "factual_qa"], "dataFormat": "multi-metric", "extraction": {"method": "firecrawl", "api": "https://crfm.stanford.edu/helm/latest/benchmark_output/runs.json"}, "confidence": "high", "enabled": true}, "mmlu": {"name": "MMLU (Massive Multitask Language Understanding)", "url": null, "tier": 2, "frequency": "weekly", "categories": ["factual_qa", "reasoning", "academic_writing", "scientific", "medical", "legal"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "MMLU benchmark latest results by model 2024"}, "confidence": "high", "enabled": true}, "gsm8k": {"name": "GSM8K (Grade School Math)", "url": null, "tier": 2, "frequency": "weekly", "categories": ["math"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "GSM8K math benchmark latest model scores 2024"}, "confidence": "high", "enabled": true}, "truthfulqa": {"name": "TruthfulQA", "url": null, "tier": 2, "frequency": "weekly", "categories": ["factual_qa", "analysis"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "TruthfulQA benchmark latest results 2024"}, "confidence": "high", "enabled": true}, "hellaswag": {"name": "HellaSwag", "url": null, "tier": 2, "frequency": "weekly", "categories": ["reasoning", "general_chat"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "HellaSwag commonsense reasoning benchmark latest scores"}, "confidence": "medium", "enabled": true}, "bigcodebench": {"name": "BigCodeBench", "url": null, "tier": 2, "frequency": "weekly", "categories": ["coding", "debugging"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "BigCodeBench 2024 results multi-language coding benchmark"}, "confidence": "medium", "enabled": true}, "medqa": {"name": "MedQA", "url": null, "tier": 3, "frequency": "monthly", "categories": ["medical"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "MedQA medical question answering benchmark results 2024"}, "confidence": "medium", "enabled": true}, "legalbench": {"name": "LegalBench", "url": null, "tier": 3, "frequency": "monthly", "categories": ["legal"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "LegalBench legal reasoning benchmark latest scores"}, "confidence": "medium", "enabled": true}, "finqa": {"name": "FinQA", "url": null, "tier": 3, "frequency": "monthly", "categories": ["financial", "math", "analysis"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "FinQA financial reasoning benchmark results 2024"}, "confidence": "medium", "enabled": true}, "winogrande": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": null, "tier": 3, "frequency": "monthly", "categories": ["reasoning"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "WinoGrande commonsense reasoning benchmark scores"}, "confidence": "medium", "enabled": true}, "superglue": {"name": "SuperGLUE", "url": null, "tier": 3, "frequency": "monthly", "categories": ["general_chat", "reasoning", "analysis"], "dataFormat": "aggregate", "extraction": {"method": "perplexity", "query": "SuperGLUE benchmark suite latest model performances"}, "confidence": "medium", "enabled": true}, "wmt": {"name": "WMT Translation", "url": null, "tier": 3, "frequency": "monthly", "categories": ["translation"], "dataFormat": "bleu", "extraction": {"method": "perplexity", "query": "WMT translation benchmark BLEU scores latest models"}, "confidence": "medium", "enabled": true}, "cnndm": {"name": "CNN/DailyMail Summarization", "url": null, "tier": 3, "frequency": "monthly", "categories": ["summarization"], "dataFormat": "rouge", "extraction": {"method": "perplexity", "query": "CNN DailyMail summarization benchmark ROUGE scores 2024"}, "confidence": "medium", "enabled": true}, "swebench": {"name": "SWE-bench", "url": null, "tier": 3, "frequency": "monthly", "categories": ["coding", "debugging"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "SWE-bench software engineering benchmark latest results"}, "confidence": "medium", "enabled": true}, "mbpp": {"name": "MBPP (Mostly Basic Python Problems)", "url": null, "tier": 3, "frequency": "monthly", "categories": ["coding"], "dataFormat": "pass@k", "extraction": {"method": "perplexity", "query": "MBPP Python coding benchmark pass@1 scores 2024"}, "confidence": "medium", "enabled": true}, "mathvista": {"name": "MathVista", "url": null, "tier": 3, "frequency": "monthly", "categories": ["math", "multimodal"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "MathVista mathematical reasoning with vision benchmark scores"}, "confidence": "medium", "enabled": true}, "mmmu": {"name": "MMMU (Multimodal)", "url": null, "tier": 3, "frequency": "monthly", "categories": ["multimodal", "image_analysis"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "MMMU multimodal understanding benchmark latest scores 2024"}, "confidence": "high", "enabled": true}, "vqa": {"name": "VQAv2", "url": null, "tier": 3, "frequency": "monthly", "categories": ["image_analysis", "multimodal"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "VQAv2 visual question answering benchmark results 2024"}, "confidence": "medium", "enabled": true}, "bbq": {"name": "BBQ (Bias <PERSON>)", "url": null, "tier": 3, "frequency": "monthly", "categories": ["analysis", "factual_qa"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "BBQ bias detection benchmark latest model scores"}, "confidence": "medium", "enabled": true}, "bioasq": {"name": "BioASQ", "url": null, "tier": 3, "frequency": "monthly", "categories": ["medical", "scientific"], "dataFormat": "percentage", "extraction": {"method": "perplexity", "query": "BioASQ biomedical question answering benchmark 2024"}, "confidence": "medium", "enabled": true}}, "modelMappings": {"GPT-4o-latest": ["openai/gpt-4o-latest", "gpt-4o-latest", "GPT-4o (Latest)"], "GPT-4-Turbo": ["openai/gpt-4-turbo", "gpt-4-turbo", "GPT-4 Turbo"], "GPT-4": ["openai/gpt-4", "gpt-4", "GPT-4 (March 2023)"], "GPT-3.5-Turbo": ["openai/gpt-3.5-turbo", "gpt-3.5-turbo", "GPT-3.5 Turbo"], "Claude-3.5-Sonnet": ["anthropic/claude-3-5-sonnet-20241022", "claude-3-5-sonnet", "Claude 3.5 Sonnet"], "Claude-3-Opus": ["anthropic/claude-3-opus", "claude-3-opus", "Claude 3 Opus"], "Claude-3-Haiku": ["anthropic/claude-3-haiku", "claude-3-haiku", "Claude 3 Haiku"], "Gemini-2.5-Pro": ["gemini/gemini-2.5-pro", "gemini-2.5-pro", "Gemini 2.5 Pro"], "Gemini-2.5-Flash": ["gemini/gemini-2.5-flash", "gemini-2.5-flash", "Gemini 2.5 Flash"], "Gemini-1.5-Pro": ["gemini/gemini-1.5-pro", "gemini-1.5-pro", "Gemini 1.5 Pro"], "Llama-3-70B": ["meta/llama-3-70b-instruct", "llama-3-70b", "Llama 3 70B Instruct"], "Mixtral-8x7B": ["mistralai/mixtral-8x7b-instruct", "mixtral-8x7b", "Mixtral 8x7B"], "o1-preview": ["openai/o1-preview", "o1-preview", "O1 Preview"], "o1-mini": ["openai/o1-mini", "o1-mini", "O1 Mini"], "o3-mini": ["openai/o3-mini", "o3-mini", "O3 Mini"], "DeepSeek-V2": ["deepseek/deepseek-chat", "deepseek-v2", "DeepSeek V2"], "Qwen-2-72B": ["qwen/qwen-2-72b-instruct", "qwen2-72b", "<PERSON><PERSON> 2 72B"], "Command-R-Plus": ["cohere/command-r-plus", "command-r-plus", "Command R+"], "Grok-4": ["x-ai/grok-4", "grok-4", "Grok 4 Thinking"]}, "scoreNormalization": {"elo": {"min": 900, "max": 1500, "formula": "(score - 900) / 6"}, "percentage": {"min": 0, "max": 100, "formula": "score"}, "pass@k": {"min": 0, "max": 100, "formula": "score"}, "bleu": {"min": 0, "max": 100, "formula": "score"}, "rouge": {"min": 0, "max": 100, "formula": "score"}}, "collectionSchedule": {"tier1": {"cron": "0 6 * * *", "description": "Daily at 6 AM UTC"}, "tier2": {"cron": "0 2 * * 0", "description": "Weekly on Sundays at 2 AM UTC"}, "tier3": {"cron": "0 3 1 * *", "description": "Monthly on 1st at 3 AM UTC"}}}