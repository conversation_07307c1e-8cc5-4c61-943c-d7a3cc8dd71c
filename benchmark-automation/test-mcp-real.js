#!/usr/bin/env node

/**
 * Quick MCP Test - Test real Firecrawl and Perplexity servers
 * This script tests the MCP servers are working correctly
 */

console.log('🧪 Testing Real MCP Servers');
console.log('📅 Date: July 17, 2025\n');

async function testFirecrawl() {
  console.log('1️⃣ Testing Firecrawl MCP...');
  
  try {
    // Test 1: LMSYS Arena
    console.log('\n   Testing LMSYS Arena leaderboard...');
    const lmsysResult = await mcp__firecrawl__firecrawl_scrape({
      url: 'https://lmarena.ai/?leaderboard',
      formats: ['markdown'],
      onlyMainContent: true,
      waitFor: 5000,
      timeout: 30000
    });
    
    if (lmsysResult.markdown) {
      console.log(`   ✅ Success! Retrieved ${lmsysResult.markdown.length} characters`);
      
      // Check for expected content
      if (lmsysResult.markdown.includes('Arena') || lmsysResult.markdown.includes('Elo')) {
        console.log('   ✅ Content looks valid (found Arena/Elo keywords)');
        
        // Extract a sample
        const lines = lmsysResult.markdown.split('\n').slice(0, 10);
        console.log('\n   First 10 lines:');
        lines.forEach(line => console.log(`   ${line.substring(0, 80)}${line.length > 80 ? '...' : ''}`));
      }
    }
    
    // Test 2: ARC-AGI Leaderboard
    console.log('\n   Testing ARC-AGI leaderboard...');
    const arcResult = await mcp__firecrawl__firecrawl_scrape({
      url: 'https://arcprize.org/leaderboard',
      formats: ['markdown'],
      onlyMainContent: true,
      waitFor: 3000
    });
    
    if (arcResult.markdown) {
      console.log(`   ✅ Success! Retrieved ${arcResult.markdown.length} characters`);
      
      if (arcResult.markdown.includes('ARC') || arcResult.markdown.includes('%')) {
        console.log('   ✅ Content looks valid (found ARC/percentage keywords)');
      }
    }
    
  } catch (error) {
    console.error('   ❌ Firecrawl test failed:', error.message);
    return false;
  }
  
  return true;
}

async function testPerplexity() {
  console.log('\n2️⃣ Testing Perplexity MCP...');
  
  try {
    console.log('\n   Searching for latest AI benchmarks...');
    const result = await mcp__perplexity-ask__search({
      query: 'latest AI model benchmarks July 2025 LMSYS Arena leaderboard results'
    });
    
    if (result) {
      console.log('   ✅ Success! Got response from Perplexity');
      
      // Show first part of response
      const preview = typeof result === 'string' ? result : JSON.stringify(result);
      console.log(`\n   Response preview: ${preview.substring(0, 200)}...`);
    }
    
  } catch (error) {
    console.error('   ❌ Perplexity test failed:', error.message);
    return false;
  }
  
  return true;
}

async function extractScoresFromMarkdown(markdown, source) {
  console.log(`\n3️⃣ Extracting scores from ${source}...`);
  
  const scores = [];
  const lines = markdown.split('\n');
  
  // Look for table rows with model names and scores
  for (const line of lines) {
    // LMSYS pattern: | Model Name | Elo |
    if (source === 'LMSYS' && line.includes('|')) {
      const parts = line.split('|').map(p => p.trim()).filter(p => p);
      if (parts.length >= 2 && /\d{4}/.test(parts[1])) {
        scores.push({
          model: parts[0],
          score: parseInt(parts[1]),
          type: 'elo'
        });
      }
    }
    
    // ARC-AGI pattern: | Model | Score% |
    if (source === 'ARC-AGI' && line.includes('|')) {
      const parts = line.split('|').map(p => p.trim()).filter(p => p);
      if (parts.length >= 2 && /\d+(\.\d+)?%?/.test(parts[1])) {
        const scoreStr = parts[1].replace('%', '');
        scores.push({
          model: parts[0],
          score: parseFloat(scoreStr),
          type: 'percentage'
        });
      }
    }
  }
  
  console.log(`   Found ${scores.length} scores`);
  
  // Show first 5
  if (scores.length > 0) {
    console.log('\n   Sample scores:');
    scores.slice(0, 5).forEach(s => {
      console.log(`   - ${s.model}: ${s.score}${s.type === 'percentage' ? '%' : ''}`);
    });
  }
  
  return scores;
}

// Main test function
async function runTests() {
  console.log('Starting MCP server tests...\n');
  
  const results = {
    firecrawl: false,
    perplexity: false
  };
  
  // Test Firecrawl
  results.firecrawl = await testFirecrawl();
  
  // Test Perplexity
  results.perplexity = await testPerplexity();
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Firecrawl MCP: ${results.firecrawl ? '✅ Working' : '❌ Failed'}`);
  console.log(`Perplexity MCP: ${results.perplexity ? '✅ Working' : '❌ Failed'}`);
  
  if (results.firecrawl && results.perplexity) {
    console.log('\n✅ All MCP servers are working correctly!');
    console.log('\nNext steps:');
    console.log('1. Run full source testing: node test-all-sources.js');
    console.log('2. Implement hourly collection schedule');
    console.log('3. Test database updates with real data');
  } else {
    console.log('\n❌ Some MCP servers are not working.');
    console.log('Please check MCP server configuration.');
  }
}

// Run tests
runTests().catch(console.error);