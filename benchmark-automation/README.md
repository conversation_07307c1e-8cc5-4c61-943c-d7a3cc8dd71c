# Benchmark Automation System

Automated collection of AI model benchmark scores from 25+ sources using MCP servers.

## Architecture

```
benchmark-automation/
├── collectors/          # Collection scripts (daily, weekly, monthly)
├── processors/          # Data processing components
│   ├── mcp-client.js   # MCP server integration (Firecrawl, Perplexity)
│   ├── data-normalizer.js  # Score normalization pipeline
│   └── model-mapper.js     # Model name mapping with fuzzy matching
├── utils/              # Utility components
│   └── database-updater.js # Database updates with confidence scoring
├── config/             # Configuration
│   └── benchmark-sources.json # 25+ benchmark sources configuration
├── logs/               # Audit logs and reports
└── cache/              # Request cache for performance
```

## Features

- **25+ Benchmark Sources**: LMSYS Arena, ARC-AGI, HumanEval, HELM, MMLU, etc.
- **MCP Server Integration**: Firecrawl for scraping, Perplexity for research
- **Intelligent Updates**: Confidence scoring, freshness tracking, conflict resolution
- **Model Name Mapping**: Fuzzy matching for 200+ model variations
- **Transaction Safety**: Atomic updates with rollback capability
- **Cache Management**: Redis cache invalidation after updates
- **Audit Trail**: Complete logging of all changes

## Setup

1. Install dependencies:
```bash
cd /var/www/dev/benchmark-automation
npm install
```

2. Configure database credentials in component files if needed

3. Test components:
```bash
npm run test-mcp        # Test MCP client
npm run test-normalizer # Test data normalizer
npm run test-mapper     # Test model mapper
npm run test-updater    # Test database updater
```

## Usage

### Manual Collection

Run daily collection (Tier 1 sources):
```bash
npm run daily
```

### Automated Collection (Cron)

Add to crontab:
```bash
# Daily at 6 AM UTC - High priority benchmarks
0 6 * * * cd /var/www/dev/benchmark-automation && npm run daily

# Weekly on Sundays at 2 AM UTC - Academic benchmarks
0 2 * * 0 cd /var/www/dev/benchmark-automation && npm run weekly

# Monthly on 1st at 3 AM UTC - Specialized benchmarks
0 3 1 * * cd /var/www/dev/benchmark-automation && npm run monthly
```

## Benchmark Sources

### Tier 1 (Daily)
- LMSYS Chatbot Arena - Elo ratings for general_chat
- ARC-AGI Leaderboard - Reasoning scores
- Hugging Face Open LLM - Aggregate scores

### Tier 2 (Weekly)
- LiveCodeBench - Coding benchmarks
- HumanEval - Pass@k coding scores
- HELM - Holistic evaluation
- MMLU - Academic knowledge
- GSM8K - Math reasoning
- TruthfulQA - Factual accuracy
- HellaSwag - Common sense
- BigCodeBench - Multi-language coding

### Tier 3 (Monthly)
- MedQA - Medical knowledge
- LegalBench - Legal reasoning
- FinQA - Financial analysis
- WinoGrande - Reasoning
- SuperGLUE - NLP tasks
- WMT/BLEU - Translation
- CNN/DailyMail - Summarization
- SWE-bench - Software engineering
- MBPP - Python coding
- MathVista - Multimodal math
- MMMU - Multimodal understanding
- VQAv2 - Visual QA
- BBQ - Bias detection
- BioASQ - Biomedical QA

## Data Flow

1. **Collection**: MCP servers fetch data from benchmark sources
2. **Normalization**: Convert scores to 0-100 scale
3. **Mapping**: Match model names to canonical database names
4. **Validation**: Check data quality and confidence
5. **Update**: Atomic database updates with rollback safety
6. **Cache Clear**: Invalidate Redis cache for updated categories
7. **Audit**: Log all changes for accountability

## Monitoring

View collection reports:
```bash
ls -la logs/collection-reports/
```

View audit logs:
```bash
ls -la logs/audit/
```

Check unmapped models:
```bash
cat logs/unmapped-models.json
```

## Troubleshooting

1. **MCP Connection Issues**: Check cache directory permissions
2. **Database Errors**: Verify MySQL credentials
3. **Model Not Found**: Check unmapped models report
4. **Low Confidence Scores**: Review data sources in config

## Next Steps

- Implement weekly and monthly collectors
- Add real MCP server integration (currently using simulation)
- Create monitoring dashboard
- Add A/B testing for score weights
- Implement user feedback integration