#!/usr/bin/env node

/**
 * MCP Client Integration Layer
 * Provides unified interface to MCP servers (Firecrawl, Perplexity, Sequential Thinking)
 * with error handling, rate limiting, and caching
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class MCPClient {
  constructor(config = {}) {
    this.config = {
      cacheDir: path.join(__dirname, '../cache'),
      cacheTTL: 3600000, // 1 hour default
      maxRetries: 3,
      retryDelay: 1000,
      rateLimitDelay: 100,
      ...config
    };
    
    this.stats = {
      firecrawl: { success: 0, failure: 0, cached: 0 },
      perplexity: { success: 0, failure: 0, cached: 0 }
    };
    
    this.lastRequestTime = {};
  }

  /**
   * Initialize cache directory
   */
  async init() {
    await fs.mkdir(this.config.cacheDir, { recursive: true });
    console.log('✅ MCP Client initialized');
  }

  /**
   * Generate cache key from URL or query
   */
  getCacheKey(type, input) {
    const hash = crypto.createHash('md5').update(`${type}:${input}`).digest('hex');
    return `${type}_${hash}.json`;
  }

  /**
   * Check if cached data exists and is fresh
   */
  async getFromCache(cacheKey) {
    try {
      const cachePath = path.join(this.config.cacheDir, cacheKey);
      const stats = await fs.stat(cachePath);
      
      if (Date.now() - stats.mtime.getTime() < this.config.cacheTTL) {
        const data = await fs.readFile(cachePath, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      // Cache miss or expired
    }
    return null;
  }

  /**
   * Save data to cache
   */
  async saveToCache(cacheKey, data) {
    try {
      const cachePath = path.join(this.config.cacheDir, cacheKey);
      await fs.writeFile(cachePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Cache write error:', error.message);
    }
  }

  /**
   * Enforce rate limiting
   */
  async enforceRateLimit(service) {
    const now = Date.now();
    const lastRequest = this.lastRequestTime[service] || 0;
    const timeSinceLastRequest = now - lastRequest;
    
    if (timeSinceLastRequest < this.config.rateLimitDelay) {
      await this.delay(this.config.rateLimitDelay - timeSinceLastRequest);
    }
    
    this.lastRequestTime[service] = Date.now();
  }

  /**
   * Delay helper
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Scrape webpage using Firecrawl MCP
   */
  async scrapeWithFirecrawl(url, options = {}) {
    const cacheKey = this.getCacheKey('firecrawl', url);
    
    // Check cache first
    const cached = await this.getFromCache(cacheKey);
    if (cached) {
      this.stats.firecrawl.cached++;
      console.log(`📦 Using cached Firecrawl data for: ${url}`);
      return cached;
    }
    
    // Rate limiting
    await this.enforceRateLimit('firecrawl');
    
    // Retry logic
    let lastError;
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`🔍 Firecrawl scraping (attempt ${attempt}): ${url}`);
        
        // Simulate MCP call (in production, use actual MCP integration)
        const result = await this.simulateFirecrawlCall(url, options);
        
        this.stats.firecrawl.success++;
        await this.saveToCache(cacheKey, result);
        return result;
        
      } catch (error) {
        lastError = error;
        console.error(`❌ Firecrawl attempt ${attempt} failed:`, error.message);
        
        if (attempt < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }
    
    this.stats.firecrawl.failure++;
    throw new Error(`Firecrawl failed after ${this.config.maxRetries} attempts: ${lastError.message}`);
  }

  /**
   * Search using Perplexity MCP
   */
  async searchWithPerplexity(query, options = {}) {
    const cacheKey = this.getCacheKey('perplexity', query);
    
    // Check cache first
    const cached = await this.getFromCache(cacheKey);
    if (cached) {
      this.stats.perplexity.cached++;
      console.log(`📦 Using cached Perplexity data for: ${query}`);
      return cached;
    }
    
    // Rate limiting
    await this.enforceRateLimit('perplexity');
    
    // Retry logic
    let lastError;
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`🔎 Perplexity searching (attempt ${attempt}): ${query}`);
        
        // Simulate MCP call (in production, use actual MCP integration)
        const result = await this.simulatePerplexityCall(query, options);
        
        this.stats.perplexity.success++;
        await this.saveToCache(cacheKey, result);
        return result;
        
      } catch (error) {
        lastError = error;
        console.error(`❌ Perplexity attempt ${attempt} failed:`, error.message);
        
        if (attempt < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }
    
    this.stats.perplexity.failure++;
    throw new Error(`Perplexity failed after ${this.config.maxRetries} attempts: ${lastError.message}`);
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      ...this.stats,
      cacheDir: this.config.cacheDir,
      cacheTTL: this.config.cacheTTL
    };
  }

  /**
   * Clear cache
   */
  async clearCache() {
    const files = await fs.readdir(this.config.cacheDir);
    for (const file of files) {
      if (file.endsWith('.json')) {
        await fs.unlink(path.join(this.config.cacheDir, file));
      }
    }
    console.log('🗑️ Cache cleared');
  }

  // Simulation methods (replace with actual MCP calls in production)
  async simulateFirecrawlCall(url, options) {
    // Simulate network delay
    await this.delay(Math.random() * 1000 + 500);
    
    // Return mock data based on URL
    if (url.includes('lmsys')) {
      return {
        markdown: `# LMSYS Chatbot Arena Leaderboard
        
| Model | Elo Rating |
|-------|------------|
| GPT-4o-latest | 1287 |
| Claude-3.5-Sonnet | 1272 |
| Gemini-2.5-Pro | 1265 |
| GPT-4-Turbo | 1261 |`,
        metadata: { source: 'firecrawl', timestamp: new Date().toISOString() }
      };
    }
    
    if (url.includes('arcprize')) {
      return {
        markdown: `# ARC-AGI Leaderboard
        
| Model | ARC-AGI-2 Score | Cost/Task |
|-------|-----------------|-----------|
| Grok 4 Thinking | 16.0% | $2.17 |
| Claude Opus 4 | 8.6% | $1.93 |
| o3 High | 6.5% | $0.834 |`,
        metadata: { source: 'firecrawl', timestamp: new Date().toISOString() }
      };
    }
    
    throw new Error('Unknown URL pattern');
  }

  async simulatePerplexityCall(query, options) {
    // Simulate network delay
    await this.delay(Math.random() * 1500 + 1000);
    
    // Return mock search results
    return {
      answer: `Based on recent benchmarks, here are the top models for ${query}...`,
      sources: ['https://example.com/benchmark1', 'https://example.com/benchmark2'],
      metadata: { source: 'perplexity', timestamp: new Date().toISOString() }
    };
  }
}

// Export singleton instance
module.exports = new MCPClient();

// CLI interface for testing
if (require.main === module) {
  (async () => {
    const client = new MCPClient();
    await client.init();
    
    // Test Firecrawl
    try {
      const lmsysData = await client.scrapeWithFirecrawl('https://lmsys.org/leaderboard');
      console.log('LMSYS Data:', lmsysData);
    } catch (error) {
      console.error('Firecrawl test failed:', error);
    }
    
    // Test Perplexity
    try {
      const searchData = await client.searchWithPerplexity('latest coding benchmarks 2024');
      console.log('Search Data:', searchData);
    } catch (error) {
      console.error('Perplexity test failed:', error);
    }
    
    // Show stats
    console.log('\n📊 Statistics:', client.getStats());
  })();
}