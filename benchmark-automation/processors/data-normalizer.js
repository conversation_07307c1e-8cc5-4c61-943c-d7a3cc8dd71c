#!/usr/bin/env node

/**
 * Data Normalization Pipeline
 * Converts raw benchmark data into standardized scores for database insertion
 */

const config = require('../config/benchmark-sources.json');

class DataNormalizer {
  constructor() {
    this.modelMappings = this.createReverseMappings(config.modelMappings);
    this.scoreNormalization = config.scoreNormalization;
  }

  /**
   * Create reverse mappings for faster lookups
   */
  createReverseMappings(mappings) {
    const reverse = {};
    for (const [canonical, aliases] of Object.entries(mappings)) {
      for (const alias of aliases) {
        reverse[alias.toLowerCase()] = aliases[0]; // First alias is canonical form
      }
    }
    return reverse;
  }

  /**
   * Normalize model name to canonical form
   */
  normalizeModelName(rawName) {
    if (!rawName) return null;
    
    const cleaned = rawName.trim().toLowerCase();
    
    // Direct match
    if (this.modelMappings[cleaned]) {
      return this.modelMappings[cleaned];
    }
    
    // Partial match
    for (const [alias, canonical] of Object.entries(this.modelMappings)) {
      if (cleaned.includes(alias) || alias.includes(cleaned)) {
        return canonical;
      }
    }
    
    // Clean common patterns
    const patterns = [
      { pattern: /\s*\(.*?\)\s*$/g, replacement: '' }, // Remove parentheses
      { pattern: /[-_]/g, replacement: ' ' }, // Replace dashes/underscores
      { pattern: /\s+/g, replacement: ' ' }, // Multiple spaces to single
      { pattern: /^(openai|anthropic|google|meta|mistral|cohere|x-ai)\//i, replacement: '' }
    ];
    
    let normalized = rawName;
    for (const { pattern, replacement } of patterns) {
      normalized = normalized.replace(pattern, replacement);
    }
    
    // Try again with cleaned version
    const cleanedNormalized = normalized.trim().toLowerCase();
    if (this.modelMappings[cleanedNormalized]) {
      return this.modelMappings[cleanedNormalized];
    }
    
    // Log unmapped model for manual review
    console.warn(`⚠️ Unmapped model name: "${rawName}" → "${normalized}"`);
    return null;
  }

  /**
   * Normalize score based on data format
   */
  normalizeScore(rawScore, dataFormat) {
    if (rawScore == null || isNaN(rawScore)) return null;
    
    const normConfig = this.scoreNormalization[dataFormat];
    if (!normConfig) {
      console.warn(`⚠️ Unknown data format: ${dataFormat}`);
      return Math.round(Math.max(0, Math.min(100, rawScore)));
    }
    
    let normalized;
    switch (dataFormat) {
      case 'elo':
        // Elo to 0-100: (score - 900) / 6
        normalized = (rawScore - normConfig.min) / 6;
        break;
        
      case 'percentage':
      case 'pass@k':
      case 'bleu':
      case 'rouge':
        // Already in 0-100 range
        normalized = rawScore;
        break;
        
      case 'aggregate':
        // Average of multiple scores
        normalized = rawScore;
        break;
        
      default:
        normalized = rawScore;
    }
    
    // Ensure within 0-100 range
    return Math.round(Math.max(0, Math.min(100, normalized)));
  }

  /**
   * Calculate confidence score based on multiple factors
   */
  calculateConfidence(source, dataAge, hasMultipleSources = false) {
    let confidence = 0;
    
    // Base confidence from source
    switch (source.confidence) {
      case 'high': confidence = 80; break;
      case 'medium': confidence = 60; break;
      case 'low': confidence = 40; break;
      default: confidence = 50;
    }
    
    // Adjust for data freshness (in days)
    if (dataAge <= 1) confidence += 10;
    else if (dataAge <= 7) confidence += 5;
    else if (dataAge > 30) confidence -= 20;
    else if (dataAge > 90) confidence -= 40;
    
    // Bonus for multiple corroborating sources
    if (hasMultipleSources) confidence += 10;
    
    // Normalize to high/medium/low
    if (confidence >= 70) return 'high';
    if (confidence >= 40) return 'medium';
    return 'low';
  }

  /**
   * Process raw benchmark data into normalized scores
   */
  async processBenchmarkData(rawData, sourceKey) {
    const source = config.sources[sourceKey];
    if (!source) {
      throw new Error(`Unknown source: ${sourceKey}`);
    }
    
    const normalizedScores = [];
    const timestamp = new Date().toISOString();
    
    // Extract scores based on source configuration
    const scores = this.extractScores(rawData, source);
    
    for (const { model, score } of scores) {
      const canonicalName = this.normalizeModelName(model);
      if (!canonicalName) continue;
      
      const normalizedScore = this.normalizeScore(score, source.dataFormat);
      if (normalizedScore === null) continue;
      
      // Create score entries for each category
      for (const category of source.categories) {
        normalizedScores.push({
          modelName: canonicalName,
          category,
          score: normalizedScore,
          source: sourceKey,
          confidence: source.confidence,
          timestamp,
          metadata: {
            rawScore: score,
            dataFormat: source.dataFormat,
            sourceUrl: source.url
          }
        });
      }
    }
    
    return normalizedScores;
  }

  /**
   * Extract scores from raw data based on source configuration
   */
  extractScores(rawData, source) {
    const scores = [];
    
    try {
      // Handle different extraction methods
      if (source.extraction.method === 'firecrawl' && rawData.markdown) {
        scores.push(...this.extractFromMarkdown(rawData.markdown, source));
      } else if (source.extraction.method === 'perplexity' && rawData.answer) {
        scores.push(...this.extractFromText(rawData.answer, source));
      } else if (rawData.scores) {
        // Direct score data
        scores.push(...rawData.scores);
      }
    } catch (error) {
      console.error(`Error extracting scores from ${source.name}:`, error);
    }
    
    return scores;
  }

  /**
   * Extract scores from markdown tables
   */
  extractFromMarkdown(markdown, source) {
    const scores = [];
    const lines = markdown.split('\n');
    
    // Simple table parser
    let inTable = false;
    let headers = [];
    let tableStarted = false;
    
    for (const line of lines) {
      // Detect table separator
      if (line.includes('|') && line.includes('-')) {
        tableStarted = true;
        continue;
      }
      
      // Process table rows
      if (line.includes('|')) {
        const cells = line.split('|').map(c => c.trim()).filter(c => c);
        
        if (cells.length >= 2) {
          // Skip if this looks like a header row
          if (!tableStarted || cells.some(c => c.toLowerCase().includes('model') || c.toLowerCase().includes('score'))) {
            headers = cells;
            continue;
          }
          
          // Extract model and score based on column configuration
          const modelCol = source.extraction.columns?.model || 0;
          const scoreCol = source.extraction.columns?.score || 1;
          
          if (cells[modelCol] && cells[scoreCol]) {
            // Handle different score formats
            let scoreValue = cells[scoreCol];
            
            // Extract numeric value from various formats
            if (source.dataFormat === 'elo') {
              // Elo rating: just a number
              scoreValue = parseFloat(scoreValue.replace(/[^0-9.-]/g, ''));
            } else if (source.dataFormat === 'percentage') {
              // Percentage: might have % sign
              scoreValue = parseFloat(scoreValue.replace('%', '').trim());
            } else {
              // Generic number extraction
              scoreValue = parseFloat(scoreValue.replace(/[^0-9.-]/g, ''));
            }
            
            if (!isNaN(scoreValue)) {
              scores.push({
                model: cells[modelCol],
                score: scoreValue
              });
            }
          }
        }
      }
    }
    
    return scores;
  }

  /**
   * Extract scores from text using patterns
   */
  extractFromText(text, source) {
    const scores = [];
    
    // Use regex patterns if configured
    if (source.extraction.patterns) {
      const modelPattern = new RegExp(source.extraction.patterns.model, 'g');
      const scorePattern = new RegExp(source.extraction.patterns.score, 'g');
      
      // Simple pattern matching (could be improved)
      const lines = text.split('\n');
      for (const line of lines) {
        const modelMatch = line.match(/([A-Za-z0-9-_.]+(?:\s+[A-Za-z0-9-_.]+)*)\s*[:,]\s*([0-9.]+)%?/);
        if (modelMatch) {
          scores.push({
            model: modelMatch[1],
            score: parseFloat(modelMatch[2])
          });
        }
      }
    }
    
    return scores;
  }

  /**
   * Merge scores from multiple sources with conflict resolution
   */
  mergeScores(allScores) {
    const merged = {};
    
    // Group by model and category
    for (const score of allScores) {
      const key = `${score.modelName}:${score.category}`;
      if (!merged[key]) {
        merged[key] = [];
      }
      merged[key].push(score);
    }
    
    // Resolve conflicts
    const resolved = [];
    for (const [key, scores] of Object.entries(merged)) {
      if (scores.length === 1) {
        resolved.push(scores[0]);
      } else {
        // Multiple sources - use weighted average based on confidence
        const weights = {
          'high': 3,
          'medium': 2,
          'low': 1
        };
        
        let weightedSum = 0;
        let totalWeight = 0;
        
        for (const score of scores) {
          const weight = weights[score.confidence] || 1;
          weightedSum += score.score * weight;
          totalWeight += weight;
        }
        
        const mergedScore = Math.round(weightedSum / totalWeight);
        
        resolved.push({
          ...scores[0],
          score: mergedScore,
          confidence: 'high', // Multiple sources increase confidence
          metadata: {
            ...scores[0].metadata,
            sources: scores.map(s => s.source),
            sourceCount: scores.length
          }
        });
      }
    }
    
    return resolved;
  }
}

// Export singleton instance
module.exports = new DataNormalizer();

// CLI interface for testing
if (require.main === module) {
  (async () => {
    const normalizer = new DataNormalizer();
    
    // Test model name normalization
    console.log('🧪 Testing model name normalization:');
    const testNames = [
      'GPT-4o-latest',
      'Claude 3.5 Sonnet',
      'gemini-2.5-pro',
      'Llama 3 70B Instruct',
      'gpt-4-turbo',
      'Unknown Model XYZ'
    ];
    
    for (const name of testNames) {
      const normalized = normalizer.normalizeModelName(name);
      console.log(`  "${name}" → "${normalized}"`);
    }
    
    // Test score normalization
    console.log('\n🧪 Testing score normalization:');
    console.log(`  Elo 1287 → ${normalizer.normalizeScore(1287, 'elo')}`);
    console.log(`  Percentage 95.5 → ${normalizer.normalizeScore(95.5, 'percentage')}`);
    console.log(`  Pass@1 84.9 → ${normalizer.normalizeScore(84.9, 'pass@k')}`);
    
    // Test data processing
    console.log('\n🧪 Testing data processing:');
    const mockData = {
      markdown: `# Leaderboard
| Model | Score |
|-------|-------|
| GPT-4o-latest | 1287 |
| Claude 3.5 Sonnet | 1272 |`
    };
    
    const processed = await normalizer.processBenchmarkData(mockData, 'lmsys-arena');
    console.log('Processed scores:', JSON.stringify(processed, null, 2));
  })();
}