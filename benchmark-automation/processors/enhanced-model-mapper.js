#!/usr/bin/env node

/**
 * Enhanced Model Name Mapping System
 * Handles version suffixes, context lengths, and fuzzy matching while preserving model distinctions
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const config = require('../config/benchmark-sources.json');

class EnhancedModelMapper {
  constructor() {
    this.modelCache = new Map();
    this.canonicalToModel = new Map();
    this.unmappedModels = new Set();
    this.connection = null;
    this.mappingStats = {
      direct: 0,
      fuzzy: 0,
      enhanced: 0,
      failed: 0
    };
  }

  /**
   * Initialize database connection and load models
   */
  async init() {
    try {
      this.connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'NewRootPassword123!',
        database: 'justsimplechat'
      });
      
      await this.loadModelsFromDatabase();
      console.log(`✅ Enhanced model mapper initialized with ${this.modelCache.size} models`);
    } catch (error) {
      console.error('❌ Failed to initialize enhanced model mapper:', error);
      throw error;
    }
  }

  /**
   * Load all models from database into cache
   */
  async loadModelsFromDatabase() {
    const [rows] = await this.connection.execute(`
      SELECT id, canonicalName, displayName, 
             JSON_EXTRACT(extendedMetadata, '$.aliases') as aliases
      FROM Models
      WHERE isEnabled = 1
    `);
    
    for (const row of rows) {
      const aliases = JSON.parse(row.aliases || '[]');
      const allNames = [
        row.canonicalName,
        row.displayName,
        ...aliases
      ].filter(Boolean);
      
      const modelInfo = {
        id: row.id,
        canonicalName: row.canonicalName,
        displayName: row.displayName
      };
      
      // Store canonical name mapping
      this.canonicalToModel.set(row.canonicalName, modelInfo);
      
      // Store all variations
      for (const name of allNames) {
        this.modelCache.set(this.normalizeKey(name), modelInfo);
      }
    }
  }

  /**
   * Normalize key for cache lookup
   */
  normalizeKey(name) {
    if (!name) return '';
    
    return name
      .toLowerCase()
      .replace(/[-_\s]+/g, ' ')
      .replace(/[^a-z0-9\s]/g, '')
      .trim();
  }

  /**
   * Enhanced model name mapping with better version handling
   */
  async mapModelName(benchmarkName) {
    if (!benchmarkName) return null;
    
    // 1. Try direct cache lookup
    const directKey = this.normalizeKey(benchmarkName);
    if (this.modelCache.has(directKey)) {
      this.mappingStats.direct++;
      return this.modelCache.get(directKey).canonicalName;
    }
    
    // 2. Try enhanced mapping (handles versions/suffixes)
    const enhancedMatch = this.enhancedMapping(benchmarkName);
    if (enhancedMatch) {
      this.mappingStats.enhanced++;
      return enhancedMatch;
    }
    
    // 3. Try fuzzy matching
    const fuzzyMatch = this.fuzzyMatch(benchmarkName);
    if (fuzzyMatch) {
      this.mappingStats.fuzzy++;
      return fuzzyMatch.canonicalName;
    }
    
    // 4. Track unmapped model
    this.unmappedModels.add(benchmarkName);
    this.mappingStats.failed++;
    console.warn(`⚠️ Unmapped model: "${benchmarkName}"`);
    
    return null;
  }

  /**
   * Enhanced mapping that handles version suffixes and context lengths intelligently
   */
  enhancedMapping(name) {
    const normalized = name.toLowerCase();
    
    // Extract base model name and variants
    const patterns = [
      // DeepSeek-R1-0528 → deepseek-r1 (ignore date suffixes)
      {
        pattern: /^deepseek[-\s]?r1[-\s]?\d{4}$/i,
        baseModel: 'deepseek-r1'
      },
      // Claude 3.7 (16K) → claude-3.7 (ignore context length)
      {
        pattern: /^claude[-\s]?3\.7[-\s]?\(\d+k?\)$/i,
        baseModel: 'claude-3.7'
      },
      // GPT-4o-latest (2025-03-26) → gpt-4o-latest (ignore date)
      {
        pattern: /^(gpt[-\s]?4o[-\s]?latest)[-\s]?\([\d-]+\)$/i,
        baseModel: 'gpt-4o-latest'
      },
      // Gemini-2.5-Pro-Preview-05-06 → gemini-2.5-pro (ignore preview suffix)
      {
        pattern: /^gemini[-\s]?2\.5[-\s]?pro[-\s]?preview[-\s]?[\d-]+$/i,
        baseModel: 'gemini-2.5-pro'
      },
      // o3-2025-04-16 → o3 (ignore date)
      {
        pattern: /^o3[-\s]?\d{4}[-\d]*$/i,
        baseModel: 'o3'
      },
      // ChatGPT-4o-latest (any suffix) → chatgpt-4o-latest
      {
        pattern: /^chatgpt[-\s]?4o[-\s]?latest.*$/i,
        baseModel: 'chatgpt-4o-latest'
      },
      // Remove thinking/reasoning suffixes but keep base model
      {
        pattern: /^(.+?)[-\s]?\(thinking.*?\)$/i,
        baseModel: '$1'
      }
    ];
    
    for (const { pattern, baseModel } of patterns) {
      const match = normalized.match(pattern);
      if (match) {
        const base = baseModel.startsWith('$') ? match[1] : baseModel;
        const baseKey = this.normalizeKey(base);
        
        // Try to find the base model in cache
        if (this.modelCache.has(baseKey)) {
          console.log(`✅ Enhanced mapping: "${name}" → "${this.modelCache.get(baseKey).canonicalName}"`);
          return this.modelCache.get(baseKey).canonicalName;
        }
        
        // Try to find in canonical names
        for (const [canonical, model] of this.canonicalToModel) {
          if (canonical.toLowerCase().includes(base.toLowerCase())) {
            console.log(`✅ Enhanced mapping: "${name}" → "${canonical}"`);
            return canonical;
          }
        }
      }
    }
    
    // Handle provider prefixes (OpenAI/, Anthropic/, etc.)
    const providerMatch = normalized.match(/^(openai|anthropic|google|gemini|meta|mistral|cohere|x-ai|xai)[-\/\s]?(.+)$/);
    if (providerMatch) {
      const provider = providerMatch[1];
      const modelName = providerMatch[2];
      
      // Look for canonical name with provider prefix
      for (const [canonical, model] of this.canonicalToModel) {
        if (canonical.toLowerCase().includes(modelName) && 
            canonical.toLowerCase().includes(provider === 'xai' ? 'x-ai' : provider)) {
          console.log(`✅ Provider mapping: "${name}" → "${canonical}"`);
          return canonical;
        }
      }
    }
    
    return null;
  }

  /**
   * Fuzzy matching using various strategies
   */
  fuzzyMatch(name) {
    const normalized = this.normalizeKey(name);
    const tokens = normalized.split(' ').filter(t => t.length > 1);
    
    if (tokens.length === 0) return null;
    
    let bestMatch = null;
    let bestScore = 0;
    
    for (const [key, model] of this.modelCache) {
      let score = 0;
      
      // Check how many tokens match
      for (const token of tokens) {
        if (key.includes(token)) {
          score += token.length;
        }
      }
      
      // Bonus for exact substring match
      if (key.includes(normalized)) {
        score *= 1.5;
      }
      
      // Bonus for similar length
      const lengthDiff = Math.abs(key.length - normalized.length);
      if (lengthDiff < 5) {
        score *= 1.2;
      }
      
      // Penalty for very different length
      if (lengthDiff > 20) {
        score *= 0.8;
      }
      
      if (score > bestScore && score > 3) { // Minimum threshold
        bestScore = score;
        bestMatch = model;
      }
    }
    
    if (bestMatch) {
      console.log(`✅ Fuzzy match: "${name}" → "${bestMatch.canonicalName}" (score: ${bestScore.toFixed(2)})`);
    }
    
    return bestMatch;
  }

  /**
   * Get mapping statistics
   */
  getStats() {
    return {
      ...this.mappingStats,
      totalModels: this.modelCache.size,
      unmappedCount: this.unmappedModels.size,
      successRate: ((this.mappingStats.direct + this.mappingStats.enhanced + this.mappingStats.fuzzy) / 
                   (this.mappingStats.direct + this.mappingStats.enhanced + this.mappingStats.fuzzy + this.mappingStats.failed) * 100).toFixed(1)
    };
  }

  /**
   * Get unmapped models for analysis
   */
  getUnmappedModels() {
    return Array.from(this.unmappedModels);
  }

  /**
   * Close database connection
   */
  async close() {
    if (this.connection) {
      await this.connection.end();
    }
  }
}

// Export singleton instance
module.exports = new EnhancedModelMapper();

// CLI interface for testing
if (require.main === module) {
  (async () => {
    const mapper = new EnhancedModelMapper();
    await mapper.init();
    
    // Test cases that should work now
    const testCases = [
      'DeepSeek-R1-0528',
      'Claude 3.7 (16K)',
      'ChatGPT-4o-latest (2025-03-26)',
      'Gemini-2.5-Pro-Preview-05-06',
      'o3-2025-04-16',
      'Grok 4 (Thinking)',
      'GPT-4.1-Nano',
      'Claude Opus 4 (Thinking 16K)',
      'openai/gpt-4o-latest',
      'anthropic/claude-3-5-sonnet'
    ];
    
    console.log('🧪 Testing enhanced model mapping...\n');
    
    for (const testCase of testCases) {
      const result = await mapper.mapModelName(testCase);
      console.log(`Input: "${testCase}"`);
      console.log(`Output: ${result || 'NOT FOUND'}\n`);
    }
    
    console.log('📊 Mapping Statistics:');
    console.log(mapper.getStats());
    
    const unmapped = mapper.getUnmappedModels();
    if (unmapped.length > 0) {
      console.log('\n⚠️ Unmapped models:');
      unmapped.forEach(model => console.log(`  - ${model}`));
    }
    
    await mapper.close();
  })();
}