#!/usr/bin/env node

/**
 * Model Name Mapping System
 * Maps benchmark model names to canonical database names with fuzzy matching
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const config = require('../config/benchmark-sources.json');

class ModelMapper {
  constructor() {
    this.modelCache = new Map();
    this.unmappedModels = new Set();
    this.mappingRules = this.buildMappingRules();
    this.connection = null;
  }

  /**
   * Initialize database connection and load models
   */
  async init() {
    try {
      this.connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'NewRootPassword123!',
        database: 'justsimplechat'
      });
      
      await this.loadModelsFromDatabase();
      console.log(`✅ Model mapper initialized with ${this.modelCache.size} models`);
    } catch (error) {
      console.error('❌ Failed to initialize model mapper:', error);
      throw error;
    }
  }

  /**
   * Load all models from database into cache
   */
  async loadModelsFromDatabase() {
    const [rows] = await this.connection.execute(`
      SELECT id, canonicalName, displayName, 
             JSON_EXTRACT(extendedMetadata, '$.aliases') as aliases
      FROM Models
      WHERE isEnabled = 1
    `);
    
    for (const row of rows) {
      const aliases = JSON.parse(row.aliases || '[]');
      const allNames = [
        row.canonicalName,
        row.displayName,
        ...aliases
      ].filter(Boolean);
      
      // Store all variations
      for (const name of allNames) {
        this.modelCache.set(this.normalizeKey(name), {
          id: row.id,
          canonicalName: row.canonicalName,
          displayName: row.displayName
        });
      }
    }
  }

  /**
   * Build mapping rules from configuration
   */
  buildMappingRules() {
    const rules = [];
    
    // Add configured mappings
    for (const [primary, aliases] of Object.entries(config.modelMappings)) {
      for (const alias of aliases) {
        rules.push({
          pattern: new RegExp(this.escapeRegex(alias), 'i'),
          replacement: aliases[0] // Canonical form
        });
      }
    }
    
    // Add common pattern rules
    rules.push(
      // Provider prefixes
      { pattern: /^(openai\/|anthropic\/|google\/|gemini\/|meta\/|mistral\/|cohere\/|x-ai\/)/i, replacement: '' },
      // Version patterns
      { pattern: /\s*\(latest\)/i, replacement: '-latest' },
      { pattern: /\s*\(preview\)/i, replacement: '-preview' },
      { pattern: /\s*\((high|medium|low)\)/i, replacement: '' },
      // Date patterns
      { pattern: /\s*\((?:jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{4}\)/i, replacement: '' },
      { pattern: /\s*\(\d{4}-\d{2}-\d{2}\)/i, replacement: '' },
      // Common variations
      { pattern: /gpt-?4-?o/i, replacement: 'gpt-4o' },
      { pattern: /claude-?3\.?5/i, replacement: 'claude-3-5' },
      { pattern: /gemini-?2\.?5/i, replacement: 'gemini-2-5' },
      { pattern: /llama-?3/i, replacement: 'llama-3' },
      // Thinking variants
      { pattern: /\s*\(thinking.*?\)/i, replacement: '' },
      { pattern: /\s+thinking$/i, replacement: '' }
    );
    
    return rules;
  }

  /**
   * Normalize key for cache lookup
   */
  normalizeKey(name) {
    if (!name) return '';
    
    return name
      .toLowerCase()
      .replace(/[-_\s]+/g, ' ')
      .replace(/[^a-z0-9\s]/g, '')
      .trim();
  }

  /**
   * Escape regex special characters
   */
  escapeRegex(str) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Map benchmark model name to canonical database name
   */
  async mapModelName(benchmarkName) {
    if (!benchmarkName) return null;
    
    // Try direct cache lookup
    const directKey = this.normalizeKey(benchmarkName);
    if (this.modelCache.has(directKey)) {
      return this.modelCache.get(directKey).canonicalName;
    }
    
    // Apply mapping rules
    let mapped = benchmarkName;
    for (const rule of this.mappingRules) {
      mapped = mapped.replace(rule.pattern, rule.replacement);
    }
    
    // Try again with mapped name
    const mappedKey = this.normalizeKey(mapped);
    if (this.modelCache.has(mappedKey)) {
      return this.modelCache.get(mappedKey).canonicalName;
    }
    
    // Fuzzy matching
    const fuzzyMatch = this.fuzzyMatch(benchmarkName);
    if (fuzzyMatch) {
      return fuzzyMatch.canonicalName;
    }
    
    // Track unmapped model
    this.unmappedModels.add(benchmarkName);
    console.warn(`⚠️ Unmapped model: "${benchmarkName}"`);
    
    return null;
  }

  /**
   * Fuzzy matching using various strategies
   */
  fuzzyMatch(name) {
    const normalized = this.normalizeKey(name);
    const tokens = normalized.split(' ');
    
    // Strategy 1: Find models containing all tokens
    let bestMatch = null;
    let bestScore = 0;
    
    for (const [key, model] of this.modelCache) {
      let score = 0;
      
      // Check how many tokens match
      for (const token of tokens) {
        if (key.includes(token)) {
          score += token.length;
        }
      }
      
      // Bonus for exact token sequence
      if (key.includes(normalized)) {
        score *= 2;
      }
      
      // Penalty for length difference
      score -= Math.abs(key.length - normalized.length) * 0.1;
      
      if (score > bestScore) {
        bestScore = score;
        bestMatch = model;
      }
    }
    
    // Only return if confidence is high enough
    if (bestScore > normalized.length * 0.7) {
      return bestMatch;
    }
    
    // Strategy 2: Known model families
    const modelFamilies = {
      'gpt': /gpt-?[0-9]/,
      'claude': /claude/,
      'gemini': /gemini/,
      'llama': /llama/,
      'mixtral': /mixtral/,
      'qwen': /qwen/,
      'deepseek': /deepseek/,
      'command': /command/
    };
    
    for (const [family, pattern] of Object.entries(modelFamilies)) {
      if (pattern.test(normalized)) {
        // Find best match within family
        for (const [key, model] of this.modelCache) {
          if (key.includes(family) && this.calculateSimilarity(normalized, key) > 0.6) {
            return model;
          }
        }
      }
    }
    
    return null;
  }

  /**
   * Calculate string similarity (0-1)
   */
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Levenshtein distance between two strings
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Get model by canonical name
   */
  getModel(canonicalName) {
    for (const model of this.modelCache.values()) {
      if (model.canonicalName === canonicalName) {
        return model;
      }
    }
    return null;
  }

  /**
   * Save unmapped models report
   */
  async saveUnmappedReport() {
    if (this.unmappedModels.size === 0) return;
    
    const reportPath = path.join(__dirname, '../logs/unmapped-models.json');
    const report = {
      timestamp: new Date().toISOString(),
      count: this.unmappedModels.size,
      models: Array.from(this.unmappedModels).sort()
    };
    
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📝 Saved ${this.unmappedModels.size} unmapped models to report`);
  }

  /**
   * Close database connection
   */
  async close() {
    if (this.connection) {
      await this.connection.end();
    }
  }
}

// Export singleton instance
module.exports = new ModelMapper();

// CLI interface for testing
if (require.main === module) {
  (async () => {
    const mapper = new ModelMapper();
    
    try {
      await mapper.init();
      
      console.log('🧪 Testing model mapping:');
      const testNames = [
        'GPT-4o-latest',
        'GPT-4o (Latest)',
        'gpt4o-latest',
        'Claude 3.5 Sonnet',
        'Claude-3.5-Sonnet (October 2024)',
        'Gemini 2.5 Pro',
        'gemini-2.5-pro',
        'Llama 3 70B Instruct',
        'o3-mini (High)',
        'Grok 4 Thinking',
        'Unknown Model XYZ',
        'GPT4 Turbo',
        'gpt-4-turbo'
      ];
      
      for (const name of testNames) {
        const canonical = await mapper.mapModelName(name);
        console.log(`  "${name}" → "${canonical || 'NOT FOUND'}"`);
      }
      
      // Save unmapped models
      await mapper.saveUnmappedReport();
      
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      await mapper.close();
    }
  })();
}