#!/usr/bin/env node

/**
 * Database Updater with Confidence Scoring
 * Updates model scores in extendedMetadata with transaction safety and audit trail
 */

const mysql = require('mysql2/promise');
const Redis = require('ioredis');
const fs = require('fs').promises;
const path = require('path');

class DatabaseUpdater {
  constructor(config = {}) {
    this.config = {
      batchSize: 100,
      backupBeforeUpdate: true,
      auditLogPath: path.join(__dirname, '../logs/audit'),
      redisConfig: {
        host: 'localhost',
        port: 6379,
        db: 0
      },
      ...config
    };
    
    this.connection = null;
    this.redis = null;
    this.updateStats = {
      total: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * Initialize database and Redis connections
   */
  async init() {
    try {
      // Database connection
      this.connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: 'NewRootPassword123!',
        database: 'justsimplechat'
      });
      
      // Redis connection for cache management
      this.redis = new Redis(this.config.redisConfig);
      
      // Create audit directory
      await fs.mkdir(this.config.auditLogPath, { recursive: true });
      
      console.log('✅ Database updater initialized');
    } catch (error) {
      console.error('❌ Failed to initialize database updater:', error);
      throw error;
    }
  }

  /**
   * Update scores with transaction safety
   */
  async updateScores(normalizedScores) {
    if (!normalizedScores || normalizedScores.length === 0) {
      console.log('No scores to update');
      return this.updateStats;
    }
    
    console.log(`🔄 Updating ${normalizedScores.length} scores...`);
    
    // Start transaction
    await this.connection.beginTransaction();
    
    try {
      // Backup current scores if enabled
      if (this.config.backupBeforeUpdate) {
        await this.backupCurrentScores(normalizedScores);
      }
      
      // Process in batches
      const batches = this.createBatches(normalizedScores, this.config.batchSize);
      
      for (const [index, batch] of batches.entries()) {
        console.log(`  Processing batch ${index + 1}/${batches.length}...`);
        await this.processBatch(batch);
      }
      
      // Commit transaction
      await this.connection.commit();
      
      // Clear relevant caches
      await this.clearCaches(normalizedScores);
      
      // Create audit log
      await this.createAuditLog(normalizedScores);
      
      console.log(`✅ Update complete: ${this.updateStats.success} successful, ${this.updateStats.failed} failed, ${this.updateStats.skipped} skipped`);
      
      return this.updateStats;
      
    } catch (error) {
      // Rollback on error
      await this.connection.rollback();
      console.error('❌ Update failed, rolled back:', error);
      throw error;
    }
  }

  /**
   * Process a batch of score updates
   */
  async processBatch(batch) {
    for (const score of batch) {
      try {
        await this.updateSingleScore(score);
        this.updateStats.success++;
      } catch (error) {
        console.error(`Failed to update ${score.modelName}/${score.category}:`, error.message);
        this.updateStats.failed++;
      }
    }
  }

  /**
   * Update a single score in the database
   */
  async updateSingleScore(scoreData) {
    const { modelName, category, score, source, confidence, timestamp, metadata } = scoreData;
    
    // Check if model exists
    const [models] = await this.connection.execute(
      'SELECT id, extendedMetadata FROM Models WHERE canonicalName = ?',
      [modelName]
    );
    
    if (models.length === 0) {
      this.updateStats.skipped++;
      console.warn(`⚠️ Model not found: ${modelName}`);
      return;
    }
    
    const model = models[0];
    let extendedMetadata = typeof model.extendedMetadata === 'string' 
      ? JSON.parse(model.extendedMetadata || '{}')
      : model.extendedMetadata || {};
    
    // Initialize categoryScores if not exists
    if (!extendedMetadata.categoryScores) {
      extendedMetadata.categoryScores = {};
    }
    
    // Get current score data
    const currentScore = extendedMetadata.categoryScores[category];
    
    // Decide whether to update based on confidence and freshness
    if (currentScore && !this.shouldUpdate(currentScore, scoreData)) {
      this.updateStats.skipped++;
      return;
    }
    
    // Update score using JSON_SET
    const updateQuery = `
      UPDATE Models 
      SET extendedMetadata = JSON_SET(
        extendedMetadata,
        '$.categoryScores.${category}',
        JSON_OBJECT(
          'score', ?,
          'source', ?,
          'confidence', ?,
          'updated', ?,
          'metadata', CAST(? AS JSON)
        )
      )
      WHERE id = ?
    `;
    
    await this.connection.execute(updateQuery, [
      score,
      source,
      confidence,
      timestamp,
      JSON.stringify(metadata || {}),
      model.id
    ]);
    
    this.updateStats.total++;
  }

  /**
   * Determine if a score should be updated
   */
  shouldUpdate(currentScore, newScore) {
    // Always update if no current score
    if (!currentScore || !currentScore.score) return true;
    
    // Confidence hierarchy
    const confidenceLevels = { low: 1, medium: 2, high: 3 };
    const currentConf = confidenceLevels[currentScore.confidence] || 1;
    const newConf = confidenceLevels[newScore.confidence] || 1;
    
    // Higher confidence always wins
    if (newConf > currentConf) return true;
    
    // Same confidence - check freshness
    if (newConf === currentConf) {
      const currentDate = new Date(currentScore.updated || 0);
      const newDate = new Date(newScore.timestamp);
      
      // Update if newer by at least 1 day
      if (newDate - currentDate > 86400000) return true;
    }
    
    // Lower confidence only updates if current data is very old (>30 days)
    if (newConf < currentConf) {
      const currentDate = new Date(currentScore.updated || 0);
      const ageInDays = (Date.now() - currentDate) / 86400000;
      
      if (ageInDays > 30) return true;
    }
    
    return false;
  }

  /**
   * Backup current scores before update
   */
  async backupCurrentScores(scoresToUpdate) {
    const modelNames = [...new Set(scoresToUpdate.map(s => s.modelName))];
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.config.auditLogPath, `backup-${timestamp}.json`);
    
    const backup = {
      timestamp: new Date().toISOString(),
      modelCount: modelNames.length,
      scoreCount: scoresToUpdate.length,
      models: {}
    };
    
    // Fetch current scores for models being updated
    for (const modelName of modelNames) {
      const [models] = await this.connection.execute(
        'SELECT canonicalName, JSON_EXTRACT(extendedMetadata, "$.categoryScores") as scores FROM Models WHERE canonicalName = ?',
        [modelName]
      );
      
      if (models.length > 0 && models[0].scores) {
        try {
          backup.models[modelName] = typeof models[0].scores === 'string' 
            ? JSON.parse(models[0].scores) 
            : models[0].scores;
        } catch (e) {
          console.warn(`Failed to parse scores for ${modelName}:`, e.message);
          backup.models[modelName] = {};
        }
      }
    }
    
    await fs.writeFile(backupPath, JSON.stringify(backup, null, 2));
    console.log(`📦 Backed up current scores to: ${path.basename(backupPath)}`);
  }

  /**
   * Clear Redis caches for updated categories
   */
  async clearCaches(scores) {
    const categories = [...new Set(scores.map(s => s.category))];
    const patterns = [
      'router:*',
      'model:*',
      ...categories.map(cat => `category:${cat}:*`)
    ];
    
    let clearedCount = 0;
    
    for (const pattern of patterns) {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        clearedCount += keys.length;
      }
    }
    
    console.log(`🗑️ Cleared ${clearedCount} cache entries`);
  }

  /**
   * Create audit log of updates
   */
  async createAuditLog(scores) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const logPath = path.join(this.config.auditLogPath, `update-${timestamp}.json`);
    
    const auditLog = {
      timestamp: new Date().toISOString(),
      stats: this.updateStats,
      updates: scores.map(s => ({
        model: s.modelName,
        category: s.category,
        score: s.score,
        source: s.source,
        confidence: s.confidence
      }))
    };
    
    await fs.writeFile(logPath, JSON.stringify(auditLog, null, 2));
    console.log(`📝 Created audit log: ${path.basename(logPath)}`);
  }

  /**
   * Create batches from array
   */
  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupFile) {
    const backupPath = path.join(this.config.auditLogPath, backupFile);
    const backupData = JSON.parse(await fs.readFile(backupPath, 'utf8'));
    
    console.log(`🔄 Restoring from backup: ${backupFile}`);
    console.log(`  Models: ${backupData.modelCount}, Scores: ${backupData.scoreCount}`);
    
    await this.connection.beginTransaction();
    
    try {
      for (const [modelName, scores] of Object.entries(backupData.models)) {
        for (const [category, scoreData] of Object.entries(scores)) {
          const updateQuery = `
            UPDATE Models 
            SET extendedMetadata = JSON_SET(
              extendedMetadata,
              '$.categoryScores.${category}',
              CAST(? AS JSON)
            )
            WHERE canonicalName = ?
          `;
          
          await this.connection.execute(updateQuery, [
            JSON.stringify(scoreData),
            modelName
          ]);
        }
      }
      
      await this.connection.commit();
      console.log('✅ Restore complete');
      
    } catch (error) {
      await this.connection.rollback();
      console.error('❌ Restore failed:', error);
      throw error;
    }
  }

  /**
   * Get update statistics
   */
  getStats() {
    return this.updateStats;
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.updateStats = {
      total: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * Close connections
   */
  async close() {
    if (this.connection) await this.connection.end();
    if (this.redis) await this.redis.quit();
  }
}

// Export singleton instance
module.exports = new DatabaseUpdater();

// CLI interface for testing
if (require.main === module) {
  (async () => {
    const updater = new DatabaseUpdater();
    
    try {
      await updater.init();
      
      // Test data
      const testScores = [
        {
          modelName: 'openai/gpt-4o-latest',
          category: 'general_chat',
          score: 88,
          source: 'lmsys-arena',
          confidence: 'high',
          timestamp: new Date().toISOString(),
          metadata: { elo: 1287 }
        },
        {
          modelName: 'anthropic/claude-3-5-sonnet-20241022',
          category: 'general_chat',
          score: 87,
          source: 'lmsys-arena',
          confidence: 'high',
          timestamp: new Date().toISOString(),
          metadata: { elo: 1272 }
        }
      ];
      
      console.log('🧪 Testing score updates...');
      const stats = await updater.updateScores(testScores);
      console.log('Update statistics:', stats);
      
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      await updater.close();
    }
  })();
}