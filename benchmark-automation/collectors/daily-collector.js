#!/usr/bin/env node

/**
 * Daily Benchmark Collector
 * Collects Tier 1 benchmark data from LMSYS Arena, ARC-AGI, and Hugging Face
 */

const path = require('path');
const fs = require('fs').promises;

// Import components
const mcpClient = require('../processors/mcp-client');
const dataFormalizer = require('../processors/data-normalizer');
const modelMapper = require('../processors/model-mapper');
const databaseUpdater = require('../utils/database-updater');
const config = require('../config/benchmark-sources.json');

class DailyCollector {
  constructor() {
    this.tier1Sources = Object.entries(config.sources)
      .filter(([_, source]) => source.tier === 1 && source.enabled)
      .map(([key, source]) => ({ key, ...source }));
    
    this.collectionResults = {
      timestamp: new Date().toISOString(),
      sources: {},
      summary: {
        total: 0,
        success: 0,
        failed: 0,
        scores: 0
      }
    };
  }

  /**
   * Main collection process
   */
  async collect() {
    console.log('🚀 Starting Daily Benchmark Collection');
    console.log(`📅 ${new Date().toISOString()}`);
    console.log(`📊 Processing ${this.tier1Sources.length} Tier 1 sources\n`);
    
    try {
      // Initialize all components
      await this.initialize();
      
      // Collect from each source
      const allScores = [];
      
      for (const source of this.tier1Sources) {
        console.log(`\n📍 Processing: ${source.name}`);
        const scores = await this.collectFromSource(source);
        
        if (scores && scores.length > 0) {
          allScores.push(...scores);
          this.collectionResults.sources[source.key] = {
            status: 'success',
            count: scores.length
          };
          this.collectionResults.summary.success++;
        } else {
          this.collectionResults.sources[source.key] = {
            status: 'failed',
            error: 'No scores extracted'
          };
          this.collectionResults.summary.failed++;
        }
        
        this.collectionResults.summary.total++;
      }
      
      console.log(`\n📊 Collected ${allScores.length} total scores`);
      
      // Merge scores from multiple sources
      const mergedScores = dataFormalizer.mergeScores(allScores);
      console.log(`🔀 Merged to ${mergedScores.length} unique scores`);
      
      // Update database
      console.log('\n💾 Updating database...');
      const updateStats = await databaseUpdater.updateScores(mergedScores);
      
      this.collectionResults.summary.scores = mergedScores.length;
      this.collectionResults.updateStats = updateStats;
      
      // Save collection report
      await this.saveReport();
      
      // Save unmapped models report
      await modelMapper.saveUnmappedReport();
      
      console.log('\n✅ Daily collection complete!');
      console.log(this.formatSummary());
      
    } catch (error) {
      console.error('❌ Collection failed:', error);
      this.collectionResults.error = error.message;
      await this.saveReport();
      throw error;
      
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Initialize all components
   */
  async initialize() {
    console.log('🔧 Initializing components...');
    
    await mcpClient.init();
    await modelMapper.init();
    await databaseUpdater.init();
    
    console.log('✅ All components initialized');
  }

  /**
   * Collect data from a single source
   */
  async collectFromSource(source) {
    try {
      let rawData;
      
      // Fetch data based on extraction method
      if (source.extraction.method === 'firecrawl') {
        rawData = await mcpClient.scrapeWithFirecrawl(source.url, {
          formats: ['markdown'],
          onlyMainContent: true
        });
      } else if (source.extraction.method === 'perplexity') {
        rawData = await mcpClient.searchWithPerplexity(source.extraction.query);
      } else {
        throw new Error(`Unknown extraction method: ${source.extraction.method}`);
      }
      
      // Process raw data
      const scores = await dataFormalizer.processBenchmarkData(rawData, source.key);
      
      // Map model names
      const mappedScores = [];
      for (const score of scores) {
        const canonicalName = await modelMapper.mapModelName(score.modelName);
        if (canonicalName) {
          mappedScores.push({
            ...score,
            modelName: canonicalName
          });
        }
      }
      
      console.log(`  ✓ Extracted ${mappedScores.length} scores`);
      return mappedScores;
      
    } catch (error) {
      console.error(`  ✗ Failed to collect from ${source.name}:`, error.message);
      
      // Try fallback if configured
      if (source.extraction.fallback === 'perplexity') {
        console.log('  🔄 Trying Perplexity fallback...');
        try {
          const fallbackQuery = `${source.name} benchmark latest results ${new Date().getFullYear()}`;
          const fallbackData = await mcpClient.searchWithPerplexity(fallbackQuery);
          const scores = await dataFormalizer.processBenchmarkData(fallbackData, source.key);
          
          console.log(`  ✓ Fallback extracted ${scores.length} scores`);
          return scores;
        } catch (fallbackError) {
          console.error('  ✗ Fallback also failed:', fallbackError.message);
        }
      }
      
      return [];
    }
  }

  /**
   * Save collection report
   */
  async saveReport() {
    const reportDir = path.join(__dirname, '../logs/collection-reports');
    await fs.mkdir(reportDir, { recursive: true });
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(reportDir, `daily-${timestamp}.json`);
    
    await fs.writeFile(reportPath, JSON.stringify(this.collectionResults, null, 2));
    console.log(`📝 Report saved: ${path.basename(reportPath)}`);
  }

  /**
   * Format summary for display
   */
  formatSummary() {
    const { summary } = this.collectionResults;
    return `
╔════════════════════════════════════╗
║       Collection Summary           ║
╠════════════════════════════════════╣
║ Sources Processed: ${summary.total.toString().padEnd(16)}║
║ Successful:        ${summary.success.toString().padEnd(16)}║
║ Failed:            ${summary.failed.toString().padEnd(16)}║
║ Total Scores:      ${summary.scores.toString().padEnd(16)}║
╚════════════════════════════════════╝`;
  }

  /**
   * Cleanup connections
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    await modelMapper.close();
    await databaseUpdater.close();
  }
}

// Main execution
if (require.main === module) {
  const collector = new DailyCollector();
  
  collector.collect()
    .then(() => {
      console.log('\n👍 Daily collection completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Daily collection failed:', error);
      process.exit(1);
    });
}

module.exports = DailyCollector;