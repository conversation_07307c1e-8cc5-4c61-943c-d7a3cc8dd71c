#!/bin/bash
# Daily Comprehensive Benchmark Collection Script
# Runs at 2am daily to collect ALL benchmark data from ALL sources

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
COLLECTION_ID=$(date +%Y%m%d_%H%M%S)
LOG_DIR="$BASE_DIR/logs/daily/$COLLECTION_ID"
PROMPT_DIR="$BASE_DIR/prompts"

# Create log directory
mkdir -p "$LOG_DIR"

# Database configuration
DB_HOST="localhost"
DB_USER="root"
DB_PASS="NewRootPassword123!"
DB_NAME="justsimplechat"

# Claude CLI configuration
CLAUDE_CMD="claude --print --dangerously-skip-permissions --allowedTools '*:*'"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/collection.log"
}

# Initialize collection run
init_collection() {
    log "Starting daily benchmark collection (ID: $COLLECTION_ID)"
    
    # Create collection run record
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" << EOF
INSERT INTO collection_runs (collection_id, started_at) VALUES ('$COLLECTION_ID', NOW());
EOF
    
    log "Collection run initialized in database"
}

# Main collection function
run_collection() {
    log "Starting comprehensive benchmark data collection..."
    
    # Create comprehensive collection prompt
    cat > "$LOG_DIR/collection_prompt.md" << 'EOF'
# Comprehensive Daily Benchmark Collection

You are a benchmark data collection specialist. Your task is to comprehensively collect ALL benchmark data from ALL active sources.

## Task Overview
1. Query the database to get all active benchmark sources
2. For each source, scrape the complete leaderboard
3. Extract ALL models and ALL metrics (don't filter anything)
4. Store raw data in benchmark_raw_data table
5. Update source metadata and collection statistics

## Database Connection
```sql
mysql -h localhost -u root -pNewRootPassword123! justsimplechat
```

## Collection Process

### 1. Get Active Sources
```sql
SELECT source_name, source_url, description 
FROM benchmark_sources 
WHERE is_active = TRUE 
ORDER BY scrape_frequency DESC;
```

### 2. For Each Source, Scrape Data
Use MCP Firecrawl to scrape each source URL:
- Use appropriate waitFor time for dynamic content
- Extract all models and scores
- Don't worry about model name mapping yet
- Capture everything as-is

### 3. Store Raw Data
For each model found, insert into benchmark_raw_data:
```sql
INSERT INTO benchmark_raw_data (
    source, source_url, model_name_raw, metric_name, 
    metric_value, metric_unit, additional_data, 
    collection_id
) VALUES (?, ?, ?, ?, ?, ?, ?, ?);
```

### 4. Example Data Storage

Source: lmsys-arena
Model found: "GPT-4o-latest"
Store as:
- model_name_raw: "GPT-4o-latest"
- metric_name: "elo"
- metric_value: 1287
- metric_unit: "elo_points"
- additional_data: {"rank": 1, "category": "overall"}

Source: arc-agi
Model found: "Claude 3.7 (16K)"
Store as:
- model_name_raw: "Claude 3.7 (16K)"
- metric_name: "arc-agi-2"
- metric_value: 8.6
- metric_unit: "percentage"
- additional_data: {"cost_per_task": 1.93, "system_type": "CoT"}

### 5. Handle Multiple Metrics
If a source has multiple metrics for the same model, create separate rows:
- Row 1: arc-agi-1 score
- Row 2: arc-agi-2 score
- Row 3: cost per task

### 6. Update Source Metadata
After each successful scrape:
```sql
UPDATE benchmark_sources 
SET last_successful_scrape = NOW(),
    total_models_found = ?,
    total_metrics_found = ?
WHERE source_name = ?;
```

### 7. Generate Collection Report
Create a detailed report showing:
- Total sources processed
- Total models found
- Total metrics collected
- Any errors or failures
- New models discovered since last run

## Error Handling
- If a source fails to scrape, log the error but continue
- Store partial data if extraction partially succeeds
- Update source metadata even for failed attempts
- Include all errors in the final report

## Important Notes
- Store model names EXACTLY as they appear on the source
- Don't attempt to normalize or map names yet
- Capture all available metadata
- Focus on completeness over accuracy
- Speed is less important than thoroughness

Your goal: Collect every piece of benchmark data available, regardless of whether we recognize the models or not.
EOF

    # Run the collection using Claude CLI
    log "Executing Claude CLI collection..."
    
    $CLAUDE_CMD \
        --context "Collection ID: $COLLECTION_ID" \
        --context "Log Directory: $LOG_DIR" \
        --log "$LOG_DIR/claude_output.log" \
        < "$LOG_DIR/collection_prompt.md" \
        > "$LOG_DIR/collection_results.md" 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log "Collection completed successfully"
    else
        log "Collection completed with errors (exit code: $exit_code)"
    fi
    
    return $exit_code
}

# Finalize collection
finalize_collection() {
    local success=$1
    log "Finalizing collection run..."
    
    # Get collection statistics
    local stats=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        SELECT 
            COUNT(DISTINCT source) as sources_processed,
            COUNT(DISTINCT model_name_raw) as unique_models,
            COUNT(*) as total_metrics
        FROM benchmark_raw_data 
        WHERE collection_id = '$COLLECTION_ID';
    " --skip-column-names)
    
    # Update collection run record
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" << EOF
UPDATE collection_runs 
SET completed_at = NOW(),
    successful_sources = $(echo $stats | cut -d' ' -f1),
    total_models_found = $(echo $stats | cut -d' ' -f2),
    total_metrics_collected = $(echo $stats | cut -d' ' -f3)
WHERE collection_id = '$COLLECTION_ID';
EOF
    
    log "Collection statistics: $stats"
    
    # Generate summary report
    generate_summary_report
}

# Generate summary report
generate_summary_report() {
    log "Generating collection summary report..."
    
    $CLAUDE_CMD << EOF > "$LOG_DIR/summary_report.md"
Generate a comprehensive summary report for collection run $COLLECTION_ID.

Query the database to analyze:
1. Total models and metrics collected
2. Breakdown by source
3. New models discovered since last run
4. Any quality issues or anomalies
5. Comparison with previous collections

Database: mysql -h localhost -u root -pNewRootPassword123! justsimplechat

Create a detailed markdown report with statistics and insights.
EOF
    
    log "Summary report generated: $LOG_DIR/summary_report.md"
}

# Cleanup old logs (keep last 30 days)
cleanup_logs() {
    find "$BASE_DIR/logs/daily" -type d -mtime +30 -exec rm -rf {} + 2>/dev/null || true
    log "Cleaned up old log files"
}

# Main execution
main() {
    log "=== Daily Benchmark Collection Started ==="
    
    # Initialize
    init_collection
    
    # Run collection
    if run_collection; then
        finalize_collection 0
        log "=== Collection Completed Successfully ==="
    else
        finalize_collection 1
        log "=== Collection Completed with Errors ==="
    fi
    
    # Cleanup
    cleanup_logs
    
    log "=== Daily Collection Finished ==="
}

# Run main function
main "$@"