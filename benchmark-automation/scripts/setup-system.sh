#!/bin/bash
# System Setup Script
# Initializes the benchmark automation system

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# Database configuration
DB_HOST="localhost"
DB_USER="root"
DB_PASS="NewRootPassword123!"
DB_NAME="justsimplechat"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check MySQL
    if ! command -v mysql &> /dev/null; then
        error "MySQL client not found. Please install mysql-client."
        exit 1
    fi
    
    # Check Claude CLI
    if ! command -v claude &> /dev/null; then
        error "Claude CLI not found. Please install Claude CLI."
        exit 1
    fi
    
    # Check Redis
    if ! command -v redis-cli &> /dev/null; then
        error "Redis CLI not found. Please install redis-tools."
        exit 1
    fi
    
    # Check jq
    if ! command -v jq &> /dev/null; then
        error "jq not found. Please install jq."
        exit 1
    fi
    
    log "✅ All prerequisites met"
}

# Test database connection
test_database() {
    log "Testing database connection..."
    
    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT 1;" > /dev/null 2>&1; then
        log "✅ Database connection successful"
    else
        error "Database connection failed. Please check credentials."
        exit 1
    fi
}

# Initialize database schema
init_database() {
    log "Initializing database schema..."
    
    # Check if tables already exist
    local existing_tables=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = '$DB_NAME' 
        AND table_name IN ('benchmark_raw_data', 'model_update_history', 'benchmark_sources', 'collection_runs');
    " --skip-column-names)
    
    if [ "$existing_tables" -eq 4 ]; then
        warn "Database tables already exist. Skipping schema creation."
    else
        log "Creating database schema..."
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$BASE_DIR/sql/01-create-benchmark-warehouse.sql"
        log "✅ Database schema created"
    fi
}

# Create directories
create_directories() {
    log "Creating directory structure..."
    
    mkdir -p "$BASE_DIR/logs/daily"
    mkdir -p "$BASE_DIR/logs/hourly"
    mkdir -p "$BASE_DIR/logs/tests"
    mkdir -p "$BASE_DIR/cache"
    mkdir -p "$BASE_DIR/reports"
    
    log "✅ Directory structure created"
}

# Set permissions
set_permissions() {
    log "Setting file permissions..."
    
    chmod +x "$BASE_DIR/scripts/"*.sh
    chmod 755 "$BASE_DIR/logs"
    chmod 755 "$BASE_DIR/cache"
    
    log "✅ Permissions set"
}

# Test Claude CLI
test_claude() {
    log "Testing Claude CLI..."
    
    echo "Test: What is 2+2?" | claude --print > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Claude CLI working"
    else
        error "Claude CLI test failed"
        exit 1
    fi
}

# Test Redis
test_redis() {
    log "Testing Redis connection..."
    
    if redis-cli ping > /dev/null 2>&1; then
        log "✅ Redis connection successful"
    else
        warn "Redis connection failed. Cache features may not work."
    fi
}

# Show system status
show_status() {
    log "System setup complete!"
    
    echo
    echo "📊 Benchmark Automation System"
    echo "==============================="
    echo
    echo "📁 Base Directory: $BASE_DIR"
    echo "🗄️  Database: $DB_NAME"
    echo "📝 Logs: $BASE_DIR/logs/"
    echo "🔧 Scripts: $BASE_DIR/scripts/"
    echo
    echo "🚀 Ready to run:"
    echo "  Daily Collection: $BASE_DIR/scripts/daily-comprehensive-collection.sh"
    echo "  Hourly Updates: $BASE_DIR/scripts/hourly-model-updates.sh"
    echo
    echo "🧪 Test scripts:"
    echo "  Test Daily: $BASE_DIR/scripts/test-daily-collection.sh"
    echo "  Test Hourly: $BASE_DIR/scripts/test-hourly-updates.sh"
    echo
    echo "⚙️  Next steps:"
    echo "  1. Run test scripts to verify functionality"
    echo "  2. Set up cron jobs with: $BASE_DIR/scripts/setup-cron.sh"
    echo "  3. Monitor logs in: $BASE_DIR/logs/"
    echo
}

# Main setup
main() {
    log "=== Benchmark Automation System Setup ==="
    
    check_prerequisites
    test_database
    init_database
    create_directories
    set_permissions
    test_claude
    test_redis
    
    show_status
    
    log "=== Setup Complete ==="
}

# Run setup
main "$@"