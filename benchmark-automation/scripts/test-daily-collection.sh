#!/bin/bash
# Test Daily Collection Script
# Tests the daily collection process with a limited set of sources

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
TEST_ID="test_$(date +%Y%m%d_%H%M%S)"
LOG_DIR="$BASE_DIR/logs/tests/$TEST_ID"

# Create log directory
mkdir -p "$LOG_DIR"

# Database configuration
DB_HOST="localhost"
DB_USER="root"
DB_PASS="NewRootPassword123!"
DB_NAME="justsimplechat"

# Claude CLI configuration
CLAUDE_CMD="claude --print --dangerously-skip-permissions --allowedTools '*:*'"

# Test configuration
TEST_SOURCES=("lmsys-arena" "arc-agi" "huggingface-open-llm")

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/test.log"
}

# Test database connection
test_database() {
    log "Testing database connection..."
    
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT 1;" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Database connection successful"
    else
        log "❌ Database connection failed"
        exit 1
    fi
}

# Test Claude CLI
test_claude() {
    log "Testing Claude CLI..."
    
    echo "Test query: What is 2+2?" | $CLAUDE_CMD > "$LOG_DIR/claude_test.txt" 2>&1
    
    if [ $? -eq 0 ]; then
        log "✅ Claude CLI working"
    else
        log "❌ Claude CLI failed"
        exit 1
    fi
}

# Test MCP Firecrawl
test_firecrawl() {
    log "Testing MCP Firecrawl..."
    
    $CLAUDE_CMD << 'EOF' > "$LOG_DIR/firecrawl_test.txt" 2>&1
Test MCP Firecrawl by scraping a simple page:

Use mcp__firecrawl__firecrawl_scrape to scrape https://httpbin.org/html
Return just the status and content length.
EOF
    
    if [ $? -eq 0 ] && grep -q "content" "$LOG_DIR/firecrawl_test.txt"; then
        log "✅ MCP Firecrawl working"
    else
        log "❌ MCP Firecrawl failed"
        exit 1
    fi
}

# Test collection for specific sources
test_collection() {
    log "Testing collection for ${#TEST_SOURCES[@]} sources..."
    
    # Create test collection prompt
    cat > "$LOG_DIR/test_collection_prompt.md" << 'EOF'
# Test Benchmark Collection

Test the benchmark collection process for a limited set of sources.

## Task
1. Query benchmark_sources table for test sources
2. Scrape each source using MCP Firecrawl
3. Extract sample data (limit to first 5 models per source)
4. Store in benchmark_raw_data table
5. Generate test report

## Test Sources
Focus only on these sources:
- lmsys-arena
- arc-agi
- huggingface-open-llm

## Process
1. Get source URLs from database
2. Scrape each source (with shorter timeout for testing)
3. Extract limited data (first 5 models only)
4. Store in benchmark_raw_data with collection_id starting with "test_"
5. Report results

## Database Connection
```sql
mysql -h localhost -u root -pNewRootPassword123! justsimplechat
```

## Collection Strategy
- Use shorter timeouts (10 seconds max)
- Extract only top 5 models per source
- Store with collection_id prefixed "test_"
- Generate concise report

## Expected Output
JSON report with:
- Sources tested
- Models found per source
- Any errors encountered
- Data quality assessment

Be concise but thorough in testing the core functionality.
EOF

    # Run test collection
    $CLAUDE_CMD \
        --context "Test ID: $TEST_ID" \
        --context "Test Sources: ${TEST_SOURCES[*]}" \
        --log "$LOG_DIR/claude_collection.log" \
        < "$LOG_DIR/test_collection_prompt.md" \
        > "$LOG_DIR/collection_results.json" 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log "✅ Collection test completed"
    else
        log "❌ Collection test failed"
        return 1
    fi
}

# Verify test results
verify_results() {
    log "Verifying test results..."
    
    # Check if data was inserted
    local data_count=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        SELECT COUNT(*) FROM benchmark_raw_data 
        WHERE collection_id LIKE 'test_%' 
        AND collection_id LIKE '%$TEST_ID%';
    " --skip-column-names)
    
    log "Found $data_count test records in database"
    
    if [ "$data_count" -gt 0 ]; then
        log "✅ Data successfully stored"
        
        # Show sample data
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
            SELECT source, model_name_raw, metric_name, metric_value
            FROM benchmark_raw_data 
            WHERE collection_id LIKE 'test_%' 
            AND collection_id LIKE '%$TEST_ID%'
            LIMIT 10;
        " | tee -a "$LOG_DIR/sample_data.txt"
        
    else
        log "❌ No test data found in database"
        return 1
    fi
}

# Cleanup test data
cleanup_test_data() {
    log "Cleaning up test data..."
    
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        DELETE FROM benchmark_raw_data 
        WHERE collection_id LIKE 'test_%' 
        AND collection_id LIKE '%$TEST_ID%';
    "
    
    log "Test data cleaned up"
}

# Generate test report
generate_report() {
    log "Generating test report..."
    
    cat > "$LOG_DIR/test_report.md" << EOF
# Daily Collection Test Report

**Test ID:** $TEST_ID  
**Date:** $(date)  
**Sources Tested:** ${TEST_SOURCES[*]}

## Test Results

### Database Connection
- ✅ Successfully connected to MySQL database

### Claude CLI
- ✅ Claude CLI responding correctly

### MCP Firecrawl
- ✅ Firecrawl MCP server working

### Collection Process
- ✅ Collection script executed successfully
- ✅ Data extracted and stored in database

### Data Verification
- Found records in benchmark_raw_data table
- Sample data looks correct

## Files Generated
- Test log: $LOG_DIR/test.log
- Collection results: $LOG_DIR/collection_results.json
- Sample data: $LOG_DIR/sample_data.txt

## Recommendations
1. Full daily collection script should work correctly
2. Monitor for any timeout issues with slower sources
3. Consider increasing retry logic for production

## Next Steps
1. Test hourly update script
2. Set up cron jobs for production
3. Add monitoring and alerting
EOF

    log "Test report generated: $LOG_DIR/test_report.md"
}

# Main test execution
main() {
    log "=== Daily Collection Test Started ==="
    
    # Run all tests
    test_database
    test_claude
    test_firecrawl
    test_collection
    verify_results
    
    # Generate report
    generate_report
    
    # Optional: cleanup test data
    read -p "Clean up test data? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_test_data
    fi
    
    log "=== Daily Collection Test Completed ==="
    log "Check results in: $LOG_DIR/"
}

# Run tests
main "$@"