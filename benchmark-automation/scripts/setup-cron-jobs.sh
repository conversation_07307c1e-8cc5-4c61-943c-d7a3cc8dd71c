#!/bin/bash
# Cron Setup Script
# Sets up cron jobs for automated benchmark collection

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
USER=$(whoami)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Check if running as correct user
check_user() {
    log "Checking user permissions..."
    
    if [ "$USER" != "jayson_yorkshire3d_co_uk" ]; then
        warn "Running as $USER instead of jayson_yorkshire3d_co_uk"
        warn "Make sure this user has access to the database and claude CLI"
    fi
    
    log "✅ User check complete"
}

# Backup existing crontab
backup_crontab() {
    log "Backing up existing crontab..."
    
    if crontab -l > /dev/null 2>&1; then
        crontab -l > "$BASE_DIR/cron_backup_$(date +%Y%m%d_%H%M%S).txt"
        log "✅ Crontab backed up"
    else
        log "No existing crontab found"
    fi
}

# Create cron jobs
setup_cron_jobs() {
    log "Setting up cron jobs..."
    
    # Create temporary cron file
    local TEMP_CRON=$(mktemp)
    
    # Get existing cron jobs (if any)
    if crontab -l > /dev/null 2>&1; then
        crontab -l | grep -v "benchmark-automation" > "$TEMP_CRON" || true
    fi
    
    # Add benchmark automation jobs
    cat >> "$TEMP_CRON" << EOF

# Benchmark Automation System
# Daily comprehensive collection at 2:00 AM
0 2 * * * cd $BASE_DIR && ./scripts/daily-comprehensive-collection.sh >> logs/daily/cron.log 2>&1

# Hourly model updates (every hour from 3 AM to 11 PM)
0 3-23 * * * cd $BASE_DIR && ./scripts/hourly-model-updates.sh >> logs/hourly/cron.log 2>&1

# Weekly cleanup of old logs (Sunday at 1 AM)
0 1 * * 0 find $BASE_DIR/logs -type f -mtime +30 -delete 2>/dev/null || true

EOF
    
    # Install new crontab
    crontab "$TEMP_CRON"
    
    # Clean up
    rm "$TEMP_CRON"
    
    log "✅ Cron jobs installed"
}

# Show installed cron jobs
show_cron_jobs() {
    log "Installed cron jobs:"
    echo
    crontab -l | grep -A 10 "Benchmark Automation System"
    echo
}

# Create log rotation config
setup_log_rotation() {
    log "Setting up log rotation..."
    
    # Create logrotate config
    cat > "$BASE_DIR/logrotate.conf" << EOF
# Benchmark Automation Log Rotation
$BASE_DIR/logs/daily/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $USER $USER
}

$BASE_DIR/logs/hourly/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 $USER $USER
}
EOF
    
    log "✅ Log rotation configured"
    log "To enable system-wide log rotation, run as root:"
    log "sudo cp $BASE_DIR/logrotate.conf /etc/logrotate.d/benchmark-automation"
}

# Show monitoring commands
show_monitoring() {
    log "Monitoring commands:"
    echo
    echo "📊 Check cron logs:"
    echo "  tail -f $BASE_DIR/logs/daily/cron.log"
    echo "  tail -f $BASE_DIR/logs/hourly/cron.log"
    echo
    echo "📈 Check system logs:"
    echo "  sudo journalctl -u cron -f"
    echo "  grep CRON /var/log/syslog"
    echo
    echo "🔍 Check database activity:"
    echo "  mysql -u root -pNewRootPassword123! justsimplechat -e 'SELECT COUNT(*) FROM benchmark_raw_data WHERE DATE(scraped_at) = CURDATE();'"
    echo
    echo "📋 List cron jobs:"
    echo "  crontab -l"
    echo
}

# Main setup
main() {
    log "=== Cron Setup Started ==="
    
    check_user
    backup_crontab
    setup_cron_jobs
    setup_log_rotation
    
    show_cron_jobs
    show_monitoring
    
    echo
    log "=== Cron Setup Complete ==="
    echo
    echo "🚀 System is now running automatically:"
    echo "  Daily collection: 2:00 AM"
    echo "  Hourly updates: 3:00 AM - 11:00 PM"
    echo "  Log cleanup: Sunday 1:00 AM"
    echo
    echo "⚠️  Important Notes:"
    echo "  - Make sure Claude CLI is in PATH for cron"
    echo "  - Database credentials are accessible"
    echo "  - Check logs regularly for issues"
    echo "  - Test manually before relying on automation"
    echo
}

# Run setup
main "$@"