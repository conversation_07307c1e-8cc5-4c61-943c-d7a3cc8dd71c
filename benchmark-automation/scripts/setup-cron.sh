#!/bin/bash

# Setup cron jobs for automated benchmark collection

echo "🔧 Setting up cron jobs for benchmark automation..."

# Create a temporary cron file
CRON_FILE="/tmp/benchmark-cron-$$"

# Get current crontab (if exists)
crontab -l > "$CRON_FILE" 2>/dev/null || true

# Check if our jobs already exist
if grep -q "benchmark-automation" "$CRON_FILE"; then
    echo "⚠️  Benchmark automation cron jobs already exist!"
    echo "Remove them manually with 'crontab -e' if you want to reinstall"
    exit 1
fi

# Add our cron jobs
cat >> "$CRON_FILE" << 'EOF'

# JustSimpleChat Benchmark Automation
# Daily at 6 AM UTC - High priority benchmarks (LMSYS, ARC-AGI, HF)
0 6 * * * cd /var/www/dev/benchmark-automation && /usr/bin/node collectors/daily-collector.js >> logs/cron-daily.log 2>&1

# Weekly on Sundays at 2 AM UTC - Academic benchmarks
0 2 * * 0 cd /var/www/dev/benchmark-automation && /usr/bin/node collectors/weekly-collector.js >> logs/cron-weekly.log 2>&1

# Monthly on 1st at 3 AM UTC - Specialized benchmarks
0 3 1 * * cd /var/www/dev/benchmark-automation && /usr/bin/node collectors/monthly-collector.js >> logs/cron-monthly.log 2>&1

EOF

# Install the new crontab
crontab "$CRON_FILE"

# Clean up
rm -f "$CRON_FILE"

echo "✅ Cron jobs installed successfully!"
echo ""
echo "📅 Schedule:"
echo "  - Daily:   6:00 AM UTC (Tier 1 sources)"
echo "  - Weekly:  Sundays 2:00 AM UTC (Tier 2 sources)"
echo "  - Monthly: 1st of month 3:00 AM UTC (Tier 3 sources)"
echo ""
echo "📝 Logs will be written to:"
echo "  - logs/cron-daily.log"
echo "  - logs/cron-weekly.log"
echo "  - logs/cron-monthly.log"
echo ""
echo "To view installed cron jobs: crontab -l"
echo "To remove cron jobs: crontab -e (and delete the benchmark lines)"