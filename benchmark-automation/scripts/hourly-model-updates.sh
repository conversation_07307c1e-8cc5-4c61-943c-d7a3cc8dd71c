#!/bin/bash
# Hourly Intelligent Model Updates Script
# Runs every hour to update 10 models with latest benchmark data

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
UPDATE_ID=$(date +%Y%m%d_%H%M%S)
LOG_DIR="$BASE_DIR/logs/hourly/$UPDATE_ID"
PROMPT_DIR="$BASE_DIR/prompts"

# Create log directory
mkdir -p "$LOG_DIR"

# Database configuration
DB_HOST="localhost"
DB_USER="root"
DB_PASS="NewRootPassword123!"
DB_NAME="justsimplechat"

# Claude CLI configuration
CLAUDE_CMD="claude --print --dangerously-skip-permissions --allowedTools '*:*'"

# Configuration
MODELS_PER_RUN=10

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/update.log"
}

# Get models that need checking
get_models_to_check() {
    log "Getting $MODELS_PER_RUN models that need checking..."
    
    # Query for models that need checking (least recently checked first)
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
        SELECT m.canonicalName 
        FROM Models m
        LEFT JOIN model_update_history h ON m.canonicalName = h.canonical_name
        WHERE m.isEnabled = 1
        ORDER BY h.last_checked ASC NULLS FIRST
        LIMIT $MODELS_PER_RUN;
    " --skip-column-names > "$LOG_DIR/models_to_check.txt"
    
    local model_count=$(wc -l < "$LOG_DIR/models_to_check.txt")
    log "Found $model_count models to check"
    
    if [ $model_count -eq 0 ]; then
        log "No models to check, exiting"
        exit 0
    fi
}

# Main update function
run_model_updates() {
    log "Starting intelligent model updates..."
    
    # Create model update prompt
    cat > "$LOG_DIR/update_prompt.md" << 'EOF'
# Intelligent Model Score Updates

You are an AI model benchmark specialist. Your task is to intelligently update scores for specific models using all available benchmark data.

## Task Overview
1. For each model in the provided list, analyze ALL benchmark data
2. Intelligently match model name variations to find relevant scores
3. Aggregate scores from multiple sources with proper weighting
4. Update database only when scores have significantly changed
5. Track all checks and updates for audit purposes

## Database Connection
```sql
mysql -h localhost -u root -pNewRootPassword123! justsimplechat
```

## Models to Check
Read the list of models from models_to_check.txt - these are the models that need score updates.

## Update Process

### 1. For Each Model, Find All Benchmark Data
Example: For model "anthropic/claude-3.7-sonnet", search for variations:
```sql
SELECT * FROM benchmark_raw_data 
WHERE model_name_raw LIKE '%claude%' 
  AND model_name_raw LIKE '%3.7%'
  AND scraped_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY scraped_at DESC;
```

Intelligent matching patterns:
- "anthropic/claude-3.7-sonnet" matches:
  - "Claude 3.7"
  - "Claude 3.7 (16K)"
  - "Claude-3.7-Sonnet"
  - "Anthropic Claude 3.7"
  - "Claude 3.7 (8K)"

### 2. Aggregate Scores by Category
For each model, calculate updated scores for our router categories:
- **general_chat**: LMSYS Arena ELO → 0-100 scale
- **reasoning**: ARC-AGI-2, MATH, etc. → direct percentages
- **coding**: HumanEval, LiveCodeBench → direct percentages
- **math**: GSM8K, MATH → direct percentages

### 3. Score Weighting by Source Reliability
```
Source Reliability Weights:
- LMSYS Arena: 0.9 (high reliability)
- ARC-AGI: 0.9 (high reliability)
- HumanEval: 0.8 (good reliability)
- HuggingFace: 0.7 (moderate reliability)
- Blog posts: 0.3 (low reliability)
```

### 4. Check Current Scores
```sql
SELECT canonicalName, 
       JSON_EXTRACT(extendedMetadata, '$.categoryScores') as current_scores
FROM Models 
WHERE canonicalName = ?;
```

### 5. Update Decision Logic
Update scores only if:
- New score differs by >5 points from current
- New data is from last 7 days
- Confidence level is high (multiple sources agree)
- OR current score is >30 days old

### 6. Update Database
```sql
UPDATE Models 
SET extendedMetadata = JSON_SET(
    extendedMetadata,
    '$.categoryScores.general_chat',
    JSON_OBJECT(
        'score', ?,
        'source', 'aggregated',
        'updated', NOW(),
        'confidence', 'high',
        'metadata', JSON_OBJECT(
            'sources', ?,
            'dataPoints', ?,
            'lastUpdated', NOW()
        )
    )
)
WHERE canonicalName = ?;
```

### 7. Track Updates
```sql
INSERT INTO model_update_history (canonical_name, last_checked, last_updated, last_score_changes)
VALUES (?, NOW(), ?, ?)
ON DUPLICATE KEY UPDATE
    last_checked = NOW(),
    last_updated = CASE WHEN VALUES(last_updated) IS NOT NULL THEN VALUES(last_updated) ELSE last_updated END,
    check_count = check_count + 1,
    update_count = update_count + CASE WHEN VALUES(last_updated) IS NOT NULL THEN 1 ELSE 0 END,
    last_score_changes = VALUES(last_score_changes);
```

### 8. Use MCP Perplexity for Missing Data
If no recent benchmark data found, use Perplexity to search for:
- Recent blog posts about model performance
- Benchmark results from papers
- Performance claims from model announcements

Example query: "latest benchmark results for Claude 3.7 January 2025"

### 9. Clear Redis Cache
For any updated models, clear relevant cache entries:
```bash
redis-cli DEL "router:*" "model:*"
```

## Example Workflow

For model "openai/gpt-4o-latest":

1. Find variations: "GPT-4o-latest", "GPT-4o (Latest)", "ChatGPT-4o-latest"
2. Aggregate data:
   - LMSYS: 1287 ELO → 91.5 general_chat
   - HumanEval: 89.2% → 89.2 coding
   - ARC-AGI: 45.3% → 45.3 reasoning
3. Compare with current scores
4. Update if significant changes
5. Record in history

## Output Format
Generate a JSON report with:
```json
{
  "update_id": "20250717_140000",
  "models_checked": 10,
  "models_updated": 3,
  "updates": [
    {
      "model": "openai/gpt-4o-latest",
      "category": "general_chat",
      "old_score": 88.5,
      "new_score": 91.5,
      "sources": ["lmsys-arena"],
      "confidence": "high"
    }
  ],
  "no_updates": ["anthropic/claude-3.7-sonnet"],
  "errors": []
}
```

## Important Notes
- Be conservative with updates - only update when confident
- Consider score trends, not just single data points
- Weight recent data higher than old data
- Handle model naming variations intelligently
- Record detailed audit trail for all decisions

Your goal: Intelligently update model scores with the best available data while maintaining high quality and auditability.
EOF

    # Get models to check
    get_models_to_check
    
    # Run the updates using Claude CLI
    log "Executing Claude CLI updates..."
    
    $CLAUDE_CMD \
        --context "Update ID: $UPDATE_ID" \
        --context "Log Directory: $LOG_DIR" \
        --context "Models File: $LOG_DIR/models_to_check.txt" \
        --log "$LOG_DIR/claude_output.log" \
        < "$LOG_DIR/update_prompt.md" \
        > "$LOG_DIR/update_results.json" 2>&1
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log "Updates completed successfully"
    else
        log "Updates completed with errors (exit code: $exit_code)"
    fi
    
    return $exit_code
}

# Process update results
process_results() {
    log "Processing update results..."
    
    # Check if results file exists and is valid JSON
    if [ ! -f "$LOG_DIR/update_results.json" ]; then
        log "No results file found"
        return 1
    fi
    
    # Validate JSON
    if ! jq . "$LOG_DIR/update_results.json" > /dev/null 2>&1; then
        log "Invalid JSON in results file"
        return 1
    fi
    
    # Extract statistics
    local models_checked=$(jq -r '.models_checked // 0' "$LOG_DIR/update_results.json")
    local models_updated=$(jq -r '.models_updated // 0' "$LOG_DIR/update_results.json")
    local updates_count=$(jq -r '.updates | length' "$LOG_DIR/update_results.json" 2>/dev/null || echo "0")
    
    log "Update statistics: $models_checked checked, $models_updated updated, $updates_count score changes"
    
    # Clear Redis cache if any updates were made
    if [ "$models_updated" -gt 0 ]; then
        log "Clearing Redis cache due to model updates..."
        redis-cli DEL "router:*" "model:*" > /dev/null 2>&1 || true
    fi
}

# Generate update report
generate_update_report() {
    log "Generating update report..."
    
    $CLAUDE_CMD << EOF > "$LOG_DIR/update_report.md"
Generate a summary report for model update run $UPDATE_ID.

Analyze the results in $LOG_DIR/update_results.json and create a markdown report with:
1. Summary of models checked and updated
2. Detailed list of score changes
3. Sources used for each update
4. Any models that couldn't be updated and why
5. Recommendations for improving data coverage

Include specific examples of the most significant score changes.
EOF
    
    log "Update report generated: $LOG_DIR/update_report.md"
}

# Cleanup old logs (keep last 7 days for hourly logs)
cleanup_logs() {
    find "$BASE_DIR/logs/hourly" -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    log "Cleaned up old log files"
}

# Main execution
main() {
    log "=== Hourly Model Updates Started ==="
    
    # Run updates
    if run_model_updates; then
        process_results
        generate_update_report
        log "=== Updates Completed Successfully ==="
    else
        log "=== Updates Completed with Errors ==="
    fi
    
    # Cleanup
    cleanup_logs
    
    log "=== Hourly Updates Finished ==="
}

# Run main function
main "$@"