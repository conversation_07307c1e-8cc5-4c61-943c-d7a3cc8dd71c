#!/usr/bin/env node

/**
 * Test Benchmark Collection with Real MCP Integration
 * This script should be run FROM WITHIN Claude Code to access MCP servers
 */

const modelMapper = require('./utils/model-mapper');
const dataNormalizer = require('./processors/data-normalizer');
const databaseUpdater = require('./utils/database-updater');
const benchmarkSources = require('./config/benchmark-sources.json');
const fs = require('fs').promises;
const path = require('path');

// Test configuration
const TEST_SOURCES = [
  'lmsys-arena',
  'arc-agi',
  'huggingface-open-llm',
  'berkeley-function-calling',
  'aider-code-editing'
];

// Store results
const testResults = {
  timestamp: new Date().toISOString(),
  environment: 'Claude Code with MCP',
  results: []
};

/**
 * Extract scores from scraped content
 */
function extractScoresFromContent(content, sourceName) {
  const scores = [];
  const lines = content.split('\n');
  
  switch(sourceName) {
    case 'lmsys-arena':
      // Look for Elo ratings in tables
      for (const line of lines) {
        // Pattern: | Model Name | 1234 |
        if (line.includes('|') && /\d{4}/.test(line)) {
          const parts = line.split('|').map(p => p.trim()).filter(p => p);
          if (parts.length >= 2) {
            const modelName = parts[0];
            const eloMatch = parts[1].match(/(\d{4})/);
            if (eloMatch) {
              scores.push({
                model: modelName,
                elo: parseInt(eloMatch[1]),
                category: 'general_chat'
              });
            }
          }
        }
      }
      break;
      
    case 'arc-agi':
      // Look for percentage scores in the table we just scraped
      const tableSection = content.split('| AI System')[1];
      if (tableSection) {
        const rows = tableSection.split('\n');
        for (const row of rows) {
          if (row.includes('|') && row.includes('%')) {
            const parts = row.split('|').map(p => p.trim());
            if (parts.length >= 5) {
              const model = parts[0];
              const arc2Score = parts[4]; // ARC-AGI-2 column
              const scoreMatch = arc2Score.match(/(\d+\.?\d*)%/);
              if (scoreMatch) {
                scores.push({
                  model: model,
                  score: parseFloat(scoreMatch[1]),
                  category: 'reasoning'
                });
              }
            }
          }
        }
      }
      break;
      
    case 'huggingface-open-llm':
      // Look for scores in HuggingFace format
      for (const line of lines) {
        // Pattern: 1. ModelName - Score: XX.X
        const match = line.match(/^\d+\.\s+(.+?)\s+-\s+Score:\s+(\d+\.?\d*)/);
        if (match) {
          scores.push({
            model: match[1],
            score: parseFloat(match[2]),
            category: 'general_chat'
          });
        }
      }
      break;
  }
  
  return scores;
}

/**
 * Test a single source with real MCP
 */
async function testSourceWithMCP(sourceName) {
  const source = benchmarkSources[sourceName];
  if (!source) {
    return { error: 'Source not found in config' };
  }
  
  console.log(`\nTesting ${sourceName}...`);
  console.log(`URL: ${source.url}`);
  
  const result = {
    source: sourceName,
    url: source.url,
    timestamp: new Date().toISOString(),
    status: 'pending'
  };
  
  try {
    // Use real MCP Firecrawl
    console.log('  Scraping with Firecrawl MCP...');
    const scrapeResult = await global.mcp__firecrawl__firecrawl_scrape({
      url: source.url,
      formats: ['markdown'],
      onlyMainContent: true,
      waitFor: source.scrapeOptions?.waitFor || 3000,
      timeout: 30000
    });
    
    if (!scrapeResult.markdown) {
      throw new Error('No content retrieved');
    }
    
    result.contentLength = scrapeResult.markdown.length;
    console.log(`  ✅ Scraped ${result.contentLength} characters`);
    
    // Save raw content for debugging
    const debugDir = path.join(__dirname, 'test-results', 'mcp-scrapes');
    await fs.mkdir(debugDir, { recursive: true });
    await fs.writeFile(
      path.join(debugDir, `${sourceName}-${Date.now()}.md`),
      scrapeResult.markdown
    );
    
    // Extract scores
    const scores = extractScoresFromContent(scrapeResult.markdown, sourceName);
    result.scoresFound = scores.length;
    result.sampleScores = scores.slice(0, 5);
    
    console.log(`  ✅ Found ${scores.length} scores`);
    
    // Map model names
    let mapped = 0;
    const unmapped = [];
    for (const score of scores) {
      const mappedName = modelMapper.mapModelName(score.model);
      if (mappedName) {
        mapped++;
      } else {
        unmapped.push(score.model);
      }
    }
    
    result.mapped = mapped;
    result.unmapped = unmapped.slice(0, 5); // First 5 unmapped
    result.status = 'success';
    
  } catch (error) {
    result.status = 'failed';
    result.error = error.message;
    console.error(`  ❌ Error: ${error.message}`);
  }
  
  return result;
}

/**
 * Run tests on selected sources
 */
async function runTests() {
  console.log('🧪 Testing Benchmark Collection with Real MCP');
  console.log('📅 Date: July 17, 2025');
  console.log(`📊 Testing ${TEST_SOURCES.length} sources\n`);
  
  // Initialize components
  await modelMapper.init();
  await dataNormalizer.init();
  
  // Test each source
  for (const sourceName of TEST_SOURCES) {
    const result = await testSourceWithMCP(sourceName);
    testResults.results.push(result);
    
    // Delay between requests
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // Generate report
  await generateReport();
}

/**
 * Generate test report
 */
async function generateReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST REPORT');
  console.log('='.repeat(60));
  
  const successful = testResults.results.filter(r => r.status === 'success').length;
  const failed = testResults.results.filter(r => r.status === 'failed').length;
  const totalScores = testResults.results.reduce((sum, r) => sum + (r.scoresFound || 0), 0);
  
  console.log(`\nSummary:`);
  console.log(`  Sources tested: ${testResults.results.length}`);
  console.log(`  Successful: ${successful}`);
  console.log(`  Failed: ${failed}`);
  console.log(`  Total scores found: ${totalScores}`);
  
  console.log('\nDetailed Results:');
  console.log('Source                    | Status | Scores | Mapped | Sample');
  console.log('-'.repeat(70));
  
  for (const result of testResults.results) {
    const status = result.status === 'success' ? '✅' : '❌';
    const scores = result.scoresFound || 0;
    const mapped = result.mapped || 0;
    const sample = result.sampleScores?.[0] 
      ? `${result.sampleScores[0].model}: ${result.sampleScores[0].score || result.sampleScores[0].elo}`
      : result.error || 'N/A';
    
    console.log(
      `${result.source.padEnd(24)} | ${status}     | ${String(scores).padEnd(6)} | ${String(mapped).padEnd(6)} | ${sample}`
    );
  }
  
  // Save report
  const reportPath = path.join(__dirname, 'test-results', `mcp-test-${Date.now()}.json`);
  await fs.mkdir(path.dirname(reportPath), { recursive: true });
  await fs.writeFile(reportPath, JSON.stringify(testResults, null, 2));
  
  console.log(`\n📄 Report saved to: ${path.basename(reportPath)}`);
  
  // Recommendations
  if (failed > 0) {
    console.log('\n⚠️ Failed sources need investigation:');
    testResults.results
      .filter(r => r.status === 'failed')
      .forEach(r => console.log(`  - ${r.source}: ${r.error}`));
  }
  
  console.log('\n✅ MCP integration is working!');
  console.log('\nNext steps:');
  console.log('1. Fix extraction patterns for sources with 0 scores');
  console.log('2. Update model mappings for unmapped models');
  console.log('3. Test remaining 20+ sources');
  console.log('4. Implement hourly collection schedule');
}

// Check if running in Claude Code environment
if (typeof global.mcp__firecrawl__firecrawl_scrape === 'undefined') {
  console.error('❌ This script must be run from within Claude Code to access MCP servers!');
  console.error('Please run this script through Claude Code, not directly with Node.js.');
  process.exit(1);
}

// Run tests
runTests().catch(console.error);