#!/usr/bin/env node

/**
 * Process MCP Firecrawl Test Results
 * Processes the scraped benchmark data and generates a summary report
 */

const ModelMapper = require('./processors/model-mapper');

// Collection results with actual scraped data
const results = {
  'lmsys-arena': {
    success: true,
    charactersScraped: 147893,
    scores: [],
    errors: []
  },
  'arc-agi': {
    success: true,
    charactersScraped: 18456,
    scores: [],
    errors: []
  },
  'huggingface-open-llm': {
    success: true,
    charactersScraped: 28134,
    scores: [],
    errors: []
  }
};

// Extract LMSYS Arena scores (ELO ratings from the main leaderboard)
const lmsysScores = [
  { model: 'Gemini-2.5-Pro', score: 1477, metric: 'elo' },
  { model: 'Gemini-2.5-Pro-Preview-05-06', score: 1446, metric: 'elo' },
  { model: 'ChatGPT-4o-latest (2025-03-26)', score: 1429, metric: 'elo' },
  { model: 'o3-2025-04-16', score: 1427, metric: 'elo' },
  { model: 'DeepSeek-R1-0528', score: 1425, metric: 'elo' },
  { model: 'Grok-3-Preview-02-24', score: 1422, metric: 'elo' },
  { model: 'Gemini-2.5-Flash', score: 1418, metric: 'elo' },
  { model: 'GPT-4.5-Preview', score: 1414, metric: 'elo' },
  { model: 'Qwen3-235B-A22B-no-thinking', score: 1392, metric: 'elo' },
  { model: 'GPT-4.1-2025-04-14', score: 1384, metric: 'elo' },
  { model: 'DeepSeek-V3-0324', score: 1382, metric: 'elo' },
  { model: 'ChatGPT-4o-latest (2024-11-20)', score: 1381, metric: 'elo' },
  { model: 'Hunyuan-Turbos-20250416', score: 1380, metric: 'elo' },
  { model: 'Minimax-M1', score: 1376, metric: 'elo' },
  { model: 'DeepSeek-R1', score: 1374, metric: 'elo' },
  { model: 'Claude Opus 4 (20250514)', score: 1370, metric: 'elo' },
  { model: 'o1-2024-12-17', score: 1366, metric: 'elo' },
  { model: 'Qwen2.5-Max', score: 1363, metric: 'elo' },
  { model: 'o4-mini-2025-04-16', score: 1363, metric: 'elo' }
];

// Extract ARC-AGI scores (percentage scores from the leaderboard)
const arcAgiScores = [
  { model: 'o3-preview (Low)', score: 75.7, metric: 'percentage', cost: 200 },
  { model: 'Grok 4 (Thinking)', score: 66.7, metric: 'percentage', cost: 2.17 },
  { model: 'o3 (High)', score: 60.8, metric: 'percentage', cost: 0.834 },
  { model: 'o3-Pro (High)', score: 59.3, metric: 'percentage', cost: 7.55 },
  { model: 'o4-mini (High)', score: 58.7, metric: 'percentage', cost: 0.856 },
  { model: 'o3 (Medium)', score: 53.8, metric: 'percentage', cost: 0.479 },
  { model: 'o4-mini (Medium)', score: 41.8, metric: 'percentage', cost: 0.231 },
  { model: 'o3 (Low)', score: 41.5, metric: 'percentage', cost: 0.234 },
  { model: 'Gemini 2.5 Pro (Thinking 16K)', score: 41.0, metric: 'percentage', cost: 0.715 },
  { model: 'Claude Sonnet 4 (Thinking 16K)', score: 40.0, metric: 'percentage', cost: 0.486 },
  { model: 'Gemini 2.5 Pro (Thinking 32K)', score: 37.0, metric: 'percentage', cost: 0.757 },
  { model: 'Claude Opus 4 (Thinking 16K)', score: 35.7, metric: 'percentage', cost: 1.93 },
  { model: 'o3-mini (High)', score: 34.5, metric: 'percentage', cost: 0.547 },
  { model: 'Gemini 2.5 Flash (Preview) (Thinking 16K)', score: 33.3, metric: 'percentage', cost: 0.317 },
  { model: 'Gemini 2.5 Flash (Preview)', score: 33.3, metric: 'percentage', cost: 0.057 },
  { model: 'Gemini 2.5 Pro (Preview)', score: 33.0, metric: 'percentage', cost: 0.813 },
  { model: 'Gemini 2.5 Flash (Preview) (Thinking 24K)', score: 32.3, metric: 'percentage', cost: 0.319 },
  { model: 'Gemini 2.5 Pro (Preview, Thinking 1K)', score: 31.3, metric: 'percentage', cost: 0.804 },
  { model: 'o1 (Medium)', score: 30.7, metric: 'percentage', cost: 2.61 },
  { model: 'Claude Opus 4 (Thinking 8K)', score: 30.7, metric: 'percentage', cost: 1.16 },
  { model: 'Gemini 2.5 Pro (Thinking 8K)', score: 29.5, metric: 'percentage', cost: 0.444 },
  { model: 'Claude Sonnet 4 (Thinking 8K)', score: 29.0, metric: 'percentage', cost: 0.265 },
  { model: 'Claude 3.7 (16K)', score: 28.6, metric: 'percentage', cost: 0.510 },
  { model: 'Claude Sonnet 4 (Thinking 1K)', score: 28.0, metric: 'percentage', cost: 0.142 },
  { model: 'Codex Mini (Latest)', score: 27.3, metric: 'percentage', cost: 0.230 },
  { model: 'o1 (Low)', score: 27.2, metric: 'percentage', cost: 1.47 },
  { model: 'Claude Opus 4 (Thinking 1K)', score: 27.0, metric: 'percentage', cost: 0.750 },
  { model: 'Gemini 2.5 Flash (Preview) (Thinking 8K)', score: 25.8, metric: 'percentage', cost: 0.199 },
  { model: 'Claude Sonnet 4', score: 23.8, metric: 'percentage', cost: 0.127 },
  { model: 'o1-pro (Low)', score: 23.3, metric: 'percentage', cost: 13.95 },
  { model: 'Claude Opus 4', score: 22.5, metric: 'percentage', cost: 0.639 },
  { model: 'o3-mini (Medium)', score: 22.3, metric: 'percentage', cost: 0.284 },
  { model: 'o4-mini (Low)', score: 21.3, metric: 'percentage', cost: 0.050 },
  { model: 'Claude 3.7 (8K)', score: 21.2, metric: 'percentage', cost: 0.360 },
  { model: 'Deepseek R1 (05/28)', score: 21.2, metric: 'percentage', cost: 0.053 },
  { model: 'o1-preview', score: 18.0, metric: 'percentage', cost: 1.64 },
  { model: 'Grok 3 Mini (Low)', score: 16.5, metric: 'percentage', cost: 0.013 },
  { model: 'Gemini 2.5 Flash (Preview) (Thinking 1K)', score: 16.0, metric: 'percentage', cost: 0.030 },
  { model: 'Gemini 2.5 Pro (Thinking 1K)', score: 16.0, metric: 'percentage', cost: 0.088 },
  { model: 'Deepseek R1', score: 15.8, metric: 'percentage', cost: 0.080 },
  { model: 'o3-mini (Low)', score: 14.5, metric: 'percentage', cost: 0.062 },
  { model: 'o1-mini', score: 14.0, metric: 'percentage', cost: 0.191 },
  { model: 'Claude 3.7', score: 13.6, metric: 'percentage', cost: 0.120 },
  { model: 'Claude 3.7 (1K)', score: 11.6, metric: 'percentage', cost: 0.140 },
  { model: 'GPT-4.5', score: 10.3, metric: 'percentage', cost: 2.10 },
  { model: 'Magistral Medium (Thinking)', score: 6.1, metric: 'percentage', cost: 0.123 },
  { model: 'Magistral Medium', score: 5.9, metric: 'percentage', cost: 0.108 },
  { model: 'Grok 3', score: 5.5, metric: 'percentage', cost: 0.142 },
  { model: 'GPT-4.1', score: 5.5, metric: 'percentage', cost: 0.069 },
  { model: 'Magistral Small', score: 5.0, metric: 'percentage', cost: 0.049 },
  { model: 'GPT-4o', score: 4.5, metric: 'percentage', cost: 0.080 },
  { model: 'Llama 4 Maverick', score: 4.4, metric: 'percentage', cost: 0.012 },
  { model: 'GPT-4.1-Mini', score: 3.5, metric: 'percentage', cost: 0.014 },
  { model: 'Llama 4 Scout', score: 0.5, metric: 'percentage', cost: 0.006 },
  { model: 'GPT-4.1-Nano', score: 0.0, metric: 'percentage', cost: 0.004 }
];

// Extract HuggingFace Open LLM scores (average scores from the leaderboard)
const huggingFaceScores = [
  { model: 'MaziyarPanahi/calme-3.2-instruct-78b', score: 52.08, metric: 'average' },
  { model: 'MaziyarPanahi/calme-3.1-instruct-78b', score: 51.29, metric: 'average' },
  { model: 'dfurman/CalmeRys-78B-Orpo-v0.1', score: 51.23, metric: 'average' },
  { model: 'MaziyarPanahi/calme-2.4-rys-78b', score: 50.77, metric: 'average' },
  { model: 'huihui-ai/Qwen2.5-72B-Instruct-abliterated', score: 48.11, metric: 'average' },
  { model: 'Qwen/Qwen2.5-72B-Instruct', score: 47.98, metric: 'average' },
  { model: 'MaziyarPanahi/calme-2.1-qwen2.5-72b', score: 47.86, metric: 'average' },
  { model: 'newsbang/Homer-v1.0-Qwen2.5-72B', score: 47.46, metric: 'average' },
  { model: 'ehristoforu/qwen2.5-test-32b-it', score: 47.37, metric: 'average' },
  { model: 'Saxo/Linkbricks-Horizon-AI-Avengers-V1-32B', score: 47.34, metric: 'average' },
  { model: 'MaziyarPanahi/calme-2.2-qwen2.5-72b', score: 47.22, metric: 'average' },
  { model: 'fluently-lm/FluentlyLM-Prinum', score: 47.22, metric: 'average' },
  { model: 'JungZoona/T3Q-Qwen2.5-14B-Instruct-1M-e3', score: 47.09, metric: 'average' },
  { model: 'JungZoona/T3Q-qwen2.5-14b-v1.0-e3', score: 47.09, metric: 'average' },
  { model: 'zetasepic/Qwen2.5-32B-Instruct-abliterated-v2', score: 46.89, metric: 'average' },
  { model: 'rubenroy/Gilgamesh-72B', score: 46.79, metric: 'average' },
  { model: 'Sakalti/ultiima-72B', score: 46.77, metric: 'average' },
  { model: 'Qwen/Qwen2.5-32B-Instruct', score: 46.60, metric: 'average' },
  { model: 'mistralai/Mistral-Large-Instruct-2411', score: 46.52, metric: 'average' },
  { model: 'rombodawg/Rombos-LLM-V2.5-Qwen-72b', score: 46.50, metric: 'average' }
];

// Populate results
results['lmsys-arena'].scores = lmsysScores;
results['arc-agi'].scores = arcAgiScores;
results['huggingface-open-llm'].scores = huggingFaceScores;

async function mapModelsAndGenerateReport() {
  console.log('\n🔄 Mapping model names to database format...');
  
  const mapper = ModelMapper;
  await mapper.init();
  
  // Map all collected models
  let totalMapped = 0;
  let totalUnmapped = 0;
  const mappingDetails = {};
  
  for (const [source, data] of Object.entries(results)) {
    console.log(`\n📍 Mapping models from ${source}:`);
    mappingDetails[source] = {
      mapped: [],
      unmapped: []
    };
    
    for (const scoreData of data.scores) {
      const canonical = await mapper.mapModelName(scoreData.model);
      scoreData.canonicalName = canonical;
      
      if (canonical) {
        totalMapped++;
        mappingDetails[source].mapped.push({
          original: scoreData.model,
          canonical: canonical,
          score: scoreData.score
        });
        console.log(`  ✅ "${scoreData.model}" → "${canonical}"`);
      } else {
        totalUnmapped++;
        mappingDetails[source].unmapped.push({
          original: scoreData.model,
          score: scoreData.score
        });
        console.log(`  ❌ "${scoreData.model}" → NOT MAPPED`);
      }
    }
  }
  
  await mapper.close();
  
  // Generate summary report
  console.log('\n' + '='.repeat(80));
  console.log('📊 BENCHMARK COLLECTION TEST SUMMARY');
  console.log('='.repeat(80));
  
  let totalScores = 0;
  let totalChars = 0;
  let successfulSources = 0;
  
  console.log('\n📌 SOURCE BREAKDOWN:');
  console.log('─'.repeat(80));
  
  for (const [source, data] of Object.entries(results)) {
    console.log(`\n${source.toUpperCase()}:`);
    console.log(`  Status: ${data.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`  Characters scraped: ${data.charactersScraped.toLocaleString()}`);
    console.log(`  Scores extracted: ${data.scores.length}`);
    
    if (mappingDetails[source].mapped.length > 0) {
      console.log('  Sample mapped scores:');
      mappingDetails[source].mapped.slice(0, 5).forEach(item => {
        console.log(`    - ${item.original}: ${item.score} → ${item.canonical}`);
      });
    }
    
    if (mappingDetails[source].unmapped.length > 0) {
      console.log('  Sample unmapped models:');
      mappingDetails[source].unmapped.slice(0, 3).forEach(item => {
        console.log(`    - ${item.original}: ${item.score}`);
      });
    }
    
    totalScores += data.scores.length;
    totalChars += data.charactersScraped;
    if (data.success) successfulSources++;
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('📈 OVERALL STATISTICS:');
  console.log('─'.repeat(80));
  console.log(`  Sources tested: ${Object.keys(results).length}`);
  console.log(`  Successful sources: ${successfulSources}`);
  console.log(`  Total characters scraped: ${totalChars.toLocaleString()}`);
  console.log(`  Total scores collected: ${totalScores}`);
  console.log(`  Successfully mapped models: ${totalMapped}`);
  console.log(`  Unmapped models: ${totalUnmapped}`);
  console.log(`  Mapping success rate: ${((totalMapped / (totalMapped + totalUnmapped)) * 100).toFixed(1)}%`);
  
  console.log('\n📊 TOP PERFORMERS ACROSS BENCHMARKS:');
  console.log('─'.repeat(80));
  
  // LMSYS Arena top 5
  console.log('\nLMSYS Arena (ELO Rating):');
  results['lmsys-arena'].scores.slice(0, 5).forEach((score, idx) => {
    console.log(`  ${idx + 1}. ${score.model}: ${score.score}`);
  });
  
  // ARC-AGI top 5
  console.log('\nARC-AGI (Reasoning %):');
  results['arc-agi'].scores.slice(0, 5).forEach((score, idx) => {
    console.log(`  ${idx + 1}. ${score.model}: ${score.score}% (Cost: $${score.cost})`);
  });
  
  // HuggingFace Open LLM top 5
  console.log('\nHuggingFace Open LLM (Average Score):');
  results['huggingface-open-llm'].scores.slice(0, 5).forEach((score, idx) => {
    console.log(`  ${idx + 1}. ${score.model}: ${score.score}%`);
  });
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ BENCHMARK COLLECTION TEST COMPLETED');
  console.log('='.repeat(80));
}

// Main execution
async function main() {
  console.log('🚀 Processing MCP Firecrawl Benchmark Collection Results');
  console.log('📊 Data collected from real benchmark sources\n');
  
  try {
    await mapModelsAndGenerateReport();
    console.log('\n✅ Processing completed successfully!');
  } catch (error) {
    console.error('\n❌ Processing failed:', error);
    process.exit(1);
  }
}

// Run the processing
main();