#!/usr/bin/env node

/**
 * Test database update with real benchmark data
 * Using correct model names from our database
 */

const databaseUpdater = require('./utils/database-updater');
const path = require('path');

async function testDatabaseUpdate() {
  console.log('🧪 Testing Database Update with Real Benchmark Data');
  console.log('📅 Date: July 17, 2025\n');
  
  try {
    await databaseUpdater.init();
    
    // Test scores using CORRECT model names from our database
    const testScores = [
      {
        modelName: 'openai/chatgpt-4o-latest',
        category: 'general_chat',
        score: 91.5,
        source: 'lmsys-arena-jul17-2025',
        confidence: 'high',
        timestamp: new Date().toISOString(),
        metadata: { 
          elo: 1429,
          sourceUrl: 'https://huggingface.co/spaces/lmsys/chatbot-arena-leaderboard',
          dataFormat: 'elo-to-percent'
        }
      },
      {
        modelName: 'gemini/gemini-2.5-pro',
        category: 'general_chat',
        score: 94.5,
        source: 'lmsys-arena-jul17-2025',
        confidence: 'high',
        timestamp: new Date().toISOString(),
        metadata: { 
          elo: 1477,
          sourceUrl: 'https://huggingface.co/spaces/lmsys/chatbot-arena-leaderboard',
          dataFormat: 'elo-to-percent'
        }
      },
      {
        modelName: 'xai/grok-4-0709',
        category: 'reasoning',
        score: 16.0,
        source: 'arc-agi-2-jul17-2025',
        confidence: 'high',
        timestamp: new Date().toISOString(),
        metadata: { 
          rawScore: 16.0,
          sourceUrl: 'https://arcprize.org/leaderboard',
          dataFormat: 'percentage',
          benchmarkVersion: 'ARC-AGI-2',
          costPerTask: 2.17,
          note: 'Leading model on ARC-AGI-2 benchmark'
        }
      },
      {
        modelName: 'openai/o3-2025-04-16',
        category: 'reasoning',
        score: 4.0,
        source: 'arc-agi-2-jul17-2025',
        confidence: 'high',
        timestamp: new Date().toISOString(),
        metadata: { 
          rawScore: 4.0,
          sourceUrl: 'https://arcprize.org/leaderboard',
          dataFormat: 'percentage',
          benchmarkVersion: 'ARC-AGI-2',
          costPerTask: 200.0,
          note: 'o3-preview (Low) score on ARC-AGI-2'
        }
      },
      {
        modelName: 'anthropic/claude-opus-4-0',
        category: 'reasoning',
        score: 8.6,
        source: 'arc-agi-2-jul17-2025',
        confidence: 'high',
        timestamp: new Date().toISOString(),
        metadata: { 
          rawScore: 8.6,
          sourceUrl: 'https://arcprize.org/leaderboard',
          dataFormat: 'percentage',
          benchmarkVersion: 'ARC-AGI-2',
          costPerTask: 1.93,
          note: 'Claude Opus 4 (Thinking 16K) score on ARC-AGI-2'
        }
      }
    ];
    
    console.log(`📊 Testing ${testScores.length} score updates...`);
    
    // Show what we're updating
    console.log('\nUpdates to be made:');
    testScores.forEach((score, i) => {
      console.log(`${i + 1}. ${score.modelName} (${score.category}): ${score.score} from ${score.source}`);
    });
    
    // Update scores
    const stats = await databaseUpdater.updateScores(testScores);
    
    console.log('\n✅ Update completed!');
    console.log(`📈 Statistics:`, stats);
    
    // Verify updates by checking one model
    if (stats.success > 0) {
      console.log('\n🔍 Verification check...');
      console.log('Run this SQL to verify the updates:');
      console.log(`
SELECT 
  canonicalName,
  JSON_EXTRACT(extendedMetadata, '$.categoryScores.general_chat.score') as general_chat_score,
  JSON_EXTRACT(extendedMetadata, '$.categoryScores.general_chat.source') as general_chat_source,
  JSON_EXTRACT(extendedMetadata, '$.categoryScores.reasoning.score') as reasoning_score,
  JSON_EXTRACT(extendedMetadata, '$.categoryScores.reasoning.source') as reasoning_source
FROM Models 
WHERE canonicalName IN ('openai/chatgpt-4o-latest', 'gemini/gemini-2.5-pro', 'xai/grok-4-0709', 'openai/o3-2025-04-16', 'anthropic/claude-opus-4-0')
ORDER BY canonicalName;
      `);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await databaseUpdater.close();
  }
}

// Run test
testDatabaseUpdate().catch(console.error);