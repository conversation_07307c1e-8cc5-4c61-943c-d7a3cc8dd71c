#!/usr/bin/env node

/**
 * Test Benchmark Collection using MCP Firecrawl
 * Tests real scraping of LMSYS Arena, ARC-AGI, and HuggingFace Open LLM Leaderboard
 */

const config = require('./config/benchmark-sources.json');
const ModelMapper = require('./processors/model-mapper');

// Collection results
const results = {
  'lmsys-arena': { success: false, charactersScraped: 0, scores: [], errors: [] },
  'arc-agi': { success: false, charactersScraped: 0, scores: [], errors: [] },
  'huggingface-open-llm': { success: false, charactersScraped: 0, scores: [], errors: [] }
};

async function testLMSYSArena() {
  console.log('\n📊 Testing LMSYS Chatbot Arena...');
  const source = config.sources['lmsys-arena'];
  console.log(`URL: ${source.url}`);
  
  try {
    // Note: This will be handled by <PERSON>'s MCP tools
    console.log('⏳ Scraping LMSYS Arena...');
    
    // Placeholder for MCP scraping results
    // In real implementation, this would be the scraped content
    const mockContent = `
    | Model | Arena Elo |
    |-------|-----------|
    | GPT-4o-latest | 1287 |
    | Claude-3.5-Sonnet | 1268 |
    | Gemini-2.0-Flash-Thinking-Exp-1219 | 1265 |
    | o1-preview | 1258 |
    | Gemini-1.5-Pro | 1251 |
    `;
    
    results['lmsys-arena'].charactersScraped = mockContent.length;
    
    // Extract scores (would be done from real scraped content)
    const scoreMatches = mockContent.matchAll(/\| ([^|]+) \| (\d+) \|/g);
    for (const match of scoreMatches) {
      const modelName = match[1].trim();
      const score = parseInt(match[2]);
      if (modelName && !isNaN(score)) {
        results['lmsys-arena'].scores.push({
          model: modelName,
          score: score,
          metric: 'elo'
        });
      }
    }
    
    results['lmsys-arena'].success = true;
    console.log(`✅ Scraped ${results['lmsys-arena'].charactersScraped} characters`);
    console.log(`📈 Extracted ${results['lmsys-arena'].scores.length} scores`);
    
  } catch (error) {
    results['lmsys-arena'].errors.push(error.message);
    console.error('❌ Error:', error.message);
  }
}

async function testARCAGI() {
  console.log('\n🧩 Testing ARC-AGI Leaderboard...');
  const source = config.sources['arc-agi'];
  console.log(`URL: ${source.url}`);
  
  try {
    console.log('⏳ Scraping ARC-AGI...');
    
    // Placeholder for MCP scraping results
    const mockContent = `
    | Rank | Model | Score | Cost |
    |------|-------|-------|------|
    | 1 | o3-mini (High) | 75.7% | $365 |
    | 2 | o3-mini (Low) | 63.2% | $17 |
    | 3 | Claude-3.5-Sonnet | 14% | $8.5 |
    | 4 | GPT-4o | 5% | $3.2 |
    `;
    
    results['arc-agi'].charactersScraped = mockContent.length;
    
    // Extract scores
    const scoreMatches = mockContent.matchAll(/\| \d+ \| ([^|]+) \| ([\d.]+)% \| \$[\d.]+ \|/g);
    for (const match of scoreMatches) {
      const modelName = match[1].trim();
      const score = parseFloat(match[2]);
      if (modelName && !isNaN(score)) {
        results['arc-agi'].scores.push({
          model: modelName,
          score: score,
          metric: 'percentage'
        });
      }
    }
    
    results['arc-agi'].success = true;
    console.log(`✅ Scraped ${results['arc-agi'].charactersScraped} characters`);
    console.log(`📈 Extracted ${results['arc-agi'].scores.length} scores`);
    
  } catch (error) {
    results['arc-agi'].errors.push(error.message);
    console.error('❌ Error:', error.message);
  }
}

async function testHuggingFaceOpenLLM() {
  console.log('\n🤗 Testing HuggingFace Open LLM Leaderboard...');
  const source = config.sources['huggingface-open-llm'];
  console.log(`URL: ${source.url}`);
  
  try {
    console.log('⏳ Scraping HuggingFace Open LLM...');
    
    // Placeholder for MCP scraping results
    const mockContent = `
    | Model | Average | ARC | HellaSwag | MMLU | TruthfulQA |
    |-------|---------|-----|-----------|------|------------|
    | Qwen/Qwen2.5-72B-Instruct | 85.5 | 96.3 | 86.5 | 83.1 | 76.2 |
    | meta-llama/Llama-3.1-70B-Instruct | 83.8 | 93.0 | 87.9 | 81.8 | 72.5 |
    | mistralai/Mixtral-8x22B-Instruct-v0.1 | 77.8 | 91.0 | 87.5 | 71.6 | 60.9 |
    | google/gemma-2-27b-it | 75.5 | 88.9 | 84.7 | 70.9 | 57.5 |
    `;
    
    results['huggingface-open-llm'].charactersScraped = mockContent.length;
    
    // Extract scores
    const scoreMatches = mockContent.matchAll(/\| ([^|]+) \| ([\d.]+) \| ([\d.]+) \| ([\d.]+) \| ([\d.]+) \| ([\d.]+) \|/g);
    for (const match of scoreMatches) {
      const modelName = match[1].trim();
      const avgScore = parseFloat(match[2]);
      if (modelName && !isNaN(avgScore)) {
        results['huggingface-open-llm'].scores.push({
          model: modelName,
          score: avgScore,
          metric: 'average',
          components: {
            ARC: parseFloat(match[3]),
            HellaSwag: parseFloat(match[4]),
            MMLU: parseFloat(match[5]),
            TruthfulQA: parseFloat(match[6])
          }
        });
      }
    }
    
    results['huggingface-open-llm'].success = true;
    console.log(`✅ Scraped ${results['huggingface-open-llm'].charactersScraped} characters`);
    console.log(`📈 Extracted ${results['huggingface-open-llm'].scores.length} scores`);
    
  } catch (error) {
    results['huggingface-open-llm'].errors.push(error.message);
    console.error('❌ Error:', error.message);
  }
}

async function mapModelsAndGenerateReport() {
  console.log('\n🔄 Mapping model names to database format...');
  
  const mapper = ModelMapper;
  await mapper.init();
  
  // Map all collected models
  let totalMapped = 0;
  let totalUnmapped = 0;
  
  for (const [source, data] of Object.entries(results)) {
    console.log(`\n📍 Mapping models from ${source}:`);
    
    for (const scoreData of data.scores) {
      const canonical = await mapper.mapModelName(scoreData.model);
      scoreData.canonicalName = canonical;
      
      if (canonical) {
        totalMapped++;
        console.log(`  ✅ "${scoreData.model}" → "${canonical}"`);
      } else {
        totalUnmapped++;
        console.log(`  ❌ "${scoreData.model}" → NOT MAPPED`);
      }
    }
  }
  
  await mapper.close();
  
  // Generate summary report
  console.log('\n' + '='.repeat(80));
  console.log('📊 BENCHMARK COLLECTION TEST SUMMARY');
  console.log('='.repeat(80));
  
  let totalScores = 0;
  let totalChars = 0;
  let successfulSources = 0;
  
  for (const [source, data] of Object.entries(results)) {
    const sourceConfig = config.sources[source];
    console.log(`\n📌 ${sourceConfig.name}:`);
    console.log(`   URL: ${sourceConfig.url}`);
    console.log(`   Status: ${data.success ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`   Characters scraped: ${data.charactersScraped.toLocaleString()}`);
    console.log(`   Scores extracted: ${data.scores.length}`);
    
    if (data.scores.length > 0) {
      console.log('   Sample scores:');
      data.scores.slice(0, 3).forEach(score => {
        console.log(`     - ${score.model}: ${score.score} (${score.metric})`);
        if (score.canonicalName) {
          console.log(`       Mapped to: ${score.canonicalName}`);
        }
      });
    }
    
    if (data.errors.length > 0) {
      console.log(`   Errors: ${data.errors.join(', ')}`);
    }
    
    totalScores += data.scores.length;
    totalChars += data.charactersScraped;
    if (data.success) successfulSources++;
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('📈 OVERALL STATISTICS:');
  console.log(`   Sources tested: ${Object.keys(results).length}`);
  console.log(`   Successful sources: ${successfulSources}`);
  console.log(`   Total characters scraped: ${totalChars.toLocaleString()}`);
  console.log(`   Total scores collected: ${totalScores}`);
  console.log(`   Successfully mapped models: ${totalMapped}`);
  console.log(`   Unmapped models: ${totalUnmapped}`);
  console.log(`   Mapping success rate: ${((totalMapped / (totalMapped + totalUnmapped)) * 100).toFixed(1)}%`);
  console.log('='.repeat(80));
}

// Main execution
async function main() {
  console.log('🚀 Starting Benchmark Collection Test with MCP Firecrawl');
  console.log('⚠️  Note: This test uses mock data to demonstrate the system.');
  console.log('    In production, MCP Firecrawl would provide real scraped content.\n');
  
  try {
    // Test each source
    await testLMSYSArena();
    await testARCAGI();
    await testHuggingFaceOpenLLM();
    
    // Map models and generate report
    await mapModelsAndGenerateReport();
    
    console.log('\n✅ Test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
main();