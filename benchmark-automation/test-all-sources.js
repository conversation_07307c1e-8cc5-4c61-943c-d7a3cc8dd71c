#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Benchmark Automation
 * Tests all 25+ sources individually with real MCP servers
 */

const benchmarkSources = require('./config/benchmark-sources.json');
const modelMapper = require('./utils/model-mapper');
const dataNormalizer = require('./processors/data-normalizer');
const databaseUpdater = require('./utils/database-updater');
const fs = require('fs').promises;
const path = require('path');

// Test results storage
const testResults = {
  timestamp: new Date().toISOString(),
  date: 'July 17, 2025',
  environment: 'Claude Code with MCP Servers',
  sources: {},
  summary: {
    total: 0,
    success: 0,
    failed: 0,
    scores: 0,
    models: new Set()
  }
};

/**
 * Test a single benchmark source
 */
async function testSource(sourceName, sourceConfig) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 Testing: ${sourceName}`);
  console.log(`URL: ${sourceConfig.url}`);
  console.log(`${'='.repeat(60)}`);
  
  const result = {
    name: sourceName,
    url: sourceConfig.url,
    status: 'pending',
    scores: 0,
    models: [],
    error: null,
    timestamp: new Date().toISOString()
  };
  
  try {
    // Step 1: Scrape the source using real MCP Firecrawl
    console.log(`\n1️⃣ Scraping with Firecrawl (waitFor: ${sourceConfig.scrapeOptions?.waitFor || 3000}ms)...`);
    
    const scrapeResult = await mcp__firecrawl__firecrawl_scrape({
      url: sourceConfig.url,
      formats: ['markdown'],
      onlyMainContent: true,
      waitFor: sourceConfig.scrapeOptions?.waitFor || 3000,
      timeout: 30000,
      removeBase64Images: true
    });
    
    if (!scrapeResult.markdown) {
      throw new Error('No content retrieved from Firecrawl');
    }
    
    console.log(`✅ Scraped ${scrapeResult.markdown.length} characters`);
    
    // Save raw markdown for debugging
    const debugDir = path.join(__dirname, 'test-results', 'raw-scrapes');
    await fs.mkdir(debugDir, { recursive: true });
    await fs.writeFile(
      path.join(debugDir, `${sourceName.replace(/\s+/g, '-')}.md`),
      scrapeResult.markdown
    );
    
    // Step 2: Extract scores using the data normalizer
    console.log(`\n2️⃣ Extracting scores...`);
    const extractionResult = await dataNormalizer.extractScores(
      sourceName,
      sourceConfig,
      scrapeResult
    );
    
    if (!extractionResult || extractionResult.length === 0) {
      throw new Error('No scores extracted from content');
    }
    
    console.log(`✅ Extracted ${extractionResult.length} raw scores`);
    
    // Step 3: Normalize scores
    console.log(`\n3️⃣ Normalizing scores...`);
    const normalizedScores = await dataNormalizer.normalizeScores(extractionResult);
    
    console.log(`✅ Normalized ${normalizedScores.length} scores`);
    
    // Step 4: Map model names
    console.log(`\n4️⃣ Mapping model names...`);
    let mappedCount = 0;
    const unmappedModels = [];
    
    for (const score of normalizedScores) {
      const mappedName = modelMapper.mapModelName(score.modelName);
      if (mappedName) {
        score.originalName = score.modelName;
        score.modelName = mappedName;
        mappedCount++;
        result.models.push({
          original: score.originalName,
          mapped: mappedName,
          category: score.category,
          score: score.score
        });
      } else {
        unmappedModels.push(score.modelName);
      }
    }
    
    console.log(`✅ Mapped ${mappedCount}/${normalizedScores.length} models`);
    if (unmappedModels.length > 0) {
      console.log(`⚠️ Unmapped models: ${unmappedModels.join(', ')}`);
    }
    
    // Update result
    result.status = 'success';
    result.scores = mappedCount;
    result.unmappedModels = unmappedModels;
    
    // Update summary
    testResults.summary.scores += mappedCount;
    result.models.forEach(m => testResults.summary.models.add(m.mapped));
    
  } catch (error) {
    result.status = 'failed';
    result.error = error.message;
    console.error(`\n❌ Test failed: ${error.message}`);
  }
  
  testResults.sources[sourceName] = result;
  return result;
}

/**
 * Test all sources
 */
async function testAllSources() {
  console.log('🚀 Starting Comprehensive Benchmark Source Testing');
  console.log(`📅 Date: July 17, 2025`);
  console.log(`🔧 Mode: Real MCP Servers (Firecrawl + Perplexity)`);
  console.log(`📊 Sources: ${Object.keys(benchmarkSources).length}`);
  
  // Initialize components
  await modelMapper.init();
  await dataNormalizer.init();
  await databaseUpdater.init();
  
  // Test each source
  for (const [sourceName, sourceConfig] of Object.entries(benchmarkSources)) {
    testResults.summary.total++;
    
    const result = await testSource(sourceName, sourceConfig);
    
    if (result.status === 'success') {
      testResults.summary.success++;
    } else {
      testResults.summary.failed++;
    }
    
    // Add delay between sources to respect rate limits
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  // Generate report
  await generateTestReport();
  
  // Close connections
  await databaseUpdater.close();
}

/**
 * Generate comprehensive test report
 */
async function generateTestReport() {
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 TEST SUMMARY');
  console.log(`${'='.repeat(60)}`);
  
  console.log(`\nTotal Sources: ${testResults.summary.total}`);
  console.log(`✅ Success: ${testResults.summary.success}`);
  console.log(`❌ Failed: ${testResults.summary.failed}`);
  console.log(`📈 Total Scores: ${testResults.summary.scores}`);
  console.log(`🤖 Unique Models: ${testResults.summary.models.size}`);
  
  // Success rate by tier
  const tierStats = { tier1: { total: 0, success: 0 }, tier2: { total: 0, success: 0 }, tier3: { total: 0, success: 0 } };
  
  for (const [name, result] of Object.entries(testResults.sources)) {
    const tier = benchmarkSources[name].priority;
    tierStats[tier].total++;
    if (result.status === 'success') {
      tierStats[tier].success++;
    }
  }
  
  console.log('\nSuccess Rate by Tier:');
  for (const [tier, stats] of Object.entries(tierStats)) {
    const rate = stats.total > 0 ? ((stats.success / stats.total) * 100).toFixed(1) : 0;
    console.log(`  ${tier}: ${stats.success}/${stats.total} (${rate}%)`);
  }
  
  // Detailed results
  console.log('\nDetailed Results:');
  console.log('Source Name                          | Status  | Scores | Error');
  console.log('-'.repeat(80));
  
  for (const [name, result] of Object.entries(testResults.sources)) {
    const status = result.status === 'success' ? '✅' : '❌';
    const error = result.error ? result.error.substring(0, 30) + '...' : '-';
    console.log(`${name.padEnd(35)} | ${status}      | ${String(result.scores).padEnd(6)} | ${error}`);
  }
  
  // Save full report
  const reportPath = path.join(__dirname, 'test-results', `full-test-${new Date().toISOString().split('T')[0]}.json`);
  await fs.mkdir(path.dirname(reportPath), { recursive: true });
  await fs.writeFile(reportPath, JSON.stringify(testResults, null, 2));
  
  console.log(`\n📄 Full report saved to: ${reportPath}`);
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (testResults.summary.failed > 0) {
    console.log('1. Failed sources need investigation:');
    for (const [name, result] of Object.entries(testResults.sources)) {
      if (result.status === 'failed') {
        console.log(`   - ${name}: ${result.error}`);
      }
    }
  }
  
  if (testResults.summary.scores < 100) {
    console.log('2. Low score extraction rate. Consider:');
    console.log('   - Increasing waitFor times for dynamic content');
    console.log('   - Updating extraction patterns');
    console.log('   - Adding source-specific parsers');
  }
  
  const unmappedCount = Object.values(testResults.sources)
    .filter(r => r.unmappedModels && r.unmappedModels.length > 0)
    .length;
  
  if (unmappedCount > 0) {
    console.log('3. Model mapping needs updates for:');
    const allUnmapped = new Set();
    for (const result of Object.values(testResults.sources)) {
      if (result.unmappedModels) {
        result.unmappedModels.forEach(m => allUnmapped.add(m));
      }
    }
    console.log(`   - ${allUnmapped.size} unique model names need mapping`);
  }
  
  // Next steps
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Review failed sources and update extraction patterns');
  console.log('2. Update model mappings for unmapped models');
  console.log('3. Implement hourly collection schedule');
  console.log('4. Run database update test with extracted scores');
  console.log('5. Monitor success rates over time');
}

// Run tests if called directly
if (require.main === module) {
  testAllSources().catch(console.error);
}