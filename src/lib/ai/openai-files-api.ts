/**
 * OpenAI Files API Integration
 * Handles native PDF and document processing using OpenAI's Files API
 */

import OpenAI from 'openai';
import { apiLogger } from '@/lib/logger';

export interface FileUploadResult {
  fileId: string;
  filename: string;
  purpose: string;
  status: string;
  bytes: number;
}

export interface FileProcessingResult {
  success: boolean;
  fileId?: string;
  content?: string;
  error?: string;
}

/**
 * OpenAI Files API client
 */
class OpenAIFilesAPI {
  private client: OpenAI;

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Upload a file to OpenAI's Files API
   */
  async uploadFile(file: File, purpose: 'assistants' | 'fine-tune' | 'batch' = 'assistants'): Promise<FileUploadResult> {
    try {
      apiLogger.info(`[OpenAI Files] Uploading file: ${file.name} (${file.size} bytes)`);
      
      // Convert File to the format expected by OpenAI SDK
      const fileBuffer = await file.arrayBuffer();
      const fileBlob = new Blob([fileBuffer], { type: file.type });
      
      // Create a File-like object that OpenAI SDK expects
      const openaiFile = new File([fileBlob], file.name, { type: file.type });
      
      const uploadedFile = await this.client.files.create({
        file: openaiFile,
        purpose: purpose,
      });

      apiLogger.info(`[OpenAI Files] File uploaded successfully: ${uploadedFile.id}`);
      
      return {
        fileId: uploadedFile.id,
        filename: uploadedFile.filename,
        purpose: uploadedFile.purpose,
        status: uploadedFile.status,
        bytes: uploadedFile.bytes,
      };
    } catch (error) {
      apiLogger.error(`[OpenAI Files] Error uploading file ${file.name}:`, error);
      throw error;
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(fileId: string) {
    try {
      const file = await this.client.files.retrieve(fileId);
      return file;
    } catch (error) {
      apiLogger.error(`[OpenAI Files] Error retrieving file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a file from OpenAI
   */
  async deleteFile(fileId: string) {
    try {
      await this.client.files.delete(fileId);
      apiLogger.info(`[OpenAI Files] File deleted: ${fileId}`);
    } catch (error) {
      apiLogger.error(`[OpenAI Files] Error deleting file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Process a PDF file using OpenAI's native capabilities
   * This uses the Assistants API to process the uploaded file
   */
  async processPDFWithAssistant(fileId: string, query: string = "Please extract and summarize the content of this document."): Promise<string> {
    try {
      apiLogger.info(`[OpenAI Files] Processing PDF with assistant: ${fileId}`);
      
      // Create a temporary assistant for document processing
      const assistant = await this.client.beta.assistants.create({
        name: "Document Processor",
        instructions: "You are a helpful assistant that can read and analyze documents. Extract and summarize the content clearly and accurately.",
        model: "gpt-4o", // Use a model that supports file processing
        tools: [{ type: "file_search" }],
      });

      // Create a thread
      const thread = await this.client.beta.threads.create({
        messages: [
          {
            role: "user",
            content: query,
            attachments: [
              {
                file_id: fileId,
                tools: [{ type: "file_search" }],
              },
            ],
          },
        ],
      });

      // Run the assistant
      const run = await this.client.beta.threads.runs.create(thread.id, {
        assistant_id: assistant.id,
      });

      // Wait for completion
      let runStatus = await this.client.beta.threads.runs.retrieve(thread.id, run.id);

      while (runStatus.status === 'queued' || runStatus.status === 'in_progress') {
        await new Promise(resolve => setTimeout(resolve, 1000));
        runStatus = await this.client.beta.threads.runs.retrieve(thread.id, run.id);
      }

      if (runStatus.status === 'completed') {
        // Get the messages
        const messages = await this.client.beta.threads.messages.list(thread.id);
        const assistantMessage = messages.data.find(msg => msg.role === 'assistant');
        
        if (assistantMessage && assistantMessage.content[0].type === 'text') {
          const content = assistantMessage.content[0].text.value;
          
          // Clean up
          await this.client.beta.assistants.delete(assistant.id);
          
          apiLogger.info(`[OpenAI Files] PDF processed successfully: ${fileId}`);
          return content;
        }
      }

      // Clean up on failure
      await this.client.beta.assistants.delete(assistant.id);
      throw new Error(`Assistant run failed with status: ${runStatus.status}`);
      
    } catch (error) {
      apiLogger.error(`[OpenAI Files] Error processing PDF ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Process a document file using OpenAI's Files API
   */
  async processDocument(file: File): Promise<FileProcessingResult> {
    try {
      // Upload the file
      const uploadResult = await this.uploadFile(file);
      
      // Wait a moment for the file to be processed
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Process with assistant
      const content = await this.processPDFWithAssistant(
        uploadResult.fileId,
        `Please extract all text content from this ${file.type} document. Provide a clear, well-formatted summary of the content.`
      );
      
      // Clean up the uploaded file
      await this.deleteFile(uploadResult.fileId);
      
      return {
        success: true,
        fileId: uploadResult.fileId,
        content: content,
      };
      
    } catch (error) {
      apiLogger.error(`[OpenAI Files] Error processing document ${file.name}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

// Export singleton instance
export const openaiFilesAPI = new OpenAIFilesAPI();

/**
 * Helper function to check if a file type is supported by OpenAI Files API
 */
export function isOpenAIFilesSupported(fileType: string): boolean {
  const supportedTypes = [
    'application/pdf',
    'text/plain',
    'text/markdown',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];
  
  return supportedTypes.includes(fileType);
}

/**
 * Helper function to determine if we should use Files API for a given model and file
 */
export function shouldUseFilesAPI(modelId: string, fileType: string): boolean {
  // Only use Files API for OpenAI models and supported file types
  const isOpenAIModel = modelId.includes('gpt-4') || modelId.includes('gpt-3.5');
  const isSupported = isOpenAIFilesSupported(fileType);
  
  return isOpenAIModel && isSupported && !!process.env.OPENAI_API_KEY;
}
