/**
 * ULTRA-FAST 28-CATEGORY ROUTER
 * 
 * Achieves sub-100ms total routing time:
 * - User data: ~0.1ms (cached)
 * - Prompt cache check: ~0.2ms
 * - Category analysis: ~50ms (Gemini Flash Lite - only bottleneck)
 * - Model selection: ~0.3ms (cached)
 * - Total: ~51ms excluding LLM response
 * 
 * Works with existing Models table categoryScores in extendedMetadata
 */

import {
  getCategoryModels,
  getBestModelForCategory,
  getUserAvailableCategories,
  cachePromptAnalysis,
  getCachedPromptAnalysis,
  warmUpRouterCache,
  getCategoryStats,
  getModelDetails
} from './optimized-redis-cache';
import { manualSelectionManager } from './manual-selections';
import {
  getCachedUserProfile,
  getCachedUserPlan,
  checkMessageLimit,
  checkRateLimit,
  warmUserCache,
  getUserLastModel,
  cacheUserLastModel,
  incrementMessageCount
} from './user-cache';
import { PromptAnalysis, RouterInput, RouterDecision, UserPlan, Model } from '@/types';
import { PrismaClient, Prisma } from '@prisma/client';
import { redis } from '@/lib/redis';
import crypto from 'crypto';
import { performance } from 'perf_hooks';
import { apiLogger } from '@/lib/logger';
import { calculateTaskScores } from './category-aggregation';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { getConfig } from '@/lib/config';

export class IntelligentRouter {
  protected prisma: PrismaClient;
  private readonly ANALYSIS_MODEL = 'gemini-2.5-flash-lite';
  private googleClient: GoogleGenerativeAI | null = null;
  
  private cacheStats = {
    promptCacheHits: 0,
    promptCacheMisses: 0,
    modelCacheHits: 0,
    modelCacheMisses: 0,
    totalRequests: 0
  };
  
  constructor() {
    this.prisma = new PrismaClient();
    
    // Initialize Google Generative AI client
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      apiLogger.warn('⚠️ Google API key not found for Ultra-Fast Router');
    } else {
      this.googleClient = new GoogleGenerativeAI(apiKey);
      apiLogger.info('✅ Google Generative AI client initialized for Ultra-Fast Router');
    }
    
    // Start cache warmup in background
    this.warmupInBackground();
  }
  
  private async warmupInBackground(): Promise<void> {
    // Don't block initialization
    setTimeout(async () => {
      try {
        await Promise.all([
          warmUpRouterCache(this.prisma),
          // Warm up user cache for common test users if needed
        ]);
      } catch (error) {
        console.error('Cache warmup error:', error);
      }
    }, 1000);
  }
  
  /**
   * ULTRA-OPTIMIZED ROUTE METHOD
   * Every millisecond counts!
   */
  async route(input: RouterInput): Promise<RouterDecision> {
    const startTime = performance.now();
    const requestId = crypto.randomUUID();
    this.cacheStats.totalRequests++;
    
    try {
      // PARALLEL STEP 1: Get user data AND check prompt cache simultaneously
      const [userDataResult, cachedAnalysis] = await Promise.all([
        this.getUserDataFast(input),
        this.checkPromptCache(input.query)
      ]);
      
      const { userPlan, canProceed, messageData, rateLimit } = userDataResult;
      
      // Quick validation
      if (!canProceed) {
        if (rateLimit && !rateLimit.allowed) {
          throw new Error(`Rate limit exceeded. Try again at ${rateLimit.resetsAt.toISOString()}`);
        }
        if (messageData && messageData.remaining <= 0) {
          throw new Error(`Daily message limit reached. Resets at ${messageData.resetsAt.toISOString()}`);
        }
      }
      
      // STEP 2: Use cached analysis or generate new one
      let analysis: PromptAnalysis;
      let analysisTime = 0;
      
      // Check if we have a forced complexity (e.g., from Ultra Think button)
      if ((input as any).forceComplexity) {
        // Skip LLM analysis and use forced complexity
        analysis = {
          primary_category: 'general_chat' as any, // Will be overridden by actual query analysis if needed
          secondary_categories: [],
          complexity: (input as any).forceComplexity,
          confidence: 1.0,
          reasoning: `Forced complexity: ${(input as any).forceComplexity} (Ultra Think mode)`,
          specific_attributes: {},
          requirements: {
            needs_web_search: input.webSearchEnabled || false,
            needs_vision: false,
            needs_large_context: false,
            needs_reasoning: true,
            needs_citations: false,
            expected_output_tokens: 2000, // Higher for ultra mode
            latency_sensitivity: 'low', // Ultra mode prioritizes quality
            needs_structured_output: false,
            needs_function_calling: false,
            max_cost_tolerance: 'high' // Higher tolerance for ultra mode
          }
        };
        
        // Still need to determine the category
        const categoryAnalysis = await this.analyzePrompt(input);
        analysis.primary_category = categoryAnalysis.primary_category;
        analysisTime = 0; // Mark as forced
      } else if (cachedAnalysis) {
        this.cacheStats.promptCacheHits++;
        analysis = cachedAnalysis;
      } else {
        this.cacheStats.promptCacheMisses++;
        const analysisStart = performance.now();
        
        // This is the ONLY real bottleneck - the LLM categorization
        analysis = await this.analyzePrompt(input);
        
        analysisTime = performance.now() - analysisStart;
        
        // Cache it immediately (don't await)
        cachePromptAnalysis(input.query, analysis).catch(() => {});
      }
      
      // STEP 3A: Check manual selection first (admin-configured)
      const selectionStart = performance.now();
      let bestModel = null;
      let selectionMethod = 'automatic';
      
      // Check for manual selection
      const manualSelectionResult = await manualSelectionManager.getManualSelection(
        analysis.primary_category,
        analysis.complexity,
        userPlan
      );
      
      if (manualSelectionResult.found && manualSelectionResult.models.length > 0) {
        // Get sticky model selection for this conversation
        const stickyModel = await manualSelectionManager.getStickyModelSelection(
          input.conversationId,
          input.userId || 'anonymous',
          analysis.primary_category,
          analysis.complexity,
          manualSelectionResult.models
        );
        
        if (stickyModel) {
          // Use sticky selection - convert to router format
          bestModel = {
            id: stickyModel.id,
            canonicalName: stickyModel.canonicalName,
            displayName: stickyModel.displayName,
            categoryScore: stickyModel.categoryScore,
            speedRating: stickyModel.speedRating,
            qualityRating: stickyModel.qualityRating,
            inputCost: stickyModel.inputCostPer1M,
            outputCost: stickyModel.outputCostPer1M,
            contextWindow: stickyModel.contextWindow,
            maxOutput: stickyModel.maxOutput,
            isEnabled: stickyModel.isEnabled,
            provider: stickyModel.provider
          };
          
          selectionMethod = `manual_${manualSelectionResult.source}`;
          
          // Log manual selection
          if (process.env.NODE_ENV === 'development') {
            console.log(`🎯 Manual selection (sticky): ${analysis.primary_category} (${analysis.complexity}) → ${bestModel.displayName} (${manualSelectionResult.source}, ${manualSelectionResult.models.length} available)`);
          }
        } else {
          // This shouldn't happen but fall back to automatic selection
          apiLogger.warn('Manual selection found but sticky selection failed', {
            category: analysis.primary_category,
            complexity: analysis.complexity,
            modelsFound: manualSelectionResult.models.length
          });
        }
      }
      
      if (!bestModel) {
        // STEP 3B: Fall back to automatic selection
        bestModel = await getBestModelForCategory(
          analysis.primary_category,
          analysis.complexity,
          userPlan,
          this.prisma,
          {
            conversationId: input.conversationId,
            ultraMode: (input as any).ultraMode || false,  // Check if ultra mode flag is passed
            enableVariety: true,  // Always enable variety for better UX
            forceNewSelection: false  // Allow session stickiness
          }
        );
        selectionMethod = 'automatic';
      }
      
      const selectionTime = performance.now() - selectionStart;
      
      if (!bestModel) {
        // Fallback if no model found
        return await this.getFallbackSelection(analysis, input);
      }
      
      // STEP 4: Build decision (get alternatives in parallel if needed)
      const totalTime = performance.now() - startTime;
      
      const decision: RouterDecision = {
        modelId: bestModel.id,
        model: bestModel,
        provider: bestModel.canonicalName.split('/')[0],
        selectedModel: bestModel.canonicalName,
        reason: `Ultra-fast routing: ${analysis.primary_category} (${analysis.complexity}) → ${bestModel.displayName} (score: ${bestModel.categoryScore}, method: ${selectionMethod})`,
        reasoning: `Ultra-fast routing: ${analysis.primary_category} (${analysis.complexity}) → ${bestModel.displayName} (score: ${bestModel.categoryScore}, method: ${selectionMethod})`,
        confidence: analysis.confidence,
        category: analysis.primary_category,
        webSearchQueries: analysis.requirements?.search_queries || [],
        metadata: {
          intelligentRouter: true,
          category: analysis.primary_category,
          promptAnalysis: {
            ...analysis,
            metadata: {
              complexity: analysis.complexity,
              confidence: analysis.confidence
            }
          }
        },
        performanceMetrics: {
          routingTime: totalTime,
          modelSelectionTime: selectionTime,
          cacheHitRate: cachedAnalysis ? 1 : 0
        }
      };
      
      // ASYNC STEP 5: Update user state (don't block response)
      if (input.userId) {
        Promise.all([
          incrementMessageCount(input.userId),
          cacheUserLastModel(input.userId, bestModel.canonicalName)
        ]).catch(() => {});
      }
      
      // Log performance in dev
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 Router: ${totalTime.toFixed(1)}ms total (analysis: ${cachedAnalysis ? 'CACHED' : analysisTime.toFixed(1) + 'ms'}, selection: ${selectionTime.toFixed(1)}ms via ${selectionMethod})`);
      }
      
      return decision;
      
    } catch (error) {
      console.error('[IntelligentRouter] Error:', error);
      this.cacheStats.totalRequests--; // Don't count errors
      return await this.getFallbackSelection(
        this.getFallbackAnalysis(input),
        input
      );
    }
  }
  
  /**
   * Get user data with timing
   */
  private async getUserDataFast(input: RouterInput): Promise<{
    userPlan: UserPlan;
    canProceed: boolean;
    messageData: any;
    rateLimit: any;
    timing: number;
  }> {
    const start = performance.now();
    
    if (!input.userId) {
      return {
        userPlan: (input.userPlan as UserPlan) || 'FREE',
        canProceed: true,
        messageData: null,
        rateLimit: null,
        timing: performance.now() - start
      };
    }
    
    // Get all user data in parallel
    const [userPlan, messageData, rateLimit] = await Promise.all([
      getCachedUserPlan(input.userId, this.prisma),
      checkMessageLimit(input.userId, input.userPlan as UserPlan || 'FREE'),
      checkRateLimit(input.userId)
    ]);
    
    return {
      userPlan,
      canProceed: rateLimit.allowed && messageData.remaining > 0,
      messageData,
      rateLimit,
      timing: performance.now() - start
    };
  }
  
  /**
   * Check prompt cache
   */
  private async checkPromptCache(query: string): Promise<PromptAnalysis | null> {
    try {
      return await getCachedPromptAnalysis(query);
    } catch (error) {
      // Don't let cache errors break routing
      return null;
    }
  }
  
  /**
   * Get cache hit rate
   */
  private getCacheHitRate(): number {
    const total = this.cacheStats.promptCacheHits + this.cacheStats.promptCacheMisses;
    if (total === 0) return 0;
    return (this.cacheStats.promptCacheHits / total) * 100;
  }
  
  /**
   * Get performance statistics
   */
  async getPerformanceStats(): Promise<any> {
    const categories = await getUserAvailableCategories(UserPlan.MAX, this.prisma);
    
    return {
      cacheStats: {
        ...this.cacheStats,
        hitRate: `${this.getCacheHitRate().toFixed(1)}%`
      },
      categoryCount: categories.length,
      topCategories: categories
        .sort((a, b) => b.modelCount - a.modelCount)
        .slice(0, 5)
        .map(c => ({
          category: c.category,
          models: c.modelCount,
          avgScore: c.avgScore
        }))
    };
  }
  
  
  /**
   * Analyze prompt to determine category and complexity (~50ms)
   * Uses configurable model (currently Gemini Flash Lite for speed)
   */
  private async analyzePrompt(input: RouterInput): Promise<PromptAnalysis> {
    try {
      // Quick heuristic for very simple queries
      const queryLower = input.query.toLowerCase().trim();
      const simpleGreetings = ['hi', 'hello', 'hey', 'yo', 'sup', 'howdy', 'greetings'];
      const simpleQueries = ['how are you', "what's up", 'how do you do', 'good morning', 'good afternoon', 'good evening'];
      
      if (simpleGreetings.includes(queryLower) || simpleQueries.includes(queryLower)) {
        return {
          primary_category: 'general_chat' as any,
          secondary_categories: [],
          complexity: 'simple' as any,
          confidence: 0.95,
          reasoning: 'Simple greeting detected via heuristic',
          specific_attributes: {},
          requirements: {
            needs_web_search: false,
            needs_vision: false,
            needs_large_context: false,
            needs_reasoning: false,
            needs_citations: false,
            expected_output_tokens: 100,
            latency_sensitivity: 'high',
            needs_structured_output: false,
            needs_function_calling: false,
            max_cost_tolerance: 'low'
          }
        };
      }
      
      if (!this.googleClient) {
        throw new Error('Google client not initialized');
      }
      
      const prompt = `Analyze this query and respond with JSON:
Query: "${input.query}"
Web Search Enabled: ${input.webSearchEnabled || false}

Complexity guidelines:
- simple: Basic greetings (hi, hello, yo), simple arithmetic, yes/no questions, basic facts
- standard: General questions, explanations, typical conversations, moderate tasks
- complex: Complex analysis, detailed explanations, multi-step problems
- difficult: Advanced reasoning, research, comprehensive analysis
- expert: Specialized domain knowledge, cutting-edge research, highly technical expert-level tasks

Web Search Detection:
Only set needs_web_search: true if the query actually requires real-time, current information that changes frequently, such as:
- Current events, news, headlines ("latest news", "breaking news")
- Weather ("today's weather", "current weather") 
- Stock prices, market data ("stock price", "market today")
- Sports scores, live events ("game score", "match result")
- Time-sensitive information ("current time", "now", "today")
- Recent developments ("recent", "latest", "just happened")

Do NOT enable web search for:
- General knowledge questions that don't change ("what is Python", "how to bake a cake")
- Programming tutorials, documentation, guides
- Mathematical calculations, coding problems
- Historical facts, established science
- Personal advice, creative writing, analysis

When needs_web_search is true, provide 3-5 specific search queries.

Search Query Examples:
- "What is Python?" → ["what is Python programming", "Python programming language", "Python tutorial"]
- "How to bake a cake?" → ["cake baking recipe", "how to bake cake", "cake baking instructions"]
- "Latest news today" → ["latest news today", "current news headlines", "breaking news"]

Return JSON with:
{
  "primary_category": "<one of: coding, debugging, creative_writing, business_writing, technical_writing, math, data_analysis, reasoning, analysis, question_answering, general_chat, tutorial, translation, summarization, brainstorming, role_play, current_events, web_search, historical, scientific, philosophical, medical, legal>",
  "complexity": "<one of: simple, standard, complex, difficult, expert>",
  "confidence": <0.0-1.0>,
  "reasoning": "<brief explanation>",
  "needs_web_search": <true/false>,
  "search_queries": ["array", "of", "search", "terms"] // Always include if needs_web_search is true
}`;
      
      // Use the Google Generative AI SDK directly
      const model = this.googleClient.getGenerativeModel({ 
        model: this.ANALYSIS_MODEL,
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 150,
          responseMimeType: 'application/json'
        }
      });
      
      const result = await model.generateContent(prompt);
      const response = result.response;
      const text = response.text();
      
      const analysis = JSON.parse(text);
      
      // Add default requirements
      return {
        ...analysis,
        secondary_categories: [],
        specific_attributes: {},
        requirements: {
          needs_web_search: analysis.needs_web_search || input.webSearchEnabled || false,
          search_queries: analysis.search_queries || [],
          needs_vision: false,
          needs_large_context: false,
          needs_reasoning: analysis.complexity === 'complex',
          needs_citations: false,
          expected_output_tokens: 500,
          latency_sensitivity: 'medium',
          needs_structured_output: false,
          needs_function_calling: false,
          max_cost_tolerance: 'medium'
        }
      };
    } catch (error) {
      apiLogger.error('Router prompt analysis error:', error);
      return this.getFallbackAnalysis(input);
    }
  }
  
  /**
   * Get fallback analysis when Gemini fails
   */
  protected getFallbackAnalysis(input: RouterInput): PromptAnalysis {
    // Simple heuristic-based categorization
    const query = input.query.toLowerCase();
    
    let category = 'general_chat';
    let complexity = 'standard';
    
    if (query.includes('code') || query.includes('function') || query.includes('implement')) {
      category = 'coding';
    } else if (query.includes('debug') || query.includes('error') || query.includes('fix')) {
      category = 'debugging';
    } else if (query.includes('write') || query.includes('story') || query.includes('create')) {
      category = 'creative_writing';
    } else if (query.includes('math') || /\d+\s*[\+\-\*\/]\s*\d+/.test(query)) {
      category = 'math';
    } else if (query.includes('analyze') || query.includes('data')) {
      category = 'data_analysis';
    }
    
    // Check for web search needs - FIXED to handle webSearchEnabled flag
    const webSearchKeywords = ['today', 'current', 'latest', 'now', 'happening', 'news', 'price', 'weather', 'stock market', 'recent'];
    const hasWebSearchKeywords = webSearchKeywords.some(keyword => query.includes(keyword)) || category === 'current_events';
    const needsWebSearch = input.webSearchEnabled || hasWebSearchKeywords;
    
    // Generate search queries if needed
    let searchQueries: string[] = [];
    if (needsWebSearch) {
      // Always generate search queries when web search is needed
      searchQueries = [input.query]; // Start with the original query
      
      // Add specific queries based on content
      if (query.includes('news')) {
        searchQueries.push('latest news today');
      }
      if (query.includes('weather')) {
        searchQueries.push('current weather forecast');
      }
      if (query.includes('stock') || query.includes('market')) {
        searchQueries.push('stock market today', 'market news');
      }
      
      // For general queries when webSearchEnabled=true, create better search terms
      if (input.webSearchEnabled && searchQueries.length === 1) {
        // Generate additional relevant search queries
        const words = input.query.toLowerCase().split(' ').filter(w => w.length > 3);
        if (words.length > 0) {
          searchQueries.push(`${words.join(' ')} information`);
          if (words.length > 1) {
            searchQueries.push(`${words.slice(0, 2).join(' ')} guide`);
          }
        }
      }
    }
    
    return {
      primary_category: category as any,
      secondary_categories: [],
      complexity: complexity as any,
      confidence: 0.7,
      reasoning: 'Fallback heuristic categorization',
      specific_attributes: {},
      requirements: {
        needs_web_search: needsWebSearch || input.webSearchEnabled || false,
        search_queries: searchQueries,
        needs_vision: false,
        needs_large_context: false,
        needs_reasoning: false,
        needs_citations: false,
        expected_output_tokens: 500,
        latency_sensitivity: 'medium',
        needs_structured_output: false,
        needs_function_calling: false,
        max_cost_tolerance: 'low'
      }
    };
  }
  
  /**
   * Get fallback selection when no models found
   */
  protected async getFallbackSelection(analysis: PromptAnalysis, input: RouterInput): Promise<RouterDecision> {
    // Get any available model for the user's plan FROM DATABASE ONLY
    // We need to join with ModelPlanRules to check plan availability
    const models = await this.prisma.$queryRaw<any[]>`
      SELECT DISTINCT
        m.id,
        m.canonicalName,
        m.displayName,
        m.inputCostPer1M,
        m.outputCostPer1M,
        m.contextWindow,
        m.supportsVision,
        m.supportsFunctionCalling,
        m.supportsWebSearch,
        m.supportsReasoning,
        m.supportsStreaming,
        CAST(JSON_UNQUOTE(JSON_EXTRACT(m.extendedMetadata, '$.categoryScores.${analysis.primary_category}.score')) AS DECIMAL(5,2)) as categoryScore
      FROM Models m
      INNER JOIN ModelPlanRules mpr ON mpr.modelId = m.id
      WHERE m.isEnabled = 1
        AND mpr.isEnabled = 1
        AND mpr.ruleType = 'INCLUDE'
        AND (
          mpr.planIds IS NULL 
          OR JSON_CONTAINS(mpr.planIds, JSON_QUOTE(UPPER('${input.userPlan}')))
        )
        AND JSON_EXTRACT(m.extendedMetadata, '$.categoryScores.${analysis.primary_category}.score') IS NOT NULL
        AND CAST(JSON_UNQUOTE(JSON_EXTRACT(m.extendedMetadata, '$.categoryScores.${analysis.primary_category}.score')) AS DECIMAL(5,2)) > 0
      ORDER BY 
        categoryScore DESC,
        m.inputCostPer1M ASC
      LIMIT 1
    `.then(res => res.map((row: any) => ({
        id: row.id,
        canonicalName: row.canonicalName,
        displayName: row.displayName,
        inputCostPer1M: Number(row.inputCostPer1M),
        outputCostPer1M: Number(row.outputCostPer1M),
        contextWindow: row.contextWindow,
        supportsVision: Boolean(row.supportsVision),
        supportsFunctionCalling: Boolean(row.supportsFunctionCalling),
        supportsWebSearch: Boolean(row.supportsWebSearch),
        supportsReasoning: Boolean(row.supportsReasoning),
        supportsStreaming: Boolean(row.supportsStreaming)
      })));
    
    if (!models || models.length === 0) {
      // No models available at all - this should never happen in production
      throw new Error(`No models available for user plan ${input.userPlan}. Database may be empty or all models disabled.`);
    }
    
    const fallbackModel = models[0];
    
    return {
      modelId: fallbackModel.id,
      model: fallbackModel,
      provider: fallbackModel.canonicalName.split('/')[0],
      selectedModel: fallbackModel.canonicalName,
      reason: 'Fallback selection - no category-specific models available',
      reasoning: 'Fallback selection - no category-specific models available',
      confidence: 0.5,
      webSearchQueries: analysis.requirements?.search_queries || [],
      metadata: {
        intelligentRouter: true,
        category: analysis.primary_category,
        promptAnalysis: analysis
      },
      performanceMetrics: {
        routingTime: 0,
        modelSelectionTime: 0,
        cacheHitRate: 0
      }
    };
  }
}

// Export singleton instance
export const intelligentRouter = new IntelligentRouter();

// Test function
export async function testRouterPerformance(): Promise<void> {
  console.log('🧪 Testing Ultra-Fast Router Performance...\n');
  
  const testQueries = [
    { query: "Write a Python function to sort a list", expected: 'coding' },
    { query: "Debug this React component", expected: 'debugging' },
    { query: "Write a story about space", expected: 'creative_writing' },
    { query: "What's 2+2?", expected: 'math' },
    { query: "How's the weather?", expected: 'general_chat' }
  ];
  
  // First pass - cold cache
  console.log('🥶 COLD CACHE RUN:');
  for (const test of testQueries) {
    const start = performance.now();
    const result = await intelligentRouter.route({
      query: test.query,
      conversationLength: 0,
      hasCode: false,
      userPlan: 'MAX',
      userId: 'test-user'
    });
    const time = performance.now() - start;
    
    console.log(`  "${test.query.substring(0, 30)}...":`);
    console.log(`    Category: ${result.category} (expected: ${test.expected})`);
    console.log(`    Model: ${result.selectedModel}`);
    console.log(`    Time: ${time.toFixed(1)}ms\n`);
  }
  
  // Second pass - warm cache
  console.log('\n🔥 WARM CACHE RUN:');
  for (const test of testQueries.slice(0, 3)) {
    const start = performance.now();
    const result = await intelligentRouter.route({
      query: test.query,
      conversationLength: 0,
      hasCode: false,
      userPlan: 'MAX',
      userId: 'test-user'
    });
    const time = performance.now() - start;
    
    console.log(`  "${test.query.substring(0, 30)}...": ${time.toFixed(1)}ms (cache hit rate: ${result.performanceMetrics?.cacheHitRate || 0})`);
  }
  
  // Show stats
  const stats = await intelligentRouter.getPerformanceStats();
  console.log('\n📊 Performance Statistics:', stats);
}