/**
 * Main Chat Interface Component
 * 📚 Documentation: 
 * - /DOCS/ui-components.md - UI component architecture
 * - /DOCS/web-search-system.md - Web search toggle and flow
 */

'use client';

import { useState, useRef, useEffect, useCallback, useMemo, startTransition } from 'react';
import { nanoid } from 'nanoid';
import { Message, RouterDecision, MessageRole, AttachmentType } from '@/types';

// Type definitions for image generation
interface ImageGenerationResult {
  images: Array<{
    data: string;
    url?: string;
    revised_prompt?: string;
    format?: string;
    width?: number;
    height?: number;
  }>;
  metadata?: Record<string, any>;
  requestId?: string;
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
  };
}
import { SuspenseMessageList, updateMessagesWithTransition } from './suspense-message-list';
import { ChatInput } from './chat-input';
import { RouterCard } from './router-card';
import { ThinkingAnimation } from './thinking-animation';
import { RouterSelectionBox } from './router-selection-box';
import { LoadingDots } from './loading-dots';
import { motion, AnimatePresence } from 'framer-motion';
import { useChatStore } from '@/stores/chat-store';
import { useConversationStore } from '@/stores/conversationStore';
import { useSession } from 'next-auth/react';
import { Sparkles, Zap, ArrowRight, Crown, Settings, Activity, Brain, ChevronDown, ChevronUp, Code, Calculator, Palette, MessageSquare, Search } from 'lucide-react';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { cleanModelName } from '@/lib/utils/model-display';
import { useAuthValidation } from '@/hooks/use-auth-validation';
import { handleAuthError } from '@/lib/auth-interceptor';
import { getMinimumThinkingTime, getModelSpeed, executeWithOptimalTiming } from '@/utils/getMinimumThinkingTime';
import { recordLatency } from '@/stores/latency-store';
import { useModelCount } from '@/hooks/useModelStats';
import { TOTAL_MODELS_MARKETING, FREE_MESSAGE_LIMITS } from '@/lib/model-stats';
import { useRateLimit, getRateLimitMessage, getResetTimeText } from '@/hooks/use-rate-limit';
import { RateLimitTooltip } from '@/components/ui/rate-limit-tooltip';
import { serializeAttachments, processFilesForPrompt } from '@/lib/ai/file-utils';
import { useLocalStorage } from 'usehooks-ts';
import { PromptSuggestionsInline } from './prompt-suggestions-inline';
import { conversationLock } from '@/lib/utils/conversation-lock';
import { messageSyncQueue } from '@/lib/utils/message-sync-queue';
import { EnhancedErrorDisplay } from './enhanced-error-display';
import { useStreamingEvents } from '@/hooks/use-streaming-events';
// Removed WebSocket image generation imports - keeping it simple
// import { 
//   useImageWebSocket, 
//   ImageGenerationRequest, 
//   ImageGenerationResult, 
//   ImageGenerationProgress,
//   ImageGenerationError 
// } from '@/hooks/useImageWebSocket';

// Image GENERATION model detection (models that CREATE images)
const isImageGenerationModel = (modelId: string): boolean => {
  if (!modelId) return false;
  const lowerModel = modelId.toLowerCase();
  
  // Only models that can generate/create images
  const imageGenerationModels = [
    'dall-e-2', 'dalle-2', 'dall-e-3', 'dalle-3',
    'gpt-image', 'openai/dall-e', 'dall_e',
    'midjourney', 'stable-diffusion', 'flux'
  ];
  
  return imageGenerationModels.some(model => lowerModel.includes(model));
};

// Image generation detection (only for auto mode or image models)
const shouldShowPromptImprovements = (prompt: string, selectedModel: string | null, isAutoMode: boolean): boolean => {
  if (!prompt) return false;
  
  // If manual model selection, only show for image GENERATION models
  if (!isAutoMode && selectedModel) {
    return isImageGenerationModel(selectedModel);
  }
  
  // If auto mode, check if it's an image generation prompt
  if (isAutoMode) {
    const lowerPrompt = prompt.toLowerCase();
    const imageKeywords = [
      'create an image', 'generate an image', 'draw', 'paint', 'sketch', 
      'make a picture', 'create a picture', 'generate a picture',
      'image of', 'picture of', 'photo of', 'illustration of',
      'design a', 'create art', 'make art', 'artistic',
      'digital art', 'concept art', 'artwork',
      'dall-e', 'dalle', 'midjourney', 'stable diffusion'
    ];
    return imageKeywords.some(keyword => lowerPrompt.includes(keyword));
  }
  
  return false;
};

// Enhanced dynamic welcome messages with personalization and timezone support
const getWelcomeMessage = (userName: string, userPreferences?: any) => {
  // Use preferredName from user preferences, fallback to firstName, then to 'User'
  const preferredName = userPreferences?.personalization?.preferredName || 
                       userName?.split(' ')[0] || 
                       'User';
  
  // Get timezone-aware time
  const userTimezone = userPreferences?.preferences?.timezone || 'UTC';
  const userTime = new Date().toLocaleString('en-US', { 
    timeZone: userTimezone, 
    hour12: false, 
    hour: 'numeric' 
  });
  const hour = parseInt(userTime);
  
  // Time-based greetings
  let timeGreeting;
  if (hour >= 5 && hour < 12) {
    timeGreeting = 'Good morning';
  } else if (hour >= 12 && hour < 17) {
    timeGreeting = 'Good afternoon';
  } else if (hour >= 17 && hour < 22) {
    timeGreeting = 'Good evening';
  } else {
    timeGreeting = 'Good night';
  }
  
  // Enhanced rolling messages array with more creative and casual options
  const messages = [
    `${timeGreeting}, ${preferredName}!`,
    `Welcome back, ${preferredName}!`,
    `Hey ${preferredName}! Ready to chat?`,
    `What&apos;s new, ${preferredName}?`,
    `${preferredName}, let&apos;s build something amazing!`,
    `Great to see you again, ${preferredName}!`,
    `${timeGreeting}, ${preferredName}! What&apos;s on your mind?`,
    `Hello ${preferredName}! Time for some AI magic ✨`,
    `${preferredName}, your AI assistant is ready!`,
    `${timeGreeting}, ${preferredName}! Let&apos;s explore together`,
    `Ready to innovate, ${preferredName}?`,
    `${preferredName}, let&apos;s make today productive!`,
    `Hey there, ${preferredName}! What shall we create?`,
    `${preferredName}, your creative playground awaits!`,
    `Ready to dive in, ${preferredName}?`,
    `${preferredName}, let&apos;s turn ideas into reality!`,
    `What can we build together, ${preferredName}?`,
    `${timeGreeting}, ${preferredName}! Ready for some AI-powered magic?`
  ];
  
  // Use current minute to rotate through messages (changes every minute)
  const messageIndex = new Date().getMinutes() % messages.length;
  return messages[messageIndex];
};

// Creative personalized subtitle alternatives
const getPersonalizedSubtitle = (userName: string, userPreferences?: any) => {
  const preferredName = userPreferences?.personalization?.preferredName || 
                       userName?.split(' ')[0] || 
                       'User';
  
  const subtitles = [
    "Your AI playground awaits",
    "Where ideas become reality",
    `Ready to explore, ${preferredName}?`,
    "Innovation at your command",
    "Next-gen intelligence, unlimited possibilities",
    "Where human creativity meets AI power",
    "Your personal AI ecosystem",
    "Building the future, one chat at a time",
    "AI supercharged conversations",
    "Time to unleash your creativity",
    `Let&apos;s create something amazing, ${preferredName}!`,
    "Your next breakthrough starts here",
    "Intelligence amplified, creativity unleashed",
    "Discover what&apos;s possible with AI",
    "Transform ideas into innovations",
    "Your creative companion is here",
    "Explore beyond boundaries",
    "Make the impossible, possible"
  ];
  
  // Use current hour + minute for more variation throughout the day
  const rotationIndex = (new Date().getHours() + new Date().getMinutes()) % subtitles.length;
  return subtitles[rotationIndex];
};

interface ChatInterfaceProps {
  initialMessage?: string;
  initialUltraThink?: boolean;
  initialWebSearch?: boolean;
  initialAttachments?: File[];
}

export function ChatInterface({ initialMessage, initialUltraThink = false, initialWebSearch = false, initialAttachments = [] }: ChatInterfaceProps = {}) {
  const { totalRounded } = useModelCount();
  const [messages, setMessages] = useState<Message[]>([]);
  
  // Enable auth validation for this component
  useAuthValidation({
    checkInterval: 3 * 60 * 1000, // Check every 3 minutes for chat interface
    showToasts: true,
    enabled: true
  });
  const messagesRef = useRef<Message[]>([]);
  const [justCreatedConversation, setJustCreatedConversation] = useState<string | null>(null);
  
  // Get store functions and state
  const {
    conversations,
    activeConversationId,
    initialized,
    initializeConversations,
    createConversation,
    setActiveConversation,
    addConversation,
    getActiveConversation,
    updateConversation,
  } = useConversationStore();
  
  // Refs for accessing current values in callbacks
  const activeConversationIdRef = useRef<string | null>(null);
  const justCreatedConversationRef = useRef<string | null>(null);
  
  // Keep refs in sync with state
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);
  
  useEffect(() => {
    activeConversationIdRef.current = activeConversationId;
  }, [activeConversationId]);
  
  useEffect(() => {
    justCreatedConversationRef.current = justCreatedConversation;
  }, [justCreatedConversation]);
  const [isLoading, setIsLoading] = useState(false);
  const [routerDecision, setRouterDecision] = useState<RouterDecision | null>(null);
  const [streamingMessage, setStreamingMessage] = useState<Message | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isThinking, setIsThinking] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [conversationLoading, setConversationLoading] = useState(false);
  const [streamingStartTime, setStreamingStartTime] = useState<number>(0);
  const streamingStartTimeRef = useRef<number>(0);
  const [currentModelName, setCurrentModelName] = useState<string>('');
  const [welcomeMessage, setWelcomeMessage] = useState<string>('');
  const [pendingAttachments, setPendingAttachments] = useState<any[]>(initialAttachments || []);
  const [isClient, setIsClient] = useState(false);
  const [routerStage, setRouterStage] = useState<string>('Analyzing query...');
  const [isNewConversation, setIsNewConversation] = useState<boolean>(false);
  const [isGeneratingTitle, setIsGeneratingTitle] = useState<boolean>(false);
  const [webSearchEnabled, setWebSearchEnabled] = useState<boolean>(false);
  const [webSearchActive, setWebSearchActive] = useState<boolean>(false);
  const [webSearchResults, setWebSearchResults] = useState<number | null>(null);
  const [webSearchQuery, setWebSearchQuery] = useState<string | null>(null);
  const [webSearchQueries, setWebSearchQueries] = useState<string[]>([]);
  const [ultraThinkActive, setUltraThinkActive] = useState<boolean>(false);
  const routerStageRef = useRef<string>('Analyzing query...');
  const stageOrderRef = useRef<number>(0);
  const [isProcessingAttachments, setIsProcessingAttachments] = useState<boolean>(false);
  const [hasAttachments, setHasAttachments] = useState<boolean>(false);
  const [currentAssistantMessage, setCurrentAssistantMessage] = useState<string>('');
  const [showPromptImprovement, setShowPromptImprovement] = useState<boolean>(false);
  const [currentInputPrompt, setCurrentInputPrompt] = useState<string>('');
  const [showPromptImprovementEnabled, setShowPromptImprovementEnabled] = useLocalStorage('promptImprovementEnabled', true);
  
  // Model selection state
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  
  // Unified status bar state
  const [showRouterDetails, setShowRouterDetails] = useState(false);
  
  // Helper function to analyze prompt for router details
  const analyzePrompt = useCallback((prompt: string, webSearchPerformed?: boolean) => {
    const wordCount = prompt.split(/\s+/).length;
    const hasCode = /```|function|class|def|const|let|var|import|export/i.test(prompt);
    const hasMath = /\d+[\+\-\*\/\^]\d+|equation|calculate|solve/i.test(prompt);
    const isCreative = /story|poem|write|create|imagine|fiction/i.test(prompt);
    const needsSearch = webSearchPerformed || /current|latest|today|news|2024|2025/i.test(prompt);
    
    let queryType = "general";
    if (hasCode) queryType = "coding";
    else if (hasMath) queryType = "mathematical";
    else if (isCreative) queryType = "creative";
    else if (needsSearch) queryType = "research";
    
    return {
      wordCount,
      queryType,
      hasCode,
      hasMath,
      isCreative,
      needsSearch,
      complexity: wordCount > 100 ? "complex" : wordCount > 50 ? "moderate" : "simple"
    };
  }, []);
  
  const getQueryIcon = useCallback((queryType: string) => {
    switch (queryType) {
      case "coding": return Code;
      case "mathematical": return Calculator;
      case "creative": return Palette;
      case "research": return Search;
      default: return MessageSquare;
    }
  }, []);
  
  // Memoized callback to prevent infinite re-renders
  const handleAttachmentsChange = useCallback((files: File[]) => {
    setHasAttachments(files.length > 0);
  }, []);

  // Memoized callback for mode toggle
  const handleModeToggle = useCallback((isAuto: boolean) => {
    console.log('[Chat] Mode toggle:', { 
      newMode: isAuto ? 'auto' : 'manual',
      previousSelectedModel: selectedModel
    });
    setIsAutoMode(isAuto);
    // Clear selected model when switching to auto mode
    if (isAuto) {
      setSelectedModel(null);
      console.log('[Chat] Cleared selected model for auto mode');
    }
  }, [selectedModel]);

  // Memoized callback for prompt suggestion
  const handleApplySuggestion = useCallback((improvedPrompt: string) => {
    setCurrentInputPrompt(improvedPrompt);
  }, []);

  // Memoized callback for web search toggle
  const handleWebSearchToggle = useCallback((enabled: boolean) => {
    setWebSearchEnabled(enabled);
  }, []);
  
  // Removed complex WebSocket image generation - keeping it simple
  // Removed isImageGenerating - no longer using WebSocket image generation
  /*
  const [imageGenerationRequests, setImageGenerationRequests] = useState<Map<string, ImageGenerationRequest>>(new Map());
  const [imageGenerationProgress, setImageGenerationProgress] = useState<Map<string, ImageGenerationProgress>>(new Map());
  const [imageGenerationResults, setImageGenerationResults] = useState<Map<string, ImageGenerationResult>>(new Map());
  
  // Initialize WebSocket hook for image streaming
  const {
    connectionState: wsConnectionState,
    isConnected: wsIsConnected,
    isConnecting: wsIsConnecting,
    error: wsError,
    connect: wsConnect,
    disconnect: wsDisconnect,
    generateImage: wsGenerateImage,
    isGenerating: wsIsGenerating,
    getProgress: wsGetProgress,
  } = useImageWebSocket(
    {}, // Use default config
    useMemo(() => ({
      onProgress: (progress: ImageGenerationProgress) => {
        console.log('[WebSocket] Image generation progress:', progress);
        setImageGenerationProgress(prev => new Map(prev.set(progress.requestId, progress)));
        
        // Update UI to show progress
        if (progress.progress > 0) {
          setIsImageGenerating(true);
        }
      },
      onComplete: (result: ImageGenerationResult) => {
        console.log('[WebSocket] Image generation complete:', result);
        setImageGenerationResults(prev => new Map(prev.set(result.requestId, result)));
        setImageGenerationProgress(prev => {
          const newMap = new Map(prev);
          newMap.delete(result.requestId);
          return newMap;
        });
        setIsImageGenerating(false);
        
        // Add result to current message or create new message
        handleImageGenerationComplete(result);
      },
      onError: (error: ImageGenerationError) => {
        console.error('[WebSocket] Image generation error:', error);
        setImageGenerationProgress(prev => {
          const newMap = new Map(prev);
          newMap.delete(error.requestId);
          return newMap;
        });
        setIsImageGenerating(false);
        
        // Show error in UI
        setError(`Image generation failed: ${error.error}`);
      },
      onConnectionChange: (state) => {
        console.log('[WebSocket] Connection state changed:', state);
      },
      onQueueUpdate: (position, estimatedWaitTime) => {
        console.log('[WebSocket] Queue update:', { position, estimatedWaitTime });
      },
    }), [])  // Empty dependency array - callbacks are stable as they only use refs and setState functions
  );
  */
  
  // Stable callback for router stage updates - prevents going backwards
  const updateRouterStage = useCallback((stage: string) => {
    // Define stage order to prevent backwards movement
    const stageOrder: Record<string, number> = {
      'Analyzing query...': 1,
      'Understanding intent...': 2,
      'Detecting complexity...': 3,
      'Matching capabilities...': 4,
      'Evaluating models...': 5,
      'Optimizing selection...': 6,
      'Model Selected': 7,
      'Generating response...': 8
    };
    
    const newOrder = stageOrder[stage] || 0;
    const currentOrder = stageOrderRef.current;
    
    // Only update if moving forward or same stage
    if (newOrder >= currentOrder) {
      stageOrderRef.current = newOrder;
      routerStageRef.current = stage;
      setRouterStage(stage);
    }
  }, []);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const routerAnalysisStartTimeRef = useRef<number>(0);
  const modelSelectorRef = useRef<any>(null);
  const { currentConversationId, addMessage, updateMessage, setMessages: setChatStoreMessages, loadConversation } = useChatStore();
  const { data: session, status } = useSession();
  const { rateLimit, refetch: fetchRateLimitInfo } = useRateLimit();
  
  // User preferences state for personalized welcome messages
  const [userPreferences, setUserPreferences] = useState<any>(null);
  
  // Fetch user preferences for personalization
  useEffect(() => {
    const fetchUserPreferences = async () => {
      if (session?.user?.id) {
        try {
          const response = await fetch('/api/user/preferences');
          if (response.ok) {
            const data = await response.json();
            setUserPreferences(data.preferences);
          }
        } catch (error) {
          console.warn('Failed to fetch user preferences:', error);
        }
      }
    };
    
    fetchUserPreferences();
  }, [session?.user?.id]);
  
  // Auto-scroll to bottom function
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);
  
  // Scroll to latest message when router analysis appears
  const scrollToLatestMessage = useCallback(() => {
    if (messagesContainerRef.current) {
      const messages = messagesContainerRef.current.querySelectorAll('[data-message-id]');
      const lastMessage = messages[messages.length - 1];
      if (lastMessage) {
        lastMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, []);
  

  // Load conversation messages
  const loadConversationMessages = useCallback(async (conversationId: string) => {
    console.log('[ChatInterface] loadConversationMessages called for:', conversationId);
    setConversationLoading(true);
    
    // Don't clear messages immediately - keep current view while loading
    try {
      if (session?.user) {
        // Load from API for authenticated users
        const response = await fetch(`/api/conversations/${conversationId}`);
        console.log('[ChatInterface] API response status:', response.status);
        if (response.ok) {
          const conversation = await response.json();
          console.log('[ChatInterface] Loaded conversation:', {
            id: conversation.id,
            messageCount: conversation.messages?.length || 0,
            hasMessages: !!conversation.messages,
            firstMessage: conversation.messages?.[0]?.content?.substring(0, 50)
          });
          // Ensure messages exist and transform them properly
          const messagesArray = conversation.messages || [];
          const transformedMessages: Message[] = messagesArray.map((msg: any) => ({
            id: msg.id,
            conversationId: msg.conversationId || conversationId,
            role: msg.role as MessageRole,
            content: msg.content || '',
            createdAt: new Date(msg.createdAt),
            updatedAt: new Date(msg.updatedAt || msg.createdAt),
            model: msg.model,
            provider: msg.provider,
            tokens: msg.totalTokens ? { total: msg.totalTokens } : undefined,
            cost: msg.cost,
            metadata: msg.metadata as any,
            reasoningContent: msg.reasoningContent || undefined,
          }));
          console.log(`[ChatInterface] Transformed ${transformedMessages.length} messages`);
          startTransition(() => {
            setMessages(transformedMessages);
            messagesRef.current = transformedMessages;
            // Also update the chat store to ensure sync
            setChatStoreMessages(transformedMessages);
          });
          console.log(`[ChatInterface] Loaded ${transformedMessages.length} messages for conversation ${conversationId}`);
        } else if (response.status === 404) {
          // Conversation not found - this is okay for new conversations
          console.log(`[ChatInterface] Conversation ${conversationId} not found, starting fresh`);
          setMessages([]);
          messagesRef.current = [];
          setChatStoreMessages([]);
        } else if (response.status === 401) {
          // Auth error - user session is invalid
          console.log('Auth error loading conversation, falling back to local storage');
          const conversation = getActiveConversation();
          if (conversation && conversation.messages) {
            startTransition(() => {
              setMessages(conversation.messages || []);
              messagesRef.current = conversation.messages || [];
            });
          } else {
            startTransition(() => {
              setMessages([]);
              messagesRef.current = [];
            });
          }
        } else {
          console.error(`Failed to load conversation messages: ${response.status} ${response.statusText}`);
          setMessages([]);
          messagesRef.current = [];
          setChatStoreMessages([]);
        }
      } else {
        // Load from local storage for anonymous users
        const conversation = getActiveConversation();
        console.log('[ChatInterface] Loading from local storage:', {
          conversationId,
          found: !!conversation,
          messageCount: conversation?.messages?.length || 0
        });
        if (conversation && conversation.messages) {
          startTransition(() => {
            setMessages(conversation.messages || []);
            messagesRef.current = conversation.messages || [];
            setChatStoreMessages(conversation.messages || []);
          });
          console.log(`[ChatInterface] Loaded ${conversation.messages.length} messages from local storage`);
        } else {
          setMessages([]);
          messagesRef.current = [];
          setChatStoreMessages([]);
        }
      }
    } catch (error) {
      console.error('Error loading conversation:', error);
      setMessages([]);
      messagesRef.current = [];
      setChatStoreMessages([]);
    } finally {
      setConversationLoading(false);
    }
  }, [session?.user, getActiveConversation, setChatStoreMessages]);

  // Client-side hydration fix
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Initialize conversations on mount and when session changes
  useEffect(() => {
    if (!initialized || (session?.user && !conversations.some(c => !c.isLocalOnly))) {
      // Reinitialize if:
      // - Not initialized yet, OR
      // - User is authenticated but we only have local conversations (user just logged in)
      console.log('[ChatInterface] Reinitializing conversations due to session change');
      initializeConversations();
    }
  }, [initialized, initializeConversations, session?.user, conversations]);

  // Handle conversation switching from URL
  useEffect(() => {
    if (!isClient) return; // Only run on client side
    
    const handleUrlChange = (event?: Event) => {
      console.log('[ChatInterface] handleUrlChange called', event?.type);
      const urlParams = new URLSearchParams(window.location.search);
      const conversationParam = urlParams.get('c');
      const wasNewConversation = urlParams.get('new') === 'true';
      
      console.log('[ChatInterface] URL params:', {
        conversationParam,
        wasNewConversation,
        activeConversationId: activeConversationIdRef.current,
        currentURL: window.location.href,
        eventType: event?.type
      });
      
      if (conversationParam) {
        console.log('[ChatInterface] Conversation param found:', conversationParam);
        
        // Always set the active conversation
        setActiveConversation(conversationParam);
        
        // Force reload on conversationchange event (user clicked)
        const isUserClick = event && event.type === 'conversationchange';
        
        // Check if this is a different conversation or we need to reload
        const needsLoad = conversationParam !== activeConversationIdRef.current || 
                         messagesRef.current.length === 0 || 
                         isUserClick;
        
        // Load messages if needed
        if (needsLoad && justCreatedConversationRef.current !== conversationParam) {
          console.log('[ChatInterface] Loading messages for conversation:', conversationParam, 'userClick:', isUserClick);
          loadConversationMessages(conversationParam);
        } else if (justCreatedConversationRef.current === conversationParam) {
          // Clear the flag since we've handled this newly created conversation
          setJustCreatedConversation(null);
        }
        
        setIsNewConversation(false);
      } else if (!conversationParam) {
        // No conversation param - either new chat or clearing
        console.log('[ChatInterface] No conversation param - handling new chat state');
        
        if (wasNewConversation || (event && event.type === 'conversationchange')) {
          // Explicit new conversation request
          console.log('[ChatInterface] Creating new conversation');
          setActiveConversation(null);
          setMessages([]);
          setStreamingMessage(null); // Clear any streaming state
          setCurrentAssistantMessage(''); // Clear assistant message
          setRouterDecision(null); // Clear router state
          setError(null); // Clear any errors
          setWebSearchActive(false); // Clear web search active state but preserve enabled state
          setWebSearchResults(null);
          setWebSearchQuery(null);
          setWebSearchQueries([]);
          // Note: webSearchEnabled state is preserved to maintain user's toggle preference
          setIsLoading(false); // Clear loading state
          setIsThinking(false); // Clear thinking state
          setIsNewConversation(true);
          // Reset to smart router mode for new chats
          setIsAutoMode(true);
          setSelectedModel(null);
          // Clear refs
          messagesRef.current = [];
          activeConversationIdRef.current = null;
        } else if (activeConversationIdRef.current) {
          // Had a conversation but URL cleared - treat as new
          console.log('[ChatInterface] Clearing active conversation for new chat');
          setActiveConversation(null);
          setMessages([]);
          setStreamingMessage(null); // Clear any streaming state
          setCurrentAssistantMessage(''); // Clear assistant message
          setRouterDecision(null); // Clear router state
          setError(null); // Clear any errors
          setWebSearchActive(false); // Clear web search active state but preserve enabled state
          setWebSearchResults(null);
          setWebSearchQuery(null);
          setWebSearchQueries([]);
          // Note: webSearchEnabled state is preserved to maintain user's toggle preference
          setIsLoading(false); // Clear loading state
          setIsThinking(false); // Clear thinking state
          setIsNewConversation(true);
          setIsAutoMode(true);
          setSelectedModel(null);
          // Clear refs
          messagesRef.current = [];
          activeConversationIdRef.current = null;
        } else {
          // First visit or refresh with no conversation
          setIsNewConversation(sessionStorage.getItem('hasUsedNewChat') === 'true');
        }
      }
    };
    
    // Call on mount and when URL changes
    handleUrlChange();
    
    // Listen for URL changes
    window.addEventListener('popstate', handleUrlChange);
    
    // Listen for conversation changes
    window.addEventListener('conversationchange', (event) => {
      console.log('[ChatInterface] conversationchange event received:', event);
      handleUrlChange(event);
    });
    
    // Listen for new chat event
    const handleNewChat = (event: Event) => {
      console.log('[ChatInterface] New chat event received');
      setActiveConversation(null);
      setMessages([]);
      setIsNewConversation(true);
      setIsAutoMode(true);
      setSelectedModel(null);
      setCurrentInputPrompt(''); // Clear the input field
    };
    window.addEventListener('newchat', handleNewChat);
    
    return () => {
      window.removeEventListener('popstate', handleUrlChange);
      window.removeEventListener('conversationchange', handleUrlChange);
      window.removeEventListener('newchat', handleNewChat);
    };
  }, [isClient, setActiveConversation, loadConversationMessages]);

  // Update welcome message for logged-in users
  useEffect(() => {
    if (session?.user?.name) {
      const updateWelcome = () => {
        setWelcomeMessage(getWelcomeMessage(session.user.name!, userPreferences));
      };
      
      // Set initial message
      updateWelcome();
      
      // Update every minute to rotate messages
      const interval = setInterval(updateWelcome, 60000);
      
      return () => clearInterval(interval);
    }
  }, [session?.user?.name, userPreferences]);

  // Handle WebSocket image generation completion
  const handleImageGenerationComplete = useCallback(async (result: ImageGenerationResult) => {
    try {
      // Get the reconstructed image from the image reconstructor
      // For now, just use the data from the result
      // const reconstructor = (wsConnection as any).imageReconstructorRef?.current;
      let imageData = result.images[0]?.data;
      
      // If no data in result, log error
      // if (!imageData && reconstructor) {
      //   const reconstructedData = reconstructor.getImage(result.requestId);
      //   if (reconstructedData) {
      //     imageData = reconstructedData;
      //     // Clean up the reconstructor
      //     reconstructor.cleanup(result.requestId);
      //   }
      // }
      
      if (!imageData || (typeof imageData === 'string' ? imageData.length === 0 : (imageData as ArrayBuffer).byteLength === 0)) {
        console.error('[WebSocket] No image data in result or reconstructor');
        // If we have a URL, use that instead
        if (result.images[0]?.url) {
          console.log('[WebSocket] Using URL from result instead of binary data');
          const imageMessage: Message = {
            id: nanoid(),
            conversationId: activeConversationIdRef.current!,
            role: MessageRole.ASSISTANT,
            content: `Here's the image you requested:\n\n![Generated Image](${result.images[0].url})\n\n${result.images[0].revised_prompt ? `*Revised prompt: ${result.images[0].revised_prompt}*` : ''}`,
            createdAt: new Date(),
            updatedAt: new Date(),
            model: result.metadata?.model,
            metadata: {
              imageGeneration: {
                requestId: result.requestId,
                format: result.images[0].format,
                width: result.images[0].width,
                height: result.images[0].height,
                timeGenerated: result.metadata?.timeGenerated,
              },
              usage: result.usage,
            },
          };
          
          // Add to messages
          startTransition(() => {
            const newMessages = [...messagesRef.current, imageMessage];
            setMessages(newMessages);
            messagesRef.current = newMessages;
          });
          
          // Save to conversation
          if (session?.user && activeConversationIdRef.current) {
            addMessage(imageMessage);
          }
          
          return;
        }
        return;
      }
      
      // Convert ArrayBuffer to base64 using native browser API for better performance and reliability
      let dataUrl: string;
      
      try {
        // Method 1: Use Blob and FileReader for large images (most reliable)
        const blob = new Blob([imageData], { type: `image/${result.images[0].format || 'png'}` });
        const reader = new FileReader();
        
        dataUrl = await new Promise<string>((resolve, reject) => {
          reader.onloadend = () => {
            if (reader.result) {
              resolve(reader.result as string);
            } else {
              reject(new Error('Failed to read blob'));
            }
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      } catch (error) {
        // Fallback: Use chunked approach if FileReader fails
        console.warn('FileReader failed, using chunked base64 encoding:', error);
        const bytes = typeof imageData === 'string' 
          ? new Uint8Array(atob(imageData).split('').map(c => c.charCodeAt(0)))
          : new Uint8Array(imageData);
        const chunks: string[] = [];
        const chunkSize = 0x8000; // 32KB chunks
        
        for (let i = 0; i < bytes.length; i += chunkSize) {
          const chunk = bytes.subarray(i, Math.min(i + chunkSize, bytes.length));
          chunks.push(String.fromCharCode.apply(null, Array.from(chunk)));
        }
        
        const base64 = btoa(chunks.join(''));
        dataUrl = `data:image/${result.images[0].format || 'png'};base64,${base64}`;
      }
      
      // Create or update message with image
      const currentMessages = messagesRef.current;
      const conversationId = activeConversationIdRef.current;
      
      if (!conversationId) {
        console.error('[WebSocket] No active conversation for image result');
        return;
      }
      
      // Create new assistant message with the image
      const imageMessage: Message = {
        id: nanoid(),
        conversationId,
        role: MessageRole.ASSISTANT,
        content: `Here's the image you requested:

![Generated Image](${dataUrl})

${result.images[0].revised_prompt ? `*Revised prompt: ${result.images[0].revised_prompt}*` : ''}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        model: result.metadata?.model,
        metadata: {
          imageGeneration: {
            requestId: result.requestId,
            format: result.images[0].format,
            width: result.images[0].width,
            height: result.images[0].height,
            timeGenerated: result.metadata?.timeGenerated,
          },
          usage: result.usage,
        },
      };
      
      // Add to messages
      startTransition(() => {
        const newMessages = [...currentMessages, imageMessage];
        setMessages(newMessages);
        messagesRef.current = newMessages;
      });
      
      // Save to conversation
      if (session?.user) {
        addMessage(imageMessage);
      }
      
      console.log('[WebSocket] Image generation result added to conversation');
    } catch (error) {
      console.error('[WebSocket] Error handling image generation result:', error);
      setError('Failed to display generated image');
    }
  }, [activeConversationIdRef, session?.user, addMessage]);
  
  // Removed WebSocket image generation - requests now go to chosen model directly

  // Auto-scroll to keep streaming message visible
  useEffect(() => {
    if (streamingMessage?.content && messagesContainerRef.current) {
      // Find the streaming message element
      const streamingElement = messagesContainerRef.current.querySelector(`[data-message-id="${streamingMessage.id}"]`);
      if (streamingElement) {
        // Use requestAnimationFrame for smooth scrolling
        requestAnimationFrame(() => {
          streamingElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'nearest',
            inline: 'nearest'
          });
        });
      } else {
        // Fallback to scroll to bottom
        requestAnimationFrame(() => {
          messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        });
      }
    }
  }, [streamingMessage?.content, streamingMessage?.id]); // Trigger on content changes

  // Auto-scroll to bottom is now handled by useScrollStabilizer

  const handleSendMessage = useCallback(async (content: string, attachments?: File[] | number, webSearchEnabled?: boolean, ultraThink?: boolean, isRetry?: boolean | undefined) => {
    // Handle both signatures - attachments from UI, retryCount from internal retry
    const retryCount = typeof attachments === 'number' ? attachments : 0;
    const actualAttachments = Array.isArray(attachments) ? attachments : undefined;
    setError(null);
    
    // Clear the input prompt immediately when sending (unless this is a retry)
    if (!isRetry) {
      setCurrentInputPrompt('');
    }
    
    // Removed WebSocket image generation routing - let the chosen model handle all requests directly
    
    // Create conversation if needed with lock to prevent race conditions
    // Force new conversation creation if this is marked as a new conversation
    let conversationId = isNewConversation ? null : activeConversationId;
    if (!conversationId) {
      const hasLock = await conversationLock.acquireLock();
      
      // If we didn't get the lock, another creation is in progress
      if (!hasLock) {
        // Wait a bit and check if a conversation was created
        await new Promise(resolve => setTimeout(resolve, 100));
        conversationId = activeConversationIdRef.current;
        
        if (!conversationId) {
          setError('A conversation is already being created. Please try again.');
          return;
        }
      } else {
        try {
          let newConv;
          if (session?.user) {
            // Create via API for authenticated users
            newConv = await createConversation({
              title: content.slice(0, 50) + (content.length > 50 ? '...' : ''),
              preview: content.slice(0, 100) + (content.length > 100 ? '...' : ''),
            });
          } else {
            // Create locally for anonymous users
            newConv = addConversation({
              title: content.slice(0, 50) + (content.length > 50 ? '...' : ''),
              preview: content.slice(0, 100) + (content.length > 100 ? '...' : ''),
              messages: messages, // Include existing messages
            });
          }
          conversationId = newConv.id;
          setActiveConversation(conversationId);
          setJustCreatedConversation(conversationId); // Track that we just created this conversation
          setIsNewConversation(false); // Reset new conversation flag after creation
        
        // Update URL with new conversation ID (only on client)
        if (isClient) {
          const url = new URL(window.location.href);
          url.searchParams.set('c', conversationId);
          url.searchParams.delete('new'); // Remove new conversation flag
          window.history.pushState({}, '', url.toString());
          
          // Dispatch event after a small delay to ensure state is updated
          setTimeout(() => {
            window.dispatchEvent(new Event('conversationchange'));
          }, 50);
        }
        
        // Conversation will be automatically updated in the store when the message is saved
        // No need to force refresh here as it causes race conditions
      } catch (error) {
        console.error('Failed to create conversation:', error);
        // Continue without conversation for anonymous users
        if (!session?.user) {
          conversationId = 'temp-' + nanoid();
        } else {
          setError('Failed to create conversation');
          return;
        }
      } finally {
        // Always release the lock
        conversationLock.releaseLock();
      }
      }
    }
    
    // It's a new message, not a retry
    const { newPrompt, remainingAttachments } = await processFilesForPrompt(actualAttachments || [], content);
    
    setHasAttachments(remainingAttachments.length > 0);

    // Use messagesRef.current to ensure we have the latest messages
    const currentMessages = messagesRef.current;
    let messagesWithUser = currentMessages;
    
    // Only create a new user message if this is NOT a retry
    if (!isRetry) {
      const userMessage = {
        id: nanoid(),
        role: MessageRole.USER,
        content: newPrompt, // Use the potentially modified prompt
        createdAt: new Date(),
        conversationId: conversationId || '', // Use the conversation ID we have
        updatedAt: new Date(),
        attachments: remainingAttachments.length > 0 ? remainingAttachments.map(file => ({
          id: nanoid(),
          name: file.name,
          size: file.size,
          type: getAttachmentType(file.type),
          url: '',
          mimeType: file.type
        })) : undefined
      };
      
      messagesWithUser = [...currentMessages, userMessage];
      setMessages(messagesWithUser);
    }
    
    // Update messagesRef immediately to keep it in sync
    messagesRef.current = messagesWithUser;
    setIsLoading(true);
    setIsThinking(true);
    setRouterDecision(null);
    updateRouterStage('Analyzing query...');
    setCurrentPrompt(newPrompt);
    setCurrentAssistantMessage(''); // Reset assistant message
    if (webSearchEnabled) {
      setWebSearchQuery(newPrompt);
    } else {
      setWebSearchQuery(null);
    }
    if (isClient) {
      const startTime = Date.now();
      setStreamingStartTime(startTime);
      streamingStartTimeRef.current = startTime;
    }
    
    // Set current model name for latency tracking
    if (!isAutoMode && selectedModel) {
      setCurrentModelName(selectedModel);
    } else {
      setCurrentModelName(''); // Will be set by router decision
    }
    
    // Track when thinking started to ensure minimum display time
    const thinkingStartTime = isClient ? Date.now() : 0;

    try {
      // Serialize any remaining (non-text) attachments
      console.log('[ChatInterface] Processing remaining attachments:', remainingAttachments.length || 0);
      const serializedAttachments = remainingAttachments.length > 0
        ? await serializeAttachments(remainingAttachments)
        : null;
      console.log('[ChatInterface] Serialized remaining attachments:', serializedAttachments?.length || 0);

      const requestBody = {
        messages: messagesWithUser,
        conversationId,
        // Include manual model selection if not in auto mode
        // IMPORTANT: Only send manualModel if we're NOT in auto mode AND have a selected model
        ...((!isAutoMode && selectedModel && selectedModel !== '') && { manualModel: selectedModel }),
        // Include web search flag (always send the boolean value)
        webSearchEnabled: webSearchEnabled,
        // Include ULTRA THINK flag for premium model routing
        ...(ultraThink && { ultraThink: true }),
        // Include serialized attachments if provided
        ...(serializedAttachments && { attachments: serializedAttachments })
      };

      // Debug logging for web search
      console.log('[ChatInterface] 🔍 WEB SEARCH DEBUG - Sending request:', {
        webSearchEnabled,
        webSearchEnabledType: typeof webSearchEnabled,
        requestBodyWebSearch: requestBody.webSearchEnabled,
        hasWebSearchInBody: 'webSearchEnabled' in requestBody
      });
      
      console.log('[Chat] Sending request to /api/chat:', {
        url: '/api/chat',
        method: 'POST',
        payload: requestBody,
        messageCount: requestBody.messages.length,
        isAutoMode,
        selectedModel,
        hasManualModel: !!requestBody.manualModel,
        manualModelCondition: `!isAutoMode(${!isAutoMode}) && selectedModel(${selectedModel})`,
        willSendManualModel: (!isAutoMode && selectedModel) ? true : false
      });

      // Make the API request with auth error handling
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        // Use centralized auth error handling
        if (await handleAuthError(response)) {
          setIsLoading(false);
          return; // Auth interceptor handles logout and redirect
        }
        throw new Error(`API error: ${response.status}`);
      }

      // Create assistant message for streaming
      const assistantMessage: Message = {
        id: nanoid(),
        conversationId,
        role: MessageRole.ASSISTANT,
        content: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        streaming: true
      };

      setStreamingMessage(assistantMessage);
      
      // Immediately scroll to ensure new message is visible
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
      
      // Show web search indicator early if web search is enabled
      if (webSearchEnabled) {
        console.log('[Chat] Web search enabled, showing indicator early');
        setWebSearchActive(true);
        setWebSearchQuery('Preparing web search...');
      }

      // Read SSE stream
      const reader = response.body!.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6);
            if (dataStr === '[DONE]') {
              continue;
            }
            
            try {
              const data = JSON.parse(dataStr);
              
              if (data.type === 'router') {
                // Show router decision immediately in the animation
                console.log('[Chat] Router decision received:', data.decision);
                setRouterDecision(data.decision);
                
                // Update model name when decision is received
                const selectedModelForResponse = data.decision.recommendedModels?.[0]?.modelId || 
                                    data.decision.selectedModel || 
                                    data.decision.modelId ||
                                    'Unknown Model';
                setCurrentModelName(selectedModelForResponse);
                
                // Hide thinking indicator once we have a decision
                setIsThinking(false);
                
                // Refresh rate limit info
                if (data.rateLimit) {
                  fetchRateLimitInfo();
                }
                
                // Track total response timing (includes router + web search)
                const totalResponseTime = Date.now() - (thinkingStartTime || 0);
                const routerLatency = data.decision.metadata?.routerLatency || 0;
                let webSearchTime = 0;
                
                console.log(`[Chat] Response timing breakdown:`);
                console.log(`  - Router decision: ${routerLatency}ms`);
                if (data.decision.webSearchPerformed) {
                  webSearchTime = totalResponseTime - routerLatency;
                  console.log(`  - Web search: ${webSearchTime}ms`);
                }
                console.log(`  - Total response: ${totalResponseTime}ms`);
                
                // Store router speed for dynamic animation timing (only on client)
                if (isClient) {
                  (window as any).__routerSpeed = routerLatency; // Use actual router time, not total
                }
                
                assistantMessage.model = selectedModelForResponse;
                assistantMessage.metadata = { 
                  routerDecision: data.decision,
                  manuallySelected: data.decision.metadata?.manuallySelected || false,
                  webSearchPerformed: data.decision.webSearchPerformed,
                  searchResults: data.decision.webSearchResultsData || undefined,
                  webSearchQueries: data.decision.webSearchQueries || []
                };
                console.log('[Chat] Assistant message model set to:', assistantMessage.model);
                console.log('[Chat] Full router decision:', data.decision);
                // Update streaming message to trigger re-render with model set
                setStreamingMessage({ ...assistantMessage });

                // Update web-search indicator state from router info
                if (typeof data.decision.webSearchPerformed !== 'undefined') {
                  const webSearchPerformed = !!data.decision.webSearchPerformed;
                  console.log('[Chat] Setting web search state:', {
                    webSearchPerformed,
                    webSearchResults: data.decision.webSearchResults,
                    webSearchQueries: data.decision.webSearchQueries,
                    webSearchTime: data.decision.webSearchTime || webSearchTime || null
                  });
                  setWebSearchActive(webSearchPerformed);
                  setWebSearchResults(data.decision.webSearchResults || null);
                  setWebSearchQueries(data.decision.webSearchQueries || []);
                  setWebSearchQuery(data.decision.webSearchQueries?.[0] || null);
                }
              } else if (data.type === 'content') {
                // Record TTFT latency on first content token
                if (assistantMessage.content === '' && streamingStartTimeRef.current > 0 && currentModelName) {
                  const ttft = Date.now() - streamingStartTimeRef.current;
                  recordLatency(currentModelName, ttft);
                  updateRouterStage('Generating response...');
                }
                
                // Track when main content starts (for reasoning box close animation)
                const isFirstContent = assistantMessage.content === '';
                if (isFirstContent) {
                  assistantMessage.metadata = {
                    ...assistantMessage.metadata,
                    mainContentStarted: true,
                    mainContentStartTime: Date.now()
                  };
                }
                
                // Append content smoothly - improved duplication check for Google models
                const newContent = data.content;
                const currentContent = assistantMessage.content;
                
                // For Google models, check if this content would create a duplication
                // Google sometimes sends cumulative content that includes previous parts
                let shouldAppend = true;
                
                if (newContent.length > 0) {
                  // If the new content is already contained within the current content, skip it
                  if (currentContent.includes(newContent)) {
                    shouldAppend = false;
                  }
                  // If current content ends with new content, it's a duplicate
                  else if (currentContent.endsWith(newContent)) {
                    shouldAppend = false;
                  }
                  // If new content starts with the current content, replace instead of append
                  else if (newContent.startsWith(currentContent) && newContent.length > currentContent.length) {
                    assistantMessage.content = newContent;
                    setStreamingMessage({ ...assistantMessage });
                    shouldAppend = false;
                  }
                }
                
                if (shouldAppend) {
                  assistantMessage.content += newContent;
                  setStreamingMessage({ ...assistantMessage });
                }
                // Note: currentAssistantMessage not needed when streamingMessage exists
              } else if (data.type === 'reasoning') {
                // Handle reasoning/thinking content - DO NOT update main message
                if (!assistantMessage.reasoningContent) {
                  assistantMessage.reasoningContent = '';
                }
                assistantMessage.reasoningContent += data.content;
                console.log('[Chat] Reasoning content updated:', assistantMessage.reasoningContent.length, 'total chars');
                // Only update reasoning content, not the whole message
                setStreamingMessage(prev => {
                  if (!prev) return assistantMessage;
                  return {
                    ...prev,
                    reasoningContent: assistantMessage.reasoningContent
                  };
                });
              } else if (data.type === 'reasoning_summary') {
                // Handle reasoning summary from O-series models via Responses API
                assistantMessage.reasoningContent = data.reasoning_summary;
                assistantMessage.metadata = {
                  ...assistantMessage.metadata,
                  reasoningSummary: data.reasoning_summary,
                  reasoningSummaryType: 'auto' // Could be extracted from model config
                };
                // Only update reasoning content and metadata
                setStreamingMessage(prev => {
                  if (!prev) return assistantMessage;
                  return {
                    ...prev,
                    reasoningContent: assistantMessage.reasoningContent,
                    metadata: assistantMessage.metadata
                  };
                });
              } else if (data.type === 'streaming_mode') {
                // Handle non-streaming mode notification
                console.log('[Chat] Streaming mode notification:', data);
                assistantMessage.metadata = {
                  ...assistantMessage.metadata,
                  streaming: data.streaming,
                  modelType: data.modelType,
                  streamingMessage: data.message
                };
                // Update streaming message to trigger UI update
                setStreamingMessage(prev => {
                  if (!prev) return assistantMessage;
                  return {
                    ...prev,
                    streaming: data.streaming,
                    metadata: assistantMessage.metadata
                  };
                });
              } else if (data.type === 'usage') {
                // Handle token usage information
                if (data.usage) {
                  assistantMessage.tokens = {
                    prompt: data.usage.prompt_tokens || 0,
                    completion: data.usage.completion_tokens || 0,
                    total: data.usage.total_tokens || 0,
                    reasoning: data.usage.reasoning_tokens || 0
                  };
                  console.log('[Chat] Token usage received:', assistantMessage.tokens);
                  // Update streaming message to show tokens
                  setStreamingMessage(prev => {
                    if (!prev) return assistantMessage;
                    return {
                      ...prev,
                      tokens: assistantMessage.tokens
                    };
                  });
                }
              } else if (data.metadata?.imageGeneration) {
                // Handle image generation metadata
                console.log('[Chat] Image metadata received:', {
                  hasImage: !!data.metadata.image,
                  imageType: data.metadata.image?.type,
                  dataLength: data.metadata.image?.data?.length,
                  urlLength: data.metadata.image?.url?.length,
                  hasOriginalPrompt: !!data.metadata.originalPrompt,
                  hasEnhancementReasoning: !!data.metadata.enhancementReasoning
                });
                
                assistantMessage.metadata = {
                  ...assistantMessage.metadata,
                  imageGeneration: true,
                  image: data.metadata.image,
                  originalPrompt: data.metadata.originalPrompt,
                  enhancementReasoning: data.metadata.enhancementReasoning
                };
                
                console.log('[Chat] Setting assistant message metadata:', {
                  hasMetadata: !!assistantMessage.metadata,
                  hasImage: !!assistantMessage.metadata.image,
                  imageType: assistantMessage.metadata.image?.type
                });
                
                setStreamingMessage({ ...assistantMessage });
              } else if (data.type === 'image_enhancement_progress') {
                // Handle image enhancement progress (display separately, don't add to content)
                console.log('[Chat] Image enhancement progress:', data.progress);
                // Could display this in a separate progress indicator if desired
                // For now, just log it to avoid cluttering the message content
              } else if (data.choices) {
                // Standard OpenAI-style streaming format
                const chunk = data.choices[0];
                if (chunk.delta?.content) {
                  // Track when main content starts
                  const isFirstContent = assistantMessage.content === '';
                  if (isFirstContent) {
                    assistantMessage.metadata = {
                      ...assistantMessage.metadata,
                      mainContentStarted: true,
                      mainContentStartTime: Date.now()
                    };
                  }
                  
                  // Check for duplication before appending - improved detection
                  const deltaContent = chunk.delta.content;
                  const currentContent = assistantMessage.content;
                  
                  // Enhanced duplication detection for all streaming models
                  let shouldAppend = true;
                  
                  if (deltaContent.length > 0) {
                    // If the delta content is already contained within the current content, skip it
                    if (currentContent.includes(deltaContent)) {
                      shouldAppend = false;
                    }
                    // If current content ends with delta content, it's a duplicate
                    else if (currentContent.endsWith(deltaContent)) {
                      shouldAppend = false;
                    }
                    // If delta content starts with the current content, replace instead of append
                    else if (deltaContent.startsWith(currentContent) && deltaContent.length > currentContent.length) {
                      assistantMessage.content = deltaContent;
                      setStreamingMessage({ ...assistantMessage });
                      shouldAppend = false;
                    }
                  }
                  
                  if (shouldAppend) {
                    assistantMessage.content += deltaContent;
                    setStreamingMessage({ ...assistantMessage });
                  }
                }
              } else if (data.type === 'error') {
                console.error('[Chat] Stream error:', data.error);
                
                // Check if error object exists and has content
                if (!data.error || typeof data.error !== 'object') {
                  throw new Error('Unknown error occurred');
                }
                
                // Check for auth required error
                if (data.error.requiresAuth || data.error.message?.includes('limit reached')) {
                  // Rate limit will be updated by the hook automatically
                  fetchRateLimitInfo();
                  
                  // Show sign in prompt for anonymous users who hit the limit
                  setMessages(prev => [...prev, {
                    id: nanoid(),
                    conversationId,
                    role: MessageRole.ASSISTANT,
                    content: `✨ **Hit your daily limit? Great news!**\n\nCreating an account takes just 10 seconds and multiplies your limits by 2x 🚀\n\n**Benefits of signing in:**\n• ${FREE_MESSAGE_LIMITS.LOGGED_IN} free messages per day (vs ${FREE_MESSAGE_LIMITS.ANONYMOUS})\n• Access to ${TOTAL_MODELS_MARKETING} powerful AI models\n• Save your conversation history\n• Priority during high traffic\n\nOur AI wants to chat more with you! Click the green "Sign in" button above to continue.`,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    metadata: {
                      error: true,
                      requiresAuth: true
                    }
                  }]);
                  setStreamingMessage(null);
                  throw new Error('Authentication required');
                }
                
                // Re-throw other errors
                const errorMessage = data.error.message || data.error.code || 'Stream processing error';
                throw new Error(errorMessage);
              }
            } catch (e) {
              console.error('[Chat] Failed to parse SSE data:', e);
            }
          }
        }
      }

      // Streaming complete
      assistantMessage.streaming = false;
      // Fetch updated rate limit info after successful request for anonymous users
      if (!session) {
        fetchRateLimitInfo();
      }

      // Finalize message - use the complete message list to avoid state sync issues
      const finalAssistantMessage = { ...assistantMessage, streaming: false };
      const finalMessages = [...messagesWithUser, finalAssistantMessage];
      
      console.log('[Chat] Finalizing messages. User message count:', messagesWithUser.length, 'Total final count:', finalMessages.length);
      console.log('[Chat] Final assistant message reasoning content:', finalAssistantMessage.reasoningContent?.length || 0, 'chars');
      
      // Set the complete message list to avoid any sync issues
      setMessages(finalMessages);
      setStreamingMessage(null);
      setCurrentAssistantMessage(''); // Clear current assistant message
      
      // Update messagesRef to keep it in sync
      messagesRef.current = finalMessages;

      // Save to store
      addMessage(messagesWithUser[messagesWithUser.length - 1]);
      addMessage(finalAssistantMessage);

      // Update conversation store with latest message info
      if (conversationId && !conversationId.startsWith('temp-')) {
        // Use the final messages array for accuracy
        updateConversation(conversationId, {
          lastMessageAt: new Date(),
          messageCount: finalMessages.length,
          preview: finalAssistantMessage.content.length > 100 
            ? finalAssistantMessage.content.slice(0, 100) + '...' 
            : finalAssistantMessage.content,
          modelPreference: finalAssistantMessage.model,
          unread: false,
          // Store messages for anonymous users - use the accurate message list
          ...(!session?.user && { messages: finalMessages })
        });
        
        // For authenticated users, ensure remote sync happens
        if (session?.user) {
          // Sync the conversation update to remote
          const updateConversationRemote = useConversationStore.getState().updateConversationRemote;
          updateConversationRemote(conversationId, {
            lastMessageAt: new Date(),
            messageCount: finalMessages.length,
            preview: finalAssistantMessage.content.length > 100 
              ? finalAssistantMessage.content.slice(0, 100) + '...' 
              : finalAssistantMessage.content,
            modelPreference: finalAssistantMessage.model,
          }).catch(error => {
            console.error('Failed to sync conversation update:', error);
          });
        }

        // Auto-generate title for authenticated users
        if (session?.user) {
          const conversation = getActiveConversation();
          if (conversation) {
            const shouldGenerateTitle = 
              // Generate for new conversations (default titles like "Chat 1", "New Chat")
              conversation.title.match(/^(Chat \d+|New Chat)$/i) ||
              // Or if conversation has few messages (2-4 messages = good context for title)
              (finalMessages.length >= 2 && finalMessages.length <= 4) ||
              // Or if title is generic and we have more context now
              (conversation.title === 'Start a new conversation...' || conversation.title.length < 15);
              
            if (shouldGenerateTitle && session?.user) {
              // Generate title asynchronously (don't block UI) - only for authenticated users
              setTimeout(async () => {
                try {
                  setIsGeneratingTitle(true);
                  const response = await fetch(`/api/conversations/${conversationId}/generate-title`, {
                    method: 'POST',
                  });
                  
                  if (response.ok) {
                    const data = await response.json();
                    console.log('✨ Auto-generated title:', data.generatedTitle);
                    // Title is automatically updated via the conversation store
                    if (data.conversation) {
                      updateConversation(conversationId, { 
                        title: data.conversation.title,
                        updatedAt: new Date(data.conversation.updatedAt)
                      });
                    }
                  }
                } catch (error) {
                  console.log('Auto title generation failed (non-critical):', error);
                  // Don't show errors for auto-generation failures
                } finally {
                  setIsGeneratingTitle(false);
                }
              }, 1500); // Small delay to ensure message is saved to DB
            } else if (shouldGenerateTitle && !session?.user) {
              // For anonymous users, generate a simple title from the first message
              const simpleTitle = messagesWithUser[messagesWithUser.length - 1].content.length > 50 
                ? messagesWithUser[messagesWithUser.length - 1].content.slice(0, 50) + '...' 
                : messagesWithUser[messagesWithUser.length - 1].content;
              updateConversation(conversationId, { 
                title: simpleTitle,
                updatedAt: new Date()
              });
            }
          }
        }
      }

    } catch (error) {
      console.error('[Chat] Error in handleSendMessage:', error);
      console.error('[Chat] Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        type: error?.constructor?.name,
        error: error
      });
      
      // Clean up streaming message if it exists
      setStreamingMessage(null);
      setIsThinking(false);
      
      // Create error message with enhanced display
      let errorContent = '❌ I encountered an error. Please try again.';
      let errorCode: string | number | undefined;
      
      if (error instanceof Error) {
        // Extract error code if available
        const errorAny = error as any;
        errorCode = errorAny.code || errorAny.status;
        
        if (error.message.includes('All providers failed')) {
          errorContent = error.message;
        } else if (error.message.includes('429')) {
          errorContent = '⏱️ Rate limit reached. Please wait a moment before trying again.';
          errorCode = 429;
        } else if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          errorContent = '⚠️ The AI service encountered an internal error. Please try again.';
          errorCode = 500;
        } else if (error.message.includes('502') || error.message.includes('Bad Gateway')) {
          errorContent = '🌐 Unable to reach the AI service. Please try again shortly.';
          errorCode = 502;
        } else if (error.message.includes('503') || error.message.includes('Service Unavailable')) {
          errorContent = '⚠️ The AI service is temporarily unavailable. Please try again in a moment.';
          errorCode = 503;
        } else if (error.message.includes('504') || error.message.includes('Gateway Timeout')) {
          errorContent = '⏱️ The request took too long. Please try with a shorter message.';
          errorCode = 504;
        } else if (error.message.includes('API') || error.message.includes('api_key')) {
          errorContent = '🔑 There\'s an issue with the API configuration. Please try a different model.';
          errorCode = 'api_error';
        } else {
          errorContent = error.message;
        }
      }
      
      // Check for specific error types and retry if possible
      let shouldRetry = false;
      const maxRetries = 3;
      
      if (error instanceof Error) {
        // Check if error has retryable flag
        const errorAny = error as any;
        if (errorAny.retryable === true) {
          shouldRetry = true;
        }
        
        if (error.message.includes('503') || error.message.includes('Service Unavailable')) {
          shouldRetry = true;
        } else if (error.message.includes('429') || error.message.includes('Rate limit')) {
          shouldRetry = true;
        } else if (error.message.includes('network') || error.message.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')) {
          shouldRetry = true;
        } else if (error.message.includes('All providers failed')) {
          shouldRetry = true;
        }
      }
      
      // Auto-retry logic
      if (shouldRetry && retryCount < maxRetries) {
        const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff
        
        console.log(`[Chat] Auto-retrying in ${retryDelay}ms (attempt ${retryCount + 1}/${maxRetries})`);
        
        // Show retry message with enhanced display
        const retryMessage: Message = {
          id: nanoid(),
          conversationId: activeConversationId || 'new',
          role: MessageRole.ASSISTANT,
          content: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { 
            isRetry: true,
            errorDisplay: {
              error: error instanceof Error ? error : new Error(String(error)),
              errorCode: String(errorCode || 'CHAT_ERROR'),
              retryingIn: Math.ceil(retryDelay / 1000),
              model: selectedModel || undefined
            }
          }
        };
        
        setMessages(prev => [...prev, retryMessage]);
        
        // Wait and retry
        setTimeout(() => {
          // Remove retry message and retry
          setMessages(prev => prev.filter(m => m.id !== retryMessage.id));
          handleSendMessage(content, retryCount + 1, undefined, undefined, true);
        }, retryDelay);
        
        return;
      }
      
      const errorMessage: Message = {
        id: nanoid(),
        conversationId: conversationId || 'new',
        role: MessageRole.ASSISTANT,
        content: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        error: {
          code: String(errorCode || 'CHAT_ERROR'),
          message: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          errorDisplay: {
            error: error instanceof Error ? error : new Error(String(error)),
            errorCode: String(errorCode || 'CHAT_ERROR'),
            model: selectedModel || currentModelName || undefined
          }
        }
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      setIsThinking(false);
      setStreamingMessage(null);
      setStreamingStartTime(0);
      updateRouterStage('Analyzing query...');
      // Reset web-search indicator with delay to allow animation to complete
      setTimeout(() => {
        setWebSearchActive(false);
        setWebSearchResults(null);
        setWebSearchQuery(null);
        setWebSearchQueries([]);
      }, 500);
      setIsProcessingAttachments(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeConversationId, session, createConversation, addConversation, 
      setActiveConversation, addMessage, updateConversation, fetchRateLimitInfo, isClient,
      isAutoMode, selectedModel, updateRouterStage, getActiveConversation]); // messages intentionally excluded to prevent infinite loop

  // Track if we've processed the initial message to prevent loops
  const processedInitialMessageRef = useRef<string | null>(null);

  // Handle initial message from landing page
  useEffect(() => {
    if (initialMessage && initialized && processedInitialMessageRef.current !== initialMessage) {
      console.log('[ChatInterface] Sending initial message:', initialMessage);
      processedInitialMessageRef.current = initialMessage;
      // Clear any existing conversation first to ensure we start fresh
      setActiveConversation(null);
      // Wait a tick to ensure state is updated
      setTimeout(() => {
        handleSendMessage(initialMessage, initialAttachments, initialWebSearch, initialUltraThink);
      }, 10);
    }
  }, [initialMessage, initialized, initialWebSearch, initialUltraThink, initialAttachments, handleSendMessage, setActiveConversation]);

  // Handle retry with different model
  const handleRetryMessage = useCallback(async (messageId: string, retryModelId?: string) => {
    // Use ref to get current messages without dependency
    const currentMessages = messagesRef.current;
    
    // Find the message to retry
    const messageIndex = currentMessages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;
    
    // Find the user message before this assistant message
    let userMessageIndex = messageIndex - 1;
    while (userMessageIndex >= 0 && currentMessages[userMessageIndex].role !== MessageRole.USER) {
      userMessageIndex--;
    }
    
    if (userMessageIndex < 0) return;
    
    const userMessage = currentMessages[userMessageIndex];
    
    // Remove all messages from the failed assistant message onwards
    const newMessages = currentMessages.slice(0, messageIndex);
    setMessages(newMessages);
    
    // If a specific model is provided, switch to manual mode and select it
    if (retryModelId) {
      setIsAutoMode(false);
      setSelectedModel(retryModelId);
    }
    
    // Retry with the original user message (pass isRetry: true to prevent duplicate user message)
    await handleSendMessage(userMessage.content, undefined, undefined, undefined, true);
  }, [handleSendMessage]);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Green Status Bar - Removed per user request */}

      {/* Removed WebSocket Status Indicator - keeping it simple */}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto smooth-scroll relative min-h-0" ref={messagesContainerRef}>
        {/* Loading overlay - doesn't replace content */}
        {conversationLoading && (
          <div className="absolute inset-0 bg-black/20 backdrop-blur-sm z-10 flex items-center justify-center">
            <div className="flex flex-col items-center gap-3 bg-gray-900/80 rounded-lg px-6 py-4 border border-gray-600">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-400"></div>
              <p className="text-gray-300 text-sm">Loading conversation...</p>
            </div>
          </div>
        )}
        
        {/* Always show messages, even while loading */}
        <div className="max-w-4xl mx-auto px-4 py-1">
            {/* Unified Purple Status Bar with Router Selection */}
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-purple-800/20 border border-purple-400/30 rounded-lg backdrop-blur-sm mb-3 relative overflow-hidden"
            >
              {/* Animated background shimmer when router decision is present */}
              {routerDecision && (
                <motion.div
                  style={{
                    position: 'absolute',
                    inset: 0,
                    background: 'linear-gradient(to right, transparent, rgba(168, 85, 247, 0.1), transparent)'
                  }}
                  animate={{ x: ['-100%', '200%'] }}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity, 
                    ease: "linear",
                    repeatDelay: 1
                  }}
                />
              )}
              
              {/* Main content */}
              <div className="relative z-10">
                <div className="p-2 sm:p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs sm:text-sm text-purple-300 flex-wrap">
                      {isThinking && !routerDecision ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          >
                            <Activity className="w-4 h-4" />
                          </motion.div>
                          <span>Analyzing your request...</span>
                        </>
                      ) : isLoading && !routerDecision ? (
                        <>
                          <Sparkles className="w-4 h-4 animate-pulse" />
                          <span>AI Router working...</span>
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4" />
                          <span className="hidden sm:inline">
                            {session?.user ? (
                              session.user.plan === 'MAX' ? 
                                `Unlimited access to all ${totalRounded} AI models` :
                                `${rateLimit?.messagesRemaining || 0} messages remaining today`
                            ) : (
                              `${rateLimit?.messagesRemaining || 0} free messages remaining`
                            )}
                          </span>
                          <span className="sm:hidden">
                            {session?.user ? (
                              session.user.plan === 'MAX' ? 
                                `${totalRounded} AI models` :
                                `${rateLimit?.messagesRemaining || 0} left`
                            ) : (
                              `${rateLimit?.messagesRemaining || 0} free left`
                            )}
                          </span>
                          
                          {/* Router decision info */}
                          {routerDecision && (
                            <>
                              <span className="text-gray-500">•</span>
                              <span className="text-purple-300">
                                {routerDecision.metadata?.manuallySelected ? 'You selected' : 'Intelligently routed to'}
                              </span>
                              <span className="font-semibold text-white">
                                {routerDecision.model?.displayName || cleanModelName(routerDecision.actualModel || routerDecision.modelId)}
                              </span>
                            </>
                          )}
                        </>
                      )}
                    </div>
                    
                    {/* Right side actions */}
                    <div className="flex items-center gap-2">
                      {/* Expand/Collapse button for router details */}
                      {routerDecision && (
                        <motion.button
                          onClick={() => setShowRouterDetails(!showRouterDetails)}
                          className="flex items-center gap-1 px-2 py-1 text-xs text-purple-300 hover:text-white transition-colors"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <span className="hidden sm:inline">Details</span>
                          {showRouterDetails ? 
                            <ChevronUp className="w-3 h-3" /> : 
                            <ChevronDown className="w-3 h-3" />
                          }
                        </motion.button>
                      )}
                      
                      {!session && (
                        <motion.button
                          onClick={() => signIn('google')}
                          className="flex items-center gap-1 px-2 sm:px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-xs font-medium text-white transition-colors"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <span className="hidden sm:inline">Sign in</span>
                          <ArrowRight className="w-3 h-3" />
                        </motion.button>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Expandable router details */}
                <AnimatePresence>
                  {showRouterDetails && routerDecision && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden border-t border-purple-400/20"
                    >
                      <div className="p-3 space-y-3">
                        {/* Query Analysis */}
                        {(() => {
                          const analysis = analyzePrompt(currentPrompt, routerDecision.webSearchPerformed);
                          const QueryIcon = getQueryIcon(analysis.queryType);
                          
                          return (
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs">
                              <div className="flex items-center gap-2">
                                <QueryIcon className="w-4 h-4 text-purple-400" />
                                <span className="text-gray-400">Query type:</span>
                                <span className="text-purple-300 capitalize">{analysis.queryType} query</span>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Brain className="w-4 h-4 text-purple-400" />
                                <span className="text-gray-400">Complexity:</span>
                                <span className="text-purple-300 capitalize">{analysis.complexity}</span>
                              </div>
                              
                              {routerDecision.webSearchPerformed && (
                                <div className="flex items-center gap-2">
                                  <Search className="w-4 h-4 text-green-400" />
                                  <span className="text-green-300">Web search performed</span>
                                </div>
                              )}
                              
                              {routerDecision.metrics?.estimatedCredits && (
                                <div className="flex items-center gap-2">
                                  <Activity className="w-4 h-4 text-purple-400" />
                                  <span className="text-gray-400">Estimated credits:</span>
                                  <span className="text-purple-300">{routerDecision.metrics.estimatedCredits}</span>
                                </div>
                              )}
                            </div>
                          );
                        })()}
                        
                        {/* User-friendly model capabilities and features */}
                        {(() => {
                          const features = [];
                          
                          // Check web search usage
                          if (routerDecision.webSearchPerformed) {
                            features.push({
                              icon: Search,
                              text: "Web search enabled",
                              color: "text-green-400"
                            });
                          }
                          
                          // Check multimodal capabilities
                          if (routerDecision.model?.supportsVision || routerDecision.metadata?.supportsVision) {
                            features.push({
                              icon: Brain,
                              text: "Vision & image analysis",
                              color: "text-purple-400"
                            });
                          }
                          
                          // Check function calling
                          if (routerDecision.model?.supportsFunctionCalling) {
                            features.push({
                              icon: Code,
                              text: "Function calling",
                              color: "text-blue-400"
                            });
                          }
                          
                          // Check reasoning capabilities
                          if (routerDecision.model?.supportsReasoning) {
                            features.push({
                              icon: Brain,
                              text: "Advanced reasoning",
                              color: "text-orange-400"
                            });
                          }
                          
                          // Check large context window
                          if (routerDecision.model?.contextWindow > 100000) {
                            features.push({
                              icon: Activity,
                              text: "Long context support",
                              color: "text-cyan-400"
                            });
                          }
                          
                          // Add console.log for development
                          if (process.env.NODE_ENV === 'development') {
                            console.log('🎯 Router Decision:', {
                              reasoning: routerDecision.reasoning,
                              model: routerDecision.model?.displayName || routerDecision.modelId,
                              provider: routerDecision.provider,
                              confidence: routerDecision.confidence,
                              webSearchPerformed: routerDecision.webSearchPerformed,
                              webSearchQueries: routerDecision.webSearchQueries,
                              alternatives: routerDecision.alternatives,
                              metadata: routerDecision.metadata,
                              metrics: routerDecision.metrics
                            });
                          }
                          
                          return features.length > 0 ? (
                            <div className="space-y-2">
                              <p className="text-xs text-gray-400">Model capabilities:</p>
                              <div className="flex flex-wrap gap-3">
                                {features.map((feature, index) => {
                                  const Icon = feature.icon;
                                  return (
                                    <div key={index} className="flex items-center gap-1.5">
                                      <Icon className={`w-3 h-3 ${feature.color}`} />
                                      <span className="text-xs text-gray-300">{feature.text}</span>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          ) : null;
                        })()}
                        
                        {/* Performance estimate */}
                        {routerDecision.metrics?.estimatedLatency && (
                          <div className="flex items-center gap-2 text-xs">
                            <Zap className="w-4 h-4 text-yellow-400" />
                            <span className="text-gray-400">Estimated response time:</span>
                            <span className="text-yellow-300">{(routerDecision.metrics.estimatedLatency / 1000).toFixed(1)}s</span>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
            
            {/* Image generation progress removed for simplicity */}
            
            <div>
              <SuspenseMessageList 
                messages={messages}
                streamingMessage={streamingMessage}
                onRetryMessage={handleRetryMessage}
                webSearchActive={webSearchActive}
                webSearchQuery={webSearchQuery || undefined}
                webSearchQueries={webSearchQueries}
                webSearchResults={webSearchResults || undefined}
                isLoading={isLoading}
                isProcessingAttachments={isProcessingAttachments}
                currentAssistantMessage={currentAssistantMessage}
              />
            </div>
            
            <div ref={messagesEndRef} />
          </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="max-w-4xl mx-auto px-4 py-2">
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg px-4 py-2 text-sm text-red-400">
            {error}
          </div>
        </div>
      )}

      {/* Input Area - Only show at bottom when there are messages */}
      {messages.length > 0 && (
        <div className={cn(
          "flex-shrink-0 border-t border-gray-800",
          // Add safe area padding for mobile devices
          "pb-safe"
        )}>
          <div className="max-w-4xl mx-auto">
            {/* Prompt Suggestions Container - Reserve space to prevent layout shift */}
            <div className="min-h-0 transition-all duration-200">
              {showPromptImprovementEnabled && currentInputPrompt && !isLoading && shouldShowPromptImprovements(currentInputPrompt, selectedModel, isAutoMode) && (
                <PromptSuggestionsInline
                  prompt={currentInputPrompt}
                  onApplySuggestion={handleApplySuggestion}
                />
              )}
            </div>
            
            {/* Only show chat input when there are messages (conversation mode) */}
            {messages.length > 0 && (
              <ChatInput 
                onSendMessage={handleSendMessage}
                onAttachmentsChange={handleAttachmentsChange}
                userPlan={session?.user?.plan as any || 'FREE'}
                onUpgrade={() => window.open('/subscribe', '_blank')}
                isStreaming={isLoading}
                isLoading={isLoading}
                disabled={false}
                sendDisabled={rateLimit?.messagesRemaining === 0}
                placeholder={
                  rateLimit?.messagesRemaining === 0 
                    ? "You've reached the free message limit. Please sign in to continue..."
                    : "Message JustSimpleChat..."
                }
                showAnimatedPlaceholder={false}
                initialPrompt={currentInputPrompt}
                onPromptChange={setCurrentInputPrompt}
                pendingAttachments={pendingAttachments}
                onClearAttachments={() => setPendingAttachments([])}
                isAnalyzing={isThinking && !routerDecision}
                enableWebSearch={webSearchEnabled}
                onWebSearchToggle={setWebSearchEnabled}
                webSearchActive={webSearchActive}
                setWebSearchActive={setWebSearchActive}
                ultraThinkActive={ultraThinkActive}
                setUltraThinkActive={setUltraThinkActive}
                showModelSelector={true}
                showWebSearch={true}
                showUltraThink={true}
                modelSelectorRef={modelSelectorRef}
                rateLimit={rateLimit}
                userSession={session}
                onOpenModelSelector={() => modelSelectorRef.current?.openModal()}
                selectedModel={selectedModel}
                isAutoMode={isAutoMode}
                setSelectedModel={setSelectedModel}
                setIsAutoMode={setIsAutoMode}
              />
            )}
        </div>
      </div>
      )}



    </div>
  );
}

// Minimal interface for new conversations (no marketing content)
function NewConversationScreen({ 
  onExampleClick, 
  rateLimit, 
  session 
}: { 
  onExampleClick: (message: string) => void;
  rateLimit: { messagesUsed: number; messagesRemaining: number; resetAt: string | null; limit: number } | null;
  session: any;
}) {
  const examples = [
    {
      prompt: "Help me write a function in Python",
      icon: "💻"
    },
    {
      prompt: "Explain quantum computing simply",
      icon: "🔬"
    },
    {
      prompt: "Plan a weekend trip to Paris",
      icon: "✈️"
    },
    {
      prompt: "Review my resume for improvements",
      icon: "📄"
    }
  ];

  return (
    <div className="h-full flex items-center justify-center px-4 new-conversation-screen">
      <div className="max-w-2xl mx-auto w-full text-center space-y-6">
        
        {/* Simple welcome */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
        >
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mobile-icon-adjust">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
          
          <h2 className="text-2xl font-bold text-white mobile-text-adjust">
            {session?.user ? (
              <>What&apos;s new, {session.user.name?.split(' ')[0] || 'there'}?</>
            ) : (
              <>What would you like to chat about?</>
            )}
          </h2>
          
          <p className="text-gray-300">
            {session?.user ? (
              <>Make the impossible, possible</>
            ) : (
              <>Ask me anything - I&apos;ll automatically choose the best AI model for your question.</>
            )}
          </p>
          
          {/* Status info */}
          <div className="flex flex-col items-center gap-1">
            <div className="flex items-center gap-2 text-sm font-medium text-purple-400">
              <Brain className="w-4 h-4" />
              <span>Smart AI Router</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              {session ? (
                <span>
                  {session.user?.plan || 'Free'} plan - {
                    rateLimit?.messagesRemaining === -1 
                      ? 'Unlimited messages'
                      : `${Math.max(0, rateLimit?.messagesRemaining || 0)} messages remaining`
                  }
                </span>
              ) : (
                <span>{rateLimit?.messagesRemaining || FREE_MESSAGE_LIMITS.ANONYMOUS} free messages remaining</span>
              )}
            </div>
          </div>
        </motion.div>

        {/* Quick examples */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-3"
        >
          <p className="text-sm text-gray-400">Try these examples:</p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {examples.map((example, index) => (
              <motion.button
                key={index}
                onClick={() => onExampleClick(example.prompt)}
                className="group p-4 bg-gray-900/50 hover:bg-gray-800/50 border border-gray-800 hover:border-gray-700 rounded-xl transition-all duration-200 text-left"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.05 }}
              >
                <div className="flex items-center gap-3">
                  <span className="text-xl">{example.icon}</span>
                  <span className="text-sm text-gray-300 group-hover:text-white transition-colors">
                    {example.prompt}
                  </span>
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
}

// Welcome Screen Component
function WelcomeScreen({ 
  onExampleClick, 
  rateLimit, 
  session 
}: { 
  onExampleClick: (message: string) => void;
  rateLimit: { messagesUsed: number; messagesRemaining: number; resetAt: string | null; limit: number } | null;
  session: any;
}) {
  const { totalRounded } = useModelCount();
  const examples = [
    {
      category: "Save Time",
      prompt: "Debug my Python code that's throwing errors",
      icon: "🐛",
      benefit: "Fix bugs in seconds"
    },
    {
      category: "Make Money",
      prompt: "Create a viral TikTok script for my product",
      icon: "💸",
      benefit: "Boost your sales"
    },
    {
      category: "Learn Faster",
      prompt: "Explain blockchain like I'm 10 years old",
      icon: "🧠",
      benefit: "Master complex topics"
    },
    {
      category: "Create More",
      prompt: "Design a landing page that converts visitors",
      icon: "🎨",
      benefit: "Ship projects faster"
    }
  ];

  const features = [
    {
      icon: "🎯",
      title: "Smart AI Router",
      description: "Automatically selects the best AI model for your specific question"
    },
    {
      icon: "💰",
      title: "Max Power, Minimum Cost",
      description: "Our router finds the cheapest model capable of your prompt, delivering premium results without the premium price."
    },
    {
      icon: "⚡",
      title: `${totalRounded} AI Models`,
      description: "Access to GPT-4, Claude, Gemini, Grok and many more"
    },
    {
      icon: "🚀",
      title: "No Setup Required",
      description: "Start chatting instantly - no API keys or complicated setup"
    }
  ];

  const models = [
    { name: "O3 Series Reasoning", use: "Complex reasoning & analysis", color: "from-green-400 to-blue-500" },
    { name: "Claude 4", use: "Code & technical tasks", color: "from-orange-400 to-red-500" },
    { name: "Gemini 2.5 Pro", use: "Creative & multimodal", color: "from-blue-400 to-cyan-500" },
    { name: "Grok 3", use: "Real-time & web search", color: "from-purple-400 to-pink-500" }
  ];

  return (
    <div className="h-full overflow-y-auto welcome-screen-container">
      <div className="max-w-4xl mx-auto px-4 py-4 sm:py-6 lg:py-8 space-y-3 sm:space-y-4 lg:space-y-6 welcome-screen-padding">
    
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="text-center space-y-3 sm:space-y-4">
        <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/30 text-xs text-green-300 mb-2">
          <Sparkles className="w-3 h-3 animate-pulse" />
          <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent font-medium">Multi-Model AI Chat Platform</span>
        </div>
        
        <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight mobile-text-adjust">
          <span className="bg-gradient-to-r from-white via-green-200 to-blue-200 bg-clip-text text-transparent">
            Great to see you again!
          </span>
          <br />
          <span className="bg-gradient-to-r from-green-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent">
            Your AI playground awaits
          </span>
        </h1>
        
        <p className="text-sm sm:text-base lg:text-lg text-gray-300 max-w-2xl mx-auto leading-relaxed">
          Ask me anything - I&apos;ll automatically choose the best AI model for your question.
          Access to {totalRounded} models including GPT-4o, Claude 4, Gemini 2.5 Pro, and more.
        </p>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4 pt-4">
          <div className="flex items-center gap-2 text-sm text-green-400">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            Start chatting instantly
          </div>
          <div className="flex items-center gap-2 text-sm text-blue-400">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
            {rateLimit ? `${rateLimit.messagesRemaining}/${rateLimit.limit} free chats` : 'Loading...'}
          </div>
          <div className="flex items-center gap-2 text-sm text-cyan-400">
            <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
            {session ? `${FREE_MESSAGE_LIMITS.LOGGED_IN} messages/day` : `Sign in for ${FREE_MESSAGE_LIMITS.LOGGED_IN} daily`}
          </div>
        </div>
        </div>
      </motion.div>

      {/* Plan Comparison - Above the Fold */}
      {!session && (
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.15 }}
        >
          <div className="bg-gradient-to-br from-gray-900/40 to-gray-800/30 border border-gray-600/30 rounded-xl p-3 sm:p-4 lg:p-5">
            <div className="text-center mb-3">
              <h3 className="text-base sm:text-lg font-bold text-white mb-1">
                🚀 Unlock More AI Power
              </h3>
              <p className="text-xs sm:text-sm text-gray-300">
                Sign in now for 2x more messages and instant access to all {totalRounded} models
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
              <div className="bg-gray-900/50 rounded-lg p-3 border border-gray-700">
                <h4 className="font-medium text-sm mb-2 text-gray-400">Free (Now)</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center gap-2 text-gray-300">
                    <span className="text-red-400">✗</span> {FREE_MESSAGE_LIMITS.ANONYMOUS} messages/day only
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <span className="text-red-400">✗</span> Basic models only
                  </div>
                  <div className="flex items-center gap-2 text-gray-300">
                    <span className="text-red-400">✗</span> No conversation history
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-green-900/30 to-emerald-900/30 rounded-lg p-3 border border-green-500/50 relative">
                <div className="absolute -top-3 -right-3 bg-green-400 text-black text-xs font-bold px-2 py-1 rounded-full transform rotate-12 shadow-lg">
                  POPULAR
                </div>
                <h4 className="font-medium text-sm mb-2 text-green-400">Freemium (Sign In)</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center gap-2 text-green-300">
                    <span className="text-green-400">✓</span> {FREE_MESSAGE_LIMITS.LOGGED_IN} messages/day
                  </div>
                  <div className="flex items-center gap-2 text-green-300">
                    <span className="text-green-400">✓</span> Access to more models
                  </div>
                  <div className="flex items-center gap-2 text-green-300">
                    <span className="text-green-400">✓</span> Save conversations
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-br from-gray-900/40 to-blue-900/20 rounded-lg p-3 border border-blue-500/30">
                <h4 className="font-medium text-sm mb-2 text-blue-400">Plus & Beyond</h4>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center gap-2 text-blue-300">
                    <span className="text-blue-400">★</span> 1000+ messages/month
                  </div>
                  <div className="flex items-center gap-2 text-blue-300">
                    <span className="text-blue-400">★</span> All {totalRounded} AI models
                  </div>
                  <div className="flex items-center gap-2 text-blue-300">
                    <span className="text-blue-400">★</span> Priority support
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 text-center">
              <button
                onClick={() => signIn('google', { callbackUrl: '/chat' })}
                className="inline-flex items-center gap-2 px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-full transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg shadow-green-500/25"
              >
                <Sparkles className="w-4 h-4" />
                                        Sign In Free - Get {FREE_MESSAGE_LIMITS.LOGGED_IN} Messages Daily
              </button>
              <p className="text-xs text-gray-400 mt-2">
                No credit card required • Instant access
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Features Grid */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <div className="group p-3 sm:p-4 rounded-xl bg-gradient-to-br from-gray-900/50 to-gray-800/30 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-300 hover:scale-105 h-full">
                <div className="text-lg sm:text-xl mb-1.5 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="font-semibold text-white mb-1 text-xs sm:text-sm">
                  {feature.title}
                </h3>
                <p className="text-xs text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* AI Provider Logos */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="space-y-4">
          <div className="text-center">
            <h2 className="text-lg sm:text-xl font-bold text-white mb-1">
              Powered by Industry Leaders
            </h2>
            <p className="text-xs sm:text-sm text-gray-400">
              Access {totalRounded} models from the world&apos;s top AI providers
            </p>
          </div>
          
          <div className="flex flex-wrap items-center justify-center gap-4 sm:gap-6 lg:gap-8">
            {/* OpenAI */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="group"
            >
              <div className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-900/50 border border-gray-800 hover:border-gray-700 transition-all duration-300 group-hover:scale-110">
                <div className="w-6 h-6 bg-white rounded-sm flex items-center justify-center">
                  <span className="text-black font-bold text-xs">AI</span>
                </div>
                <span className="text-sm font-medium text-gray-300">OpenAI</span>
              </div>
            </motion.div>
            
            {/* Anthropic */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.55 }}
              className="group"
            >
              <div className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-900/50 border border-gray-800 hover:border-gray-700 transition-all duration-300 group-hover:scale-110">
                <div className="w-6 h-6 bg-orange-500 rounded-sm flex items-center justify-center">
                  <span className="text-white font-bold text-xs">A</span>
                </div>
                <span className="text-sm font-medium text-gray-300">Anthropic</span>
              </div>
            </motion.div>
            
            {/* Google */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 }}
              className="group"
            >
              <div className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-900/50 border border-gray-800 hover:border-gray-700 transition-all duration-300 group-hover:scale-110">
                <div className="w-6 h-6 flex items-center justify-center">
                  <span className="text-2xl">🔷</span>
                </div>
                <span className="text-sm font-medium text-gray-300">Google</span>
              </div>
            </motion.div>
            
            {/* xAI (Grok) */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.65 }}
              className="group"
            >
              <div className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-900/50 border border-gray-800 hover:border-gray-700 transition-all duration-300 group-hover:scale-110">
                <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-xs">X</span>
                </div>
                <span className="text-sm font-medium text-gray-300">xAI</span>
              </div>
            </motion.div>
          </div>
          
          {/* Model Examples */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 mt-4">
            {models.map((model, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.75 + index * 0.05 }}
              >
                <div className="text-center p-2 rounded-lg bg-gray-900/30 border border-gray-800/50">
                  <div className={`w-full h-0.5 rounded-full bg-gradient-to-r ${model.color} mb-2`} />
                  <h3 className="text-xs font-medium text-gray-300">
                    {model.name}
                  </h3>
                  <p className="text-xs text-gray-500 mt-0.5">
                    {model.use}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Example Prompts */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <div className="space-y-3">
        <div className="text-center">
          <h2 className="text-base sm:text-lg font-bold text-white">
            Real Results, Real Fast
          </h2>
        </div>
        
        <div className="grid grid-cols-2 gap-2 sm:gap-3">
          {examples.map((example, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.7 + index * 0.05 }}
            >
              <button
                onClick={() => onExampleClick(example.prompt)}
                className="group w-full p-2.5 sm:p-3 text-left rounded-lg bg-gradient-to-br from-gray-900/60 to-gray-800/30 border border-gray-700/50 hover:border-blue-500/50 hover:bg-gradient-to-br hover:from-blue-900/20 hover:to-cyan-900/20 transition-all duration-200 hover:scale-105"
              >
                <div className="flex items-start gap-2">
                  <span className="text-lg sm:text-xl flex-shrink-0">
                    {example.icon}
                  </span>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs font-bold text-green-400 mb-0.5">
                      {example.category}
                    </div>
                    <p className="text-xs sm:text-sm text-gray-300 group-hover:text-white transition-colors line-clamp-2">
                      &quot;{example.prompt}&quot;
                    </p>
                    <div className="text-xs text-blue-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {example.benefit} →
                    </div>
                  </div>
                </div>
              </button>
            </motion.div>
          ))}
        </div>
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <div className="text-center space-y-2 pb-3">
        <div className="inline-block p-3 rounded-xl bg-gradient-to-br from-gray-900/30 to-blue-900/30 border border-blue-500/30">
          <h3 className="text-base sm:text-lg font-bold text-white mb-1">
            Ready to get started?
          </h3>
          <p className="text-xs sm:text-sm text-gray-300 mb-2">
            Just type your question below and watch our AI router work its magic!
          </p>
          <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-green-400">
            <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 animate-pulse" />
            <span>Your first message is free and instant</span>
          </div>
        </div>
        </div>
      </motion.div>

    </div>
  </div>
  );
}

function getAttachmentType(mimeType: string): AttachmentType {
  if (mimeType.startsWith('image/')) return AttachmentType.IMAGE;
  if (mimeType.startsWith('audio/')) return AttachmentType.AUDIO;
  if (mimeType.startsWith('video/')) return AttachmentType.VIDEO;
  if (mimeType.includes('pdf')) return AttachmentType.DOCUMENT;
  if (mimeType.includes('text/')) return AttachmentType.CODE;
  return AttachmentType.DATA;
}