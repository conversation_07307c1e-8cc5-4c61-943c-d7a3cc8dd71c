'use client';

import { motion } from 'framer-motion';
import { <PERSON>, Sparkles, Zap, Activity, Cpu, Layers, Circle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

interface ThinkingMessageProps {
  isActive?: boolean;
  routerLatency?: number;
}

export function ThinkingMessage({ isActive = true, routerLatency }: ThinkingMessageProps) {
  const [currentStage, setCurrentStage] = useState(0);
  const stages = ['Analyzing...', 'Routing...', 'Optimizing...'];
  
  useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        setCurrentStage((prev: number) => (prev + 1) % stages.length);
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isActive, stages.length]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.8, rotateX: -30 }}
      animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
      exit={{ opacity: 0, y: -20, scale: 0.8, rotateX: 30 }}
      transition={{ duration: 0.2, ease: 'easeOut' }}
      style={{ position: 'relative', marginBottom: '1rem' }}
    >
      <div className="flex gap-3">
        {/* Avatar with intense animation */}
        <div className="flex-shrink-0 relative">
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{ 
              rotate: { duration: 2, repeat: Infinity, ease: "linear" },
              scale: { duration: 0.5, repeat: Infinity }
            }}
            style={{ 
              width: '2rem', 
              height: '2rem', 
              borderRadius: '50%',
              background: 'linear-gradient(to bottom right, rgb(147 51 234), rgb(37 99 235), rgb(6 182 212))',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 10px 15px -3px rgba(147, 51, 234, 0.5)'
            }}
          >
            <Brain className="w-4 h-4 text-white" />
          </motion.div>
          
          {/* Orbiting particles */}
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              style={{ position: 'absolute', inset: 0 }}
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 1.5 - i * 0.3, 
                repeat: Infinity, 
                ease: "linear",
                delay: i * 0.2 
              }}
            >
              <div className={cn(
                "absolute w-1.5 h-1.5 rounded-full",
                i === 0 && "bg-purple-400 top-0 left-1/2 -translate-x-1/2 -translate-y-2",
                i === 1 && "bg-blue-400 bottom-0 left-1/2 -translate-x-1/2 translate-y-2",
                i === 2 && "bg-cyan-400 top-1/2 right-0 translate-x-2 -translate-y-1/2"
              )} />
            </motion.div>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 space-y-1.5">
          {/* Name with glitch effect */}
          <div className="flex items-center gap-3 text-sm">
            <motion.span 
              animate={{ 
                backgroundPosition: ['0%', '100%', '0%'],
              }}
              transition={{ duration: 2, repeat: Infinity }}
              style={{ 
                backgroundSize: '200% 100%',
                fontWeight: '500',
                backgroundImage: 'linear-gradient(to right, rgb(196 181 253), rgb(147 197 253), rgb(103 232 249))',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                color: 'transparent',
                position: 'relative'
              }}
            >
              AI Router
              {/* Glitch effect */}
              <motion.span
                style={{
                  position: 'absolute',
                  inset: 0,
                  backgroundImage: 'linear-gradient(to right, rgb(196 181 253), rgb(147 197 253), rgb(103 232 249))',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  color: 'transparent'
                }}
                animate={{ 
                  x: [-1, 1, -1],
                  opacity: [0, 1, 0]
                }}
                transition={{ duration: 0.2, repeat: Infinity, repeatDelay: 2 }}
              >
                AI Router
              </motion.span>
            </motion.span>
            
            <motion.span 
              style={{
                fontSize: '0.75rem',
                color: 'rgb(196 181 253)',
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem'
              }}
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              <Sparkles className="w-3 h-3" />
              {stages[currentStage]}
            </motion.span>
          </div>

          {/* Ultra-fast thinking visualization */}
          <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-purple-900/30 via-blue-900/30 to-cyan-900/30 border border-purple-500/40 p-3">
            {/* Lightning background */}
            <div className="absolute inset-0">
              <motion.div
                style={{
                  position: 'absolute',
                  inset: 0,
                  background: 'linear-gradient(to right, rgba(168, 85, 247, 0.1), transparent, rgba(6, 182, 212, 0.1))'
                }}
                animate={{ x: ['-100%', '200%'] }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <motion.div
                style={{
                  position: 'absolute',
                  inset: 0,
                  background: 'linear-gradient(to left, rgba(59, 130, 246, 0.1), transparent, rgba(168, 85, 247, 0.1))'
                }}
                animate={{ x: ['200%', '-100%'] }}
                transition={{ duration: 0.8, repeat: Infinity, ease: "linear" }}
              />
            </div>
            
            <div className="relative flex items-center justify-between">
              {/* Quick status icons */}
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ 
                    rotate: [0, 360],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{ duration: 0.5, repeat: Infinity }}
                >
                  <Cpu className="w-4 h-4 text-purple-400" />
                </motion.div>
                
                <div className="flex gap-1">
                  {[0, 1, 2, 3, 4].map((i) => (
                    <motion.div
                      key={i}
                      style={{
                        width: '2rem',
                        height: '0.25rem',
                        background: 'linear-gradient(to right, rgb(168 85 247), rgb(59 130 246))',
                        borderRadius: '9999px'
                      }}
                      animate={{ 
                        scaleX: [0, 1, 0],
                        opacity: [0.3, 1, 0.3]
                      }}
                      transition={{ 
                        duration: 0.6,
                        repeat: Infinity,
                        delay: i * 0.1
                      }}
                    />
                  ))}
                </div>
                
                <motion.div
                  animate={{ 
                    y: [0, -3, 0]
                  }}
                  transition={{ duration: 0.3, repeat: Infinity }}
                >
                  <Activity className="w-4 h-4 text-cyan-400" />
                </motion.div>
              </div>

              {/* Speed indicator */}
              <motion.div 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem',
                  fontSize: '0.75rem',
                  color: 'rgb(74 222 128)'
                }}
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 0.5, repeat: Infinity }}
              >
                <Zap className="w-3 h-3" />
                <span className="font-mono">
                  {routerLatency ? `${Math.round(routerLatency)}ms` : "<50ms"}
                </span>
              </motion.div>
            </div>

            {/* Matrix-style falling characters (subtle) */}
            <div className="absolute inset-0 pointer-events-none opacity-20">
              {[0, 1, 2, 3].map((col) => (
                <motion.div
                  key={col}
                  style={{ 
                    position: 'absolute',
                    fontSize: '0.75rem',
                    fontFamily: 'monospace',
                    color: 'rgb(74 222 128)',
                    left: `${col * 25}%` 
                  }}
                  animate={{ y: [-20, 60] }}
                  transition={{ 
                    duration: 1.5 - col * 0.2,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                >
                  {['0', '1', 'AI', '∞'][col]}
                </motion.div>
              ))}
            </div>
          </div>

          {/* Neural network pulse effect */}
          <div className="absolute -right-2 -top-2 pointer-events-none">
            <motion.div
              animate={{ 
                scale: [1, 1.5, 1],
                opacity: [0.3, 0, 0.3]
              }}
              transition={{ duration: 1.5, repeat: Infinity }}
              style={{
                width: '4rem',
                height: '4rem',
                borderRadius: '50%',
                border: '1px solid rgba(196, 181, 253, 0.3)'
              }}
            />
            <motion.div
              animate={{ 
                scale: [1, 1.3, 1],
                opacity: [0.5, 0, 0.5]
              }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
              style={{
                position: 'absolute',
                inset: 0,
                width: '4rem',
                height: '4rem',
                borderRadius: '50%',
                border: '1px solid rgba(147, 197, 253, 0.3)'
              }}
            />
          </div>

          {/* Floating tech icons */}
          <div className="absolute -left-8 top-0 opacity-30">
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, 360]
              }}
              transition={{ 
                y: { duration: 2, repeat: Infinity },
                rotate: { duration: 4, repeat: Infinity, ease: "linear" }
              }}
            >
              <Layers className="w-5 h-5 text-purple-400" />
            </motion.div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}