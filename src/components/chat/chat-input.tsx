'use client';

import { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Paperclip,
  Mic,
  StopCircle,
  Image as ImageIcon,
  FileText,
  X,
  Loader2,
  Search,
  Brain
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateFileSize, formatFileSize, MAX_FILE_SIZE_DISPLAY } from '@/lib/upload-constants';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { AnimatedPlaceholder } from './AnimatedPlaceholder';
import { ModelSelectorEnhanced } from './model-selector-enhanced';
import { WebSearchButton } from './web-search-button';
import { UltraThinkToggle } from '@/components/ui/ultra-think-toggle';
import { UserPlan } from '@/types';
import { useSession, signIn } from 'next-auth/react';
import { supportsWebSearch } from '@/lib/thinking-models';
import { KeyboardShortcut } from '@/components/ui/keyboard-shortcut';
import toast from 'react-hot-toast';
import { FREE_MESSAGE_LIMITS } from '@/lib/model-stats';
import Link from 'next/link';
import { LEGAL_TEXT, LEGAL_LINKS } from '@/lib/constants/legal';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useConversationStore } from '@/stores/conversationStore';

interface ChatInputProps {
  onSendMessage: (content: string, attachments?: File[], webSearchEnabled?: boolean, ultraThink?: boolean) => void;
  onAttachmentsChange?: (attachments: File[]) => void;
  onUltraThink?: (content: string, attachments?: File[]) => void;
  onUpgrade?: () => void;
  userPlan?: UserPlan;
  isStreaming: boolean;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
  sendDisabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  showAnimatedPlaceholder?: boolean;
  isAutoMode?: boolean;
  selectedModel?: string | null;
  onModelSelect?: (modelId: string | null) => void;
  onModeToggle?: (auto: boolean) => void;
  enableWebSearch?: boolean;
  onWebSearchToggle?: (enabled: boolean) => void;
  onInputChange?: (value: string) => void;
  value?: string;
  showModelSelector?: boolean;
  showWebSearch?: boolean;
  showUltraThink?: boolean;
  modelSelectorRef?: React.RefObject<any>;
  rateLimit?: any;
  userSession?: any;
  onOpenModelSelector?: () => void;
  setSelectedModel?: (model: string | null) => void;
  setIsAutoMode?: (auto: boolean) => void;
  webSearchActive?: boolean;
  setWebSearchActive?: (active: boolean) => void;
  initialPrompt?: string;
  onPromptChange?: (prompt: string) => void;
  pendingAttachments?: File[];
  onClearAttachments?: () => void;
  isAnalyzing?: boolean;
  ultraThinkActive?: boolean;
  setUltraThinkActive?: (active: boolean) => void;
  session?: any;
}

export function ChatInput({ 
  onSendMessage, 
  onAttachmentsChange,
  onUltraThink,
  onUpgrade,
  userPlan = UserPlan.FREE,
  isStreaming,
  isLoading = false,
  disabled = false,
  sendDisabled = false,
  placeholder = "Type a message...",
  maxLength = 4000,
  showAnimatedPlaceholder = false,
  isAutoMode = true,
  selectedModel = null,
  onModelSelect,
  onModeToggle,
  enableWebSearch = false,
  onWebSearchToggle,
  onInputChange,
  value,
  showModelSelector = false,
  showWebSearch = false,
  showUltraThink = false,
  modelSelectorRef,
  rateLimit,
  userSession,
  onOpenModelSelector,
  setSelectedModel,
  setIsAutoMode,
  webSearchActive = false,
  setWebSearchActive
}: ChatInputProps) {
  const [message, setMessage] = useState(value || '');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [isMobileKeyboardOpen, setIsMobileKeyboardOpen] = useState(false);
  const [showMobileTools, setShowMobileTools] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [ultraThinkEnabled, setUltraThinkEnabled] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mobileToolsRef = useRef<HTMLDivElement>(null);
  const { data: session } = useSession();
  const { activeConversationId } = useConversationStore();

  // Check localStorage after mount to avoid hydration mismatch
  useEffect(() => {
    const storedInteraction = localStorage.getItem('hasInteracted');
    if (storedInteraction === 'true') {
      setHasInteracted(true);
    }
  }, []);

  // Check for active conversation
  useEffect(() => {
    if (activeConversationId && !hasInteracted) {
      setHasInteracted(true);
      localStorage.setItem('hasInteracted', 'true');
    }
  }, [activeConversationId, hasInteracted]);

  // Auto-resize for both mobile and desktop
  useEffect(() => {
    if (textareaRef.current) {
      const isDesktop = window.innerWidth >= 768; // md breakpoint (includes tablets/iPads)

      // Auto-resize with constraints for both mobile and desktop
      textareaRef.current.style.height = 'auto';

      const scrollHeight = textareaRef.current.scrollHeight;
      const minHeight = 48; // Minimum height for single line
      const maxHeight = isDesktop ? 160 : 96; // Desktop: 6 lines, Mobile: 4 lines

      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;

      // Enable scrolling if content exceeds max height
      if (scrollHeight > maxHeight) {
        textareaRef.current.style.overflowY = 'auto';
        textareaRef.current.scrollTop = textareaRef.current.scrollHeight;
      } else {
        textareaRef.current.style.overflowY = 'hidden';
      }
    }
  }, [message]);

  // Close mobile tools menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileToolsRef.current && !mobileToolsRef.current.contains(event.target as Node)) {
        setShowMobileTools(false);
      }
    };

    if (showMobileTools) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showMobileTools]);

  // Focus on mount
  useEffect(() => {
    textareaRef.current?.focus();
  }, []);

  // Update message when value prop changes (for applying suggestions)
  useEffect(() => {
    if (value !== undefined && value !== message) {
      setMessage(value);
    }
  }, [value, message]);

  useEffect(() => {
    if (onAttachmentsChange) {
      onAttachmentsChange(attachments);
    }
  }, [attachments, onAttachmentsChange]);

  function handleSubmit() {
    if (message.trim() && !isLoading && !disabled) {
      console.log('[ChatInput] Sending message with attachments:', attachments.length, 'ultraThink:', ultraThinkEnabled);
      onSendMessage(message.trim(), attachments, enableWebSearch, ultraThinkEnabled);
      setMessage('');
      setAttachments([]);
      // Reset ULTRA THINK after use (like web search stays persistent)
      // setUltraThinkEnabled(false); // Keep it enabled for convenience
      textareaRef.current?.focus();
      
      // Mark that user has interacted
      if (!hasInteracted) {
        setHasInteracted(true);
        localStorage.setItem('hasInteracted', 'true');
      }
    }
  }

  // Remove handleUltraThink - now handled by toggle state

  function handleKeyDown(e: KeyboardEvent<HTMLTextAreaElement>) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  }

  function handleFileSelect(files: FileList | null) {
    if (files) {
      console.log('[ChatInput] Files selected:', files.length);
      const newFiles = Array.from(files).filter(file => {
        // Validate file size using centralized validation
        const validation = validateFileSize(file);
        if (!validation.valid) {
          alert(validation.error);
          return false;
        }
        console.log('[ChatInput] File validated:', file.name, file.type, file.size);
        return true;
      });
      
      setAttachments(prev => {
        const updated = [...prev, ...newFiles];
        console.log('[ChatInput] Attachments updated:', updated.length, 'files');
        return updated;
      });
    }
  }

  function removeAttachment(index: number) {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  }

  // Drag and drop handlers
  function handleDrag(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }

  function handleDrop(e: React.DragEvent) {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  }

  // Voice recording (placeholder)
  function toggleRecording() {
    // Show coming soon message
    toast('Voice input is on our roadmap!', {
      icon: '🎤',
      duration: 3000,
      style: {
        borderRadius: '10px',
        background: '#333',
        color: '#fff',
      },
    });
  }

  return (
    <div 
      className={cn(
        "relative p-2 sm:p-3",
        dragActive && "bg-purple-500/5"
      )}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {/* Rate Limit Message */}
      {disabled && placeholder?.includes('reached the free message limit') && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-2 p-3 bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/30 rounded-lg"
        >
          <div className="flex items-center justify-between flex-wrap gap-3">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-white mb-1">
                🎆 You{"'"}ve discovered all {FREE_MESSAGE_LIMITS.ANONYMOUS} free messages!
              </p>
              <p className="text-xs text-gray-300">
                                  Sign in now to unlock your VIP pass: {FREE_MESSAGE_LIMITS.LOGGED_IN} messages/day, no waiting!
              </p>
            </div>
            <Button
              onClick={() => signIn('google', { callbackUrl: '/chat' })}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 whitespace-nowrap"
            >
                                  Sign in for 2x more →
            </Button>
          </div>
        </motion.div>
      )}
      {/* Attachments Preview */}
      <AnimatePresence>
        {attachments.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
          >
            <div className="mb-2 flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-2 bg-gray-900 rounded-lg px-3 py-2 text-sm"
              >
                {file.type.startsWith('image/') ? (
                  <ImageIcon className="w-4 h-4 text-purple-400" />
                ) : (
                  <FileText className="w-4 h-4 text-blue-400" />
                )}
                <span className="max-w-[200px] truncate">{file.name}</span>
                <span className="text-gray-500">
                  {formatFileSize(file.size)}
                </span>
                <button
                  onClick={() => removeAttachment(index)}
                  className="ml-1 hover:text-red-400 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Model Selector */}
      {showModelSelector && (setSelectedModel || onModelSelect) && (setIsAutoMode || onModeToggle) && (
        <div className="mb-2" data-model-selector>
          <ModelSelectorEnhanced
            selectedModel={selectedModel || ''}
            onModelChange={setSelectedModel || onModelSelect || (() => {})}
            isAutoMode={isAutoMode}
            onModeToggle={setIsAutoMode || onModeToggle}
            userPlan={
              userSession?.user?.plan ?
                UserPlan[userSession.user.plan as keyof typeof UserPlan] || UserPlan.FREE :
                UserPlan.FREE
            }
            className="w-full sm:w-auto"
          />
        </div>
      )}

      {/* Modern Unified Input Area - All Devices */}
      <div ref={mobileToolsRef} className="relative bg-purple-800/10 backdrop-blur-sm rounded-2xl border border-purple-400/20 hover:border-green-400/30 shadow-lg transition-all duration-300 overflow-hidden ring-1 ring-green-500/10 hover:ring-green-400/20">
        {/* Mobile Stacked Layout */}
        <div className="md:hidden">
          {/* Input Area */}
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                onInputChange?.(e.target.value);
              }}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={isLoading || disabled}
              maxLength={maxLength}
              className={cn(
                "min-h-[48px] max-h-[96px] resize-none w-full text-sm",
                "bg-transparent border-0 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0",
                "text-gray-100 placeholder:text-gray-400 py-3 px-4 pr-16",
                "transition-all duration-200",
                "leading-6 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent",
                "overflow-y-auto scroll-smooth",
                isLoading && "opacity-50 cursor-not-allowed",
                disabled && placeholder?.includes('reached the free message limit') && "opacity-60 placeholder:text-purple-400/60"
              )}
              rows={1}
              autoComplete="off"
              autoCorrect="on"
              autoCapitalize="sentences"
              spellCheck="true"
              inputMode="text"
              enterKeyHint="send"
            />

            {/* Character Count */}
            {message.length > maxLength * 0.8 && (
              <div className={cn(
                "absolute bottom-2 right-16 text-xs",
                message.length >= maxLength ? "text-red-400" : "text-gray-500"
              )}>
                {message.length}/{maxLength}
              </div>
            )}
          </div>

          {/* Mobile Bottom Toolbar */}
          <div className="flex items-center justify-between px-2 py-2 border-t border-gray-700/30">
            <div className="flex items-center gap-1">
              {/* Tools Menu */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      className={cn(
                        "h-9 w-9 p-0 rounded-full transition-colors",
                        showMobileTools
                          ? "bg-purple-500/20 text-purple-400"
                          : "hover:bg-gray-700/50 text-gray-400 hover:text-white"
                      )}
                      onClick={() => setShowMobileTools(!showMobileTools)}
                    >
                      <motion.div
                        animate={{ rotate: showMobileTools ? 45 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </motion.div>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                    <p className="text-sm">Tools & Options</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Ultra Think Button - Mobile */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => setUltraThinkEnabled(!ultraThinkEnabled)}
                      disabled={isLoading || disabled}
                      className={cn(
                        "h-9 w-9 p-0 rounded-full transition-colors",
                        ultraThinkEnabled
                          ? "bg-purple-500/20 text-purple-400 border border-purple-500/30"
                          : "hover:bg-gray-700/50 text-gray-400 hover:text-white"
                      )}
                    >
                      <Brain className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                    <p className="text-sm">Ultra Think {ultraThinkEnabled ? "ON" : "OFF"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Voice Input */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={toggleRecording}
                      disabled={isLoading || disabled}
                      className={cn(
                        "h-9 w-9 p-0 rounded-full hover:bg-gray-700/50 transition-colors",
                        isRecording ? "text-red-400 bg-red-500/10" : "text-gray-400 hover:text-white"
                      )}
                    >
                      {isRecording ? (
                        <StopCircle className="w-5 h-5" />
                      ) : (
                        <Mic className="w-5 h-5" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                    <p className="text-sm">{isRecording ? "Stop recording" : "Voice input"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Send Button */}
            <Button
              onClick={handleSubmit}
              disabled={!message.trim() || isLoading || disabled || sendDisabled}
              size="sm"
              className={cn(
                "h-9 w-9 p-0 rounded-full",
                "bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "transition-all duration-200 transform hover:scale-105 active:scale-95"
              )}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Mobile Tools Menu */}
          <AnimatePresence>
            {showMobileTools && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="border-t border-gray-700/30 bg-gray-800/40 backdrop-blur-sm overflow-hidden"
              >
                <div className="p-3 space-y-2">
                  <div className="text-xs font-medium text-gray-400 mb-2">Additional Tools</div>
                  <div className="grid grid-cols-1 gap-2">
                    {/* File Upload */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        fileInputRef.current?.click();
                        setShowMobileTools(false);
                      }}
                      disabled={isLoading || disabled}
                      className="h-10 justify-start gap-2 text-sm hover:bg-gray-700/50 text-gray-300"
                    >
                      <Paperclip className="w-4 h-4" />
                      {session?.user ? "Attach Files" : "🔒 Sign In"}
                    </Button>

                    {/* Model Selector Quick Access */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        // Scroll to model selector
                        const modelSelector = document.querySelector('[data-model-selector]');
                        modelSelector?.scrollIntoView({ behavior: 'smooth' });
                        setShowMobileTools(false);
                      }}
                      className="h-10 justify-start gap-2 text-sm hover:bg-gray-700/50 text-gray-300"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                      AI Model Settings
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Modern Desktop Layout - Integrated */}
        <div className="hidden md:block">
          <div className="relative"> {/* Remove fixed height constraint */}
            {/* Input Field - Dynamic Height */}
            <div className="relative">
              <Textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => {
                  setMessage(e.target.value);
                  onInputChange?.(e.target.value);
                }}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={isLoading || disabled}
                maxLength={maxLength}
                className={cn(
                  "min-h-[100px] max-h-[160px] resize-none w-full text-sm",
                  "bg-transparent border-0 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0",
                  "text-gray-100 placeholder:text-gray-400 pt-4 px-4 pb-4", // Normal padding since buttons are now below
                  "transition-all duration-200",
                  "leading-6 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent",
                  "overflow-y-auto scroll-smooth",
                  isLoading && "opacity-50 cursor-not-allowed",
                  disabled && placeholder?.includes('reached the free message limit') && "opacity-60 placeholder:text-purple-400/60"
                )}
                rows={1}
                autoComplete="off"
                autoCorrect="on"
                autoCapitalize="sentences"
                spellCheck="true"
                inputMode="text"
                enterKeyHint="send"
                title="Press Enter to send, Shift+Enter for new line"
              />

              {/* Character Count */}
              {message.length > maxLength * 0.8 && (
                <div className={cn(
                  "absolute top-2 right-4 text-xs bg-gray-800/80 px-2 py-1 rounded",
                  message.length >= maxLength ? "text-red-400" : "text-gray-500"
                )}>
                  {message.length}/{maxLength}
                </div>
              )}
            </div>

            {/* Bottom Icons Bar */}
            <div className="flex items-center justify-between px-2 py-2">
              <div className="flex items-center gap-1">
                {/* Tools Button */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isLoading || disabled}
                        className={cn(
                          "h-8 w-8 p-0 rounded-full transition-colors",
                          "hover:bg-gray-700/50 text-gray-400 hover:text-white"
                        )}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                      <p className="text-sm">Tools</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Web Search Button */}
                {onWebSearchToggle && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onWebSearchToggle(!enableWebSearch)}
                    disabled={isLoading || disabled}
                    className={cn(
                      "h-8 px-2 rounded-full transition-all duration-300 flex items-center gap-1",
                      enableWebSearch
                        ? "bg-green-500/20 text-green-400 hover:bg-green-500/30"
                        : "hover:bg-gray-700/50 text-gray-400 hover:text-gray-300"
                    )}
                  >
                    <Search className="w-3 h-3" />
                    <span className="text-xs font-medium">Search</span>
                  </Button>
                )}

                {/* Ultra Think Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setUltraThinkEnabled(!ultraThinkEnabled)}
                  disabled={isLoading || disabled}
                  className={cn(
                    "h-8 px-2 rounded-full transition-all duration-300 flex items-center gap-1",
                    ultraThinkEnabled
                      ? "bg-purple-500/20 text-purple-400 hover:bg-purple-500/30"
                      : "hover:bg-gray-700/50 text-gray-400 hover:text-gray-300"
                  )}
                >
                  <Brain className="w-3 h-3" />
                  <span className="text-xs font-medium">Think</span>
                </Button>
              </div>

              <div className="flex items-center gap-1">
                {/* Voice Input */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={toggleRecording}
                        disabled={isLoading || disabled}
                        className={cn(
                          "h-8 w-8 p-0 rounded-full hover:bg-gray-700/50 transition-colors",
                          isRecording ? "text-red-400 bg-red-500/10" : "text-gray-400 hover:text-white"
                        )}
                      >
                        {isRecording ? (
                          <StopCircle className="w-4 h-4" />
                        ) : (
                          <Mic className="w-4 h-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-gray-800 border border-gray-700">
                      <p className="text-sm">{isRecording ? "Stop recording" : "Voice input"}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Send Button */}
                <Button
                  onClick={handleSubmit}
                  disabled={!message.trim() || isLoading || disabled || sendDisabled}
                  size="sm"
                  className={cn(
                    "h-8 w-8 p-0 rounded-full",
                    "bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    "transition-all duration-200 transform hover:scale-105 active:scale-95"
                  )}
                >
                  {isLoading ? (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.md,.json,.xml,.html,.js,.ts,.jsx,.tsx,.py,.sql"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Drag Overlay */}
      <AnimatePresence>
        {dragActive && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'absolute',
              inset: 0,
              backgroundColor: 'rgba(168, 85, 247, 0.1)',
              border: '2px dashed rgb(168, 85, 247)',
              borderRadius: '0.5rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <div className="text-purple-400 text-sm font-medium text-center">
              {session?.user ? (
                <>
                  <div>Drop files here</div>
                  <div className="text-xs text-gray-400 mt-1">Max size: {MAX_FILE_SIZE_DISPLAY}</div>
                </>
              ) : (
                <>
                  <div>🔒 Sign in to upload files</div>
                  <div className="text-xs text-gray-400 mt-1">File uploads require login</div>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Terms text only - hide after interaction */}
      {!hasInteracted && (
        <div className="mt-2 text-xs text-gray-400">
          <div className="text-center">
            <span className="hidden sm:inline">
              {LEGAL_TEXT.desktop.prefix}{" "}
              <Link 
                href={LEGAL_LINKS.terms} 
                className="text-blue-400 hover:text-blue-300 underline underline-offset-2 transition-colors"
              >
                {LEGAL_TEXT.desktop.terms}
              </Link>{" "}
              {LEGAL_TEXT.desktop.and}{" "}
              <Link 
                href={LEGAL_LINKS.privacy} 
                className="text-blue-400 hover:text-blue-300 underline underline-offset-2 transition-colors"
              >
                {LEGAL_TEXT.desktop.privacy}
              </Link>
            </span>
            
            {/* Mobile version - Ultra compact */}
            <span className="sm:hidden">
              {LEGAL_TEXT.mobile.prefix}{" "}
              <Link 
                href={LEGAL_LINKS.terms} 
                className="text-blue-400 underline"
              >
                {LEGAL_TEXT.mobile.terms}
              </Link>{" "}
              {LEGAL_TEXT.mobile.and}{" "}
              <Link 
                href={LEGAL_LINKS.privacy} 
                className="text-blue-400 underline"
              >
                {LEGAL_TEXT.mobile.privacy}
              </Link>
            </span>
          </div>
        </div>
      )}
    </div>
  );
}