'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, Globe } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CitationSource {
  title: string;
  url: string;
  domain: string;
  snippet?: string;
}

interface CitationLinkProps {
  citationNumber: string;
  sources: CitationSource[];
  className?: string;
}

/**
 * CitationLink Component - Converts [1], [2] citations into clickable elements
 * Shows tooltip with source info on hover and opens source on click
 */
export function CitationLink({ citationNumber, sources, className }: CitationLinkProps) {
  const [showTooltip, setShowTooltip] = useState(false);
  
  // Find the source for this citation number (1-indexed)
  const sourceIndex = parseInt(citationNumber) - 1;
  const source = sources[sourceIndex];
  
  if (!source) {
    // If no source found, render as plain text
    return <span className="text-gray-400">[{citationNumber}]</span>;
  }
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    window.open(source.url, '_blank', 'noopener,noreferrer');
  };
  
  return (
    <span 
      className="relative inline-block"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <button
        onClick={handleClick}
        className={cn(
          "inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-xs font-mono",
          "bg-blue-500/20 text-blue-300 border border-blue-500/30",
          "hover:bg-blue-500/30 hover:text-blue-200 hover:border-blue-500/50",
          "transition-all duration-200 cursor-pointer",
          "focus:outline-none focus:ring-2 focus:ring-blue-500/50",
          className
        )}
      >
        <span>[{citationNumber}]</span>
        <ExternalLink className="w-2.5 h-2.5 opacity-60" />
      </button>
      
      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
          >
            <div className="bg-gray-900/95 backdrop-blur border border-gray-700 rounded-lg p-3 shadow-lg max-w-xs">
              <div className="flex items-start gap-2">
                <div className="p-1 rounded bg-blue-500/20">
                  <Globe className="w-3 h-3 text-blue-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-xs font-medium text-gray-200 truncate">
                    {source.title}
                  </div>
                  <div className="text-xs text-gray-400 mt-0.5">
                    {source.domain}
                  </div>
                  {source.snippet && (
                    <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {source.snippet}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Tooltip arrow */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-700" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </span>
  );
}

/**
 * Custom ReactMarkdown component to transform text and replace citations
 */
export function CitationText({ children, sources }: { children: React.ReactNode; sources: CitationSource[] }) {
  if (typeof children !== 'string') {
    return <>{children}</>;
  }
  
  // Replace [1], [2], etc. with CitationLink components
  const parts = children.split(/(\[\d+\])/g);
  
  return (
    <>
      {parts.map((part, index) => {
        const citationMatch = part.match(/^\[(\d+)\]$/);
        if (citationMatch) {
          const citationNumber = citationMatch[1];
          return (
            <CitationLink
              key={`citation-${citationNumber}-${index}`}
              citationNumber={citationNumber}
              sources={sources}
            />
          );
        }
        return part;
      })}
    </>
  );
}