'use client';

import { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Maximize2, X, Download, Pause, Play, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useProgressiveImage } from '@/hooks/useProgressiveImage';
import { ImageChunk } from '@/lib/image-streaming';

interface StreamingImageProps {
  /**
   * Unique identifier for the image stream
   */
  streamId: string;
  
  /**
   * Expected total size of the image in bytes (optional)
   */
  expectedSize?: number;
  
  /**
   * Image chunks to process
   */
  chunks?: ImageChunk[];
  
  /**
   * Alt text for the image
   */
  alt?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Enable progressive JPEG rendering
   */
  enableProgressive?: boolean;
  
  /**
   * Callback when image loading starts
   */
  onLoadStart?: () => void;
  
  /**
   * Callback when image loading completes
   */
  onLoadComplete?: (imageUrl: string) => void;
  
  /**
   * Callback when image loading fails
   */
  onError?: (error: Error) => void;
  
  /**
   * Callback for progress updates
   */
  onProgress?: (progress: number) => void;
  
  /**
   * Enable blur-to-sharp transition
   */
  enableBlurTransition?: boolean;
  
  /**
   * Placeholder color while loading
   */
  placeholderColor?: string;
  
  /**
   * Custom placeholder component
   */
  placeholder?: React.ReactNode;
}

export function StreamingImage({
  streamId,
  expectedSize,
  chunks = [],
  alt = 'Streaming image',
  className,
  enableProgressive = true,
  onLoadStart,
  onLoadComplete,
  onError,
  onProgress,
  enableBlurTransition = true,
  placeholderColor = '#1a1a1a',
  placeholder
}: StreamingImageProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [showControls, setShowControls] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Use the progressive image hook
  const {
    imageUrl,
    progress,
    isLoading,
    isComplete,
    error,
    retryChunk,
    pauseStreaming,
    resumeStreaming,
    reset
  } = useProgressiveImage({
    streamId,
    chunks,
    expectedSize,
    enableProgressive,
    onProgress,
    onError,
    onLoadStart,
    onLoadComplete
  });

  // Handle loading state transitions
  useEffect(() => {
    if (isLoading && !isPaused) {
      setShowControls(true);
    }
  }, [isLoading, isPaused]);

  // Handle pause/resume
  const handlePauseResume = () => {
    if (isPaused) {
      resumeStreaming();
      setIsPaused(false);
    } else {
      pauseStreaming();
      setIsPaused(true);
    }
  };

  // Handle retry
  const handleRetry = () => {
    setRetryCount((prev: number) => prev + 1);
    reset();
  };

  // Handle download
  const handleDownload = () => {
    if (!imageUrl) return;
    
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `${streamId}-image-${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Calculate blur intensity based on progress
  const getBlurIntensity = () => {
    if (!enableBlurTransition || isComplete) return 0;
    if (progress < 0.1) return 20;
    if (progress < 0.3) return 15;
    if (progress < 0.5) return 10;
    if (progress < 0.7) return 5;
    if (progress < 0.9) return 2;
    return 0;
  };

  // Loading placeholder component
  const renderPlaceholder = () => {
    if (placeholder) return placeholder;
    
    return (
      <div 
        className={cn(
          "relative w-full h-48 rounded-lg overflow-hidden",
          "bg-gradient-to-br from-gray-800 to-gray-900",
          "flex items-center justify-center",
          "animate-pulse"
        )}
        style={{ backgroundColor: placeholderColor }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-600/20 to-transparent animate-pulse" />
        <div className="text-center space-y-2">
          <div className="w-12 h-12 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
          <p className="text-sm text-gray-400">Loading image...</p>
          {expectedSize && (
            <p className="text-xs text-gray-500">
              {Math.round(progress * 100)}% ({Math.round(progress * expectedSize / 1024)}KB)
            </p>
          )}
        </div>
      </div>
    );
  };

  // Error state
  if (error) {
    return (
      <div className={cn(
        "relative w-full h-48 rounded-lg overflow-hidden",
        "bg-red-950/50 border border-red-800/50",
        "flex items-center justify-center",
        className
      )}>
        <div className="text-center space-y-2">
          <div className="w-12 h-12 rounded-full bg-red-900/50 flex items-center justify-center">
            <X className="w-6 h-6 text-red-400" />
          </div>
          <p className="text-sm text-red-400">Failed to load image</p>
          <p className="text-xs text-red-500">{error.message}</p>
          <Button
            size="sm"
            variant="outline"
            onClick={handleRetry}
            className="mt-2"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry ({retryCount})
          </Button>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading && !imageUrl) {
    return (
      <div className={cn("relative group", className)} ref={containerRef}>
        {renderPlaceholder()}
        
        {/* Progress bar */}
        <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/50 to-transparent">
          <Progress value={progress * 100} className="w-full h-2" />
        </div>
        
        {/* Loading controls */}
        {showControls && (
          <div className="absolute top-2 right-2 flex gap-2">
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70"
              onClick={handlePauseResume}
            >
              {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
            </Button>
          </div>
        )}
      </div>
    );
  }

  // Main image display
  return (
    <>
      <div className={cn(
        "relative inline-block max-w-full my-2 group",
        className
      )} ref={containerRef}>
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          ref={imageRef}
          src={imageUrl || ''}
          alt={alt}
          className={cn(
            "rounded-lg max-w-full h-auto",
            "max-h-[400px] object-contain",
            "cursor-pointer transition-all duration-300",
            "hover:shadow-lg hover:shadow-purple-500/20",
            enableBlurTransition && !isComplete && "transition-filter duration-1000"
          )}
          style={{
            filter: enableBlurTransition ? `blur(${getBlurIntensity()}px)` : undefined
          }}
          onClick={() => setIsExpanded(true)}
        />
        
        {/* Progress overlay for incomplete images */}
        {!isComplete && imageUrl && (
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/50 to-transparent">
            <Progress value={progress * 100} className="w-full h-2" />
            <div className="text-xs text-white/70 mt-1">
              {Math.round(progress * 100)}% loaded
            </div>
          </div>
        )}
        
        {/* Overlay controls */}
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
          <Button
            size="sm"
            variant="secondary"
            className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70"
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(true);
            }}
          >
            <Maximize2 className="w-4 h-4" />
          </Button>
          
          {!isComplete && (
            <Button
              size="sm"
              variant="secondary"
              className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70"
              onClick={(e) => {
                e.stopPropagation();
                handlePauseResume();
              }}
            >
              {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
            </Button>
          )}
          
          <Button
            size="sm"
            variant="secondary"
            className="h-8 w-8 p-0 bg-black/50 hover:bg-black/70"
            onClick={(e) => {
              e.stopPropagation();
              handleDownload();
            }}
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Expanded modal */}
      {isExpanded && imageUrl && (
        <div 
          className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
          onClick={() => setIsExpanded(false)}
        >
          <div className="relative max-w-[90vw] max-h-[90vh]">
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img
              src={imageUrl || ''}
              alt={alt}
              className={cn(
                "max-w-full max-h-full object-contain rounded-lg",
                enableBlurTransition && !isComplete && "transition-filter duration-1000"
              )}
              style={{
                filter: enableBlurTransition ? `blur(${getBlurIntensity()}px)` : undefined
              }}
              onClick={(e) => e.stopPropagation()}
            />
            
            {/* Progress overlay in modal */}
            {!isComplete && (
              <div className="absolute bottom-4 left-4 right-16 bg-black/75 rounded-lg p-3">
                <Progress value={progress * 100} className="w-full h-2 mb-2" />
                <div className="text-xs text-white/70 flex justify-between">
                  <span>{Math.round(progress * 100)}% loaded</span>
                  {expectedSize && (
                    <span>{Math.round(progress * expectedSize / 1024)}KB / {Math.round(expectedSize / 1024)}KB</span>
                  )}
                </div>
              </div>
            )}
            
            {/* Close button */}
            <Button
              size="sm"
              variant="secondary"
              className="absolute top-4 right-4 h-10 w-10 p-0 bg-black/50 hover:bg-black/70"
              onClick={() => setIsExpanded(false)}
            >
              <X className="w-5 h-5" />
            </Button>
            
            {/* Download button */}
            <Button
              size="sm"
              variant="secondary"
              className="absolute top-4 right-16 h-10 w-10 p-0 bg-black/50 hover:bg-black/70"
              onClick={(e) => {
                e.stopPropagation();
                handleDownload();
              }}
            >
              <Download className="w-5 h-5" />
            </Button>
            
            {/* Pause/Resume button */}
            {!isComplete && (
              <Button
                size="sm"
                variant="secondary"
                className="absolute top-4 right-28 h-10 w-10 p-0 bg-black/50 hover:bg-black/70"
                onClick={(e) => {
                  e.stopPropagation();
                  handlePauseResume();
                }}
              >
                {isPaused ? <Play className="w-5 h-5" /> : <Pause className="w-5 h-5" />}
              </Button>
            )}
          </div>
        </div>
      )}
    </>
  );
}