'use client';

import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface SmoothStreamingContainerProps {
  children: React.ReactNode;
  isStreaming: boolean;
  className?: string;
}

/**
 * Modern container that prevents layout shifts during streaming
 * Uses ResizeObserver to track content height and maintain stability
 */
export function SmoothStreamingContainer({ 
  children, 
  isStreaming,
  className 
}: SmoothStreamingContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const minHeightRef = useRef(0);
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const height = entry.contentRect.height;
        
        // During streaming, only allow height to increase
        if (isStreaming && height > minHeightRef.current) {
          minHeightRef.current = height;
          // Use requestAnimationFrame for smoother updates
          requestAnimationFrame(() => {
            if (containerRef.current) {
              containerRef.current.style.minHeight = `${height}px`;
            }
          });
        }
      }
    });
    
    resizeObserver.observe(containerRef.current);
    
    return () => {
      resizeObserver.disconnect();
    };
  }, [isStreaming]);
  
  // Reset min-height after streaming completes
  useEffect(() => {
    if (!isStreaming && containerRef.current) {
      // Smooth transition to natural height
      const timer = setTimeout(() => {
        if (containerRef.current) {
          // Use requestAnimationFrame for smooth transition
          requestAnimationFrame(() => {
            if (containerRef.current) {
              containerRef.current.style.minHeight = '';
              minHeightRef.current = 0;
            }
          });
        }
      }, 500); // Increased delay to match typing animation completion
      
      return () => clearTimeout(timer);
    }
  }, [isStreaming]);

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative",
        "transition-[min-height] duration-500 ease-out",
        className
      )}
      style={{
        // Prevent horizontal overflow during streaming
        overflowX: 'hidden',
        // Maintain text baseline
        lineHeight: 'inherit',
        // Ensure smooth rendering
        willChange: isStreaming ? 'min-height' : 'auto',
      }}
    >
      {children}
    </div>
  );
}