'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { CodeBlock, InlineCode } from './code-block';
import { ImageDisplay } from './image-display';
import { cn } from '@/lib/utils';
import { dynamicSpeedCalculator, type StreamingContext } from '@/lib/dynamic-speed-calculator';

interface UltraSmoothStreamingV2Props {
  content: string;
  isStreaming: boolean;
  onStreamComplete?: () => void;
  className?: string;
  speed?: 'normal' | 'fast' | 'slow';
  dynamicSpeed?: boolean;
  bufferLength?: number;
}

// Enhanced timing configurations with anti-squash settings
const TIMING_CONFIG = {
  normal: { 
    charsPerFrame: 3,
    smoothness: 0.85,
    initialDelay: 50,      // Delay before starting to prevent squash
    minHeight: '2.5rem',   // Larger initial height
    dynamicSpeedEnabled: true,
  },
  fast: { 
    charsPerFrame: 5,
    smoothness: 0.7,
    initialDelay: 30,
    minHeight: '2.5rem',
    dynamicSpeedEnabled: true,
  },
  slow: { 
    charsPerFrame: 2,
    smoothness: 0.95,
    initialDelay: 70,
    minHeight: '2.5rem',
    dynamicSpeedEnabled: true,
  },
};

// Minimal cursor with zero layout impact
const StreamingCursor = React.memo(({ show }: { show: boolean }) => {
  if (!show) return null;
  
  return (
    <span 
      className="inline-block w-[2px] h-[1em] bg-purple-500 ml-0.5 align-middle"
      style={{ 
        animation: 'ultra-smooth-pulse 1s ease-in-out infinite',
        verticalAlign: '-0.1em',
      }}
    />
  );
});

StreamingCursor.displayName = 'StreamingCursor';

export function UltraSmoothStreamingV2({
  content,
  isStreaming,
  onStreamComplete,
  className,
  speed = 'normal',
  dynamicSpeed = true,
  bufferLength = 0,
}: UltraSmoothStreamingV2Props) {
  const [displayedContent, setDisplayedContent] = useState('');
  const [showCursor, setShowCursor] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Performance refs
  const currentIndexRef = useRef(0);
  const frameIdRef = useRef<number | undefined>(undefined);
  const lastFrameTimeRef = useRef(0);
  const completedRef = useRef(false);
  const initTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  
  // Dynamic speed refs
  const lastContentLengthRef = useRef(0);
  const chunksProcessedRef = useRef(0);
  const totalChunkSizeRef = useRef(0);
  const currentCharsPerFrameRef = useRef(TIMING_CONFIG[speed].charsPerFrame);
  
  const config = TIMING_CONFIG[speed];

  // Calculate dynamic speed based on new content chunks
  const updateDynamicSpeed = useCallback(() => {
    if (!dynamicSpeed || !config.dynamicSpeedEnabled) {
      currentCharsPerFrameRef.current = config.charsPerFrame;
      return;
    }

    const newContentLength = content.length;
    const chunkSize = newContentLength - lastContentLengthRef.current;
    
    if (chunkSize > 0) {
      // Track chunk statistics
      chunksProcessedRef.current += 1;
      totalChunkSizeRef.current += chunkSize;
      
      // Get the new chunk content for analysis
      const newChunk = content.substring(lastContentLengthRef.current, newContentLength);
      
      // Create streaming context
      const streamingContext: StreamingContext = {
        currentBufferLength: bufferLength,
        totalChunksProcessed: chunksProcessedRef.current,
        averageChunkSize: totalChunkSizeRef.current / chunksProcessedRef.current,
        isCodeBlock: /```[\s\S]*?```/.test(newChunk),
        isThinking: /<thinking>[\s\S]*?<\/thinking>/.test(newChunk),
        userSpeedPreference: speed,
      };
      
      // Calculate adaptive chars per frame
      const adaptiveCharsPerFrame = dynamicSpeedCalculator.calculateAdaptiveCharsPerFrame(
        newChunk,
        streamingContext
      );
      
      currentCharsPerFrameRef.current = adaptiveCharsPerFrame;
      lastContentLengthRef.current = newContentLength;
    }
  }, [content, dynamicSpeed, config, bufferLength, speed]);

  // Memoize markdown components
  const markdownComponents = useMemo(() => ({
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      const filename = props['data-filename'] || undefined;
      
      return !inline && match ? (
        <CodeBlock
          language={match[1]}
          value={String(children).replace(/\n$/, '')}
          filename={filename}
          showLineNumbers={true}
        />
      ) : (
        <InlineCode>{children}</InlineCode>
      );
    },
    // Stable paragraph rendering
    p: ({ children }: any) => (
      <p className="mb-2 last:mb-0" style={{ minHeight: '1.5em' }}>
        {children}
      </p>
    ),
    // Stable list rendering
    ul: ({ children }: any) => (
      <ul className="mb-2 last:mb-0 list-disc pl-5" style={{ minHeight: '1.5em' }}>
        {children}
      </ul>
    ),
    ol: ({ children }: any) => (
      <ol className="mb-2 last:mb-0 list-decimal pl-5" style={{ minHeight: '1.5em' }}>
        {children}
      </ol>
    ),
    // Image handling
    img({ node, src, alt, ...props }: any) {
      // Handle base64 images from markdown
      if (src && src.startsWith('data:image')) {
        return (
          <ImageDisplay
            src={src}
            alt={alt || 'Image'}
            className="my-2"
          />
        );
      }
      // Handle regular image URLs
      return (
        <ImageDisplay
          src={src || ''}
          alt={alt || 'Image'}
          className="my-2"
        />
      );
    },
  }), []);

  // High-performance animation with anti-squash and dynamic speed
  const animate = useCallback(() => {
    const now = performance.now();
    
    if (!lastFrameTimeRef.current) {
      lastFrameTimeRef.current = now;
    }
    
    const deltaTime = now - lastFrameTimeRef.current;
    
    // 60fps target (16.67ms)
    if (deltaTime >= 16) {
      lastFrameTimeRef.current = now;
      
      // Use dynamic chars per frame if enabled, fallback to config
      const charsPerFrame = dynamicSpeed && config.dynamicSpeedEnabled 
        ? currentCharsPerFrameRef.current 
        : config.charsPerFrame;
      
      const targetIndex = currentIndexRef.current + charsPerFrame;
      const actualIndex = Math.min(targetIndex, content.length);
      
      if (actualIndex > currentIndexRef.current) {
        currentIndexRef.current = actualIndex;
        
        // Batch DOM updates
        requestAnimationFrame(() => {
          setDisplayedContent(content.substring(0, actualIndex));
        });
        
        if (actualIndex >= content.length && !completedRef.current) {
          completedRef.current = true;
          setShowCursor(false);
          onStreamComplete?.();
        }
      }
    }
    
    if (currentIndexRef.current < content.length && isStreaming) {
      frameIdRef.current = requestAnimationFrame(animate);
    }
  }, [content, config.charsPerFrame, config.dynamicSpeedEnabled, dynamicSpeed, isStreaming, onStreamComplete]);

  // Handle streaming with initialization delay
  useEffect(() => {
    if (!isStreaming) {
      // Show all content immediately when not streaming
      setDisplayedContent(content);
      setShowCursor(false);
      currentIndexRef.current = content.length;
      setIsInitialized(true);
      
      if (!completedRef.current && content.length > 0) {
        completedRef.current = true;
        onStreamComplete?.();
      }
      return;
    }
    
    // Reset if content is shorter (new message)
    if (content.length < currentIndexRef.current) {
      currentIndexRef.current = 0;
      setDisplayedContent('');
      completedRef.current = false;
      setIsInitialized(false);
      // Reset dynamic speed tracking
      lastContentLengthRef.current = 0;
      chunksProcessedRef.current = 0;
      totalChunkSizeRef.current = 0;
      currentCharsPerFrameRef.current = config.charsPerFrame;
      dynamicSpeedCalculator.resetHistory();
    }
    
    // Update dynamic speed when content changes
    updateDynamicSpeed();
    
    // Initialize with delay to prevent squashing
    if (!isInitialized && content.length > 0) {
      initTimeoutRef.current = setTimeout(() => {
        setIsInitialized(true);
        setShowCursor(true);
        frameIdRef.current = requestAnimationFrame(animate);
      }, config.initialDelay);
    } else if (isInitialized) {
      // Continue animation if already initialized
      if (!frameIdRef.current) {
        frameIdRef.current = requestAnimationFrame(animate);
      }
    }
    
    return () => {
      if (frameIdRef.current) {
        cancelAnimationFrame(frameIdRef.current);
      }
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
      }
    };
  }, [content, isStreaming, animate, config.initialDelay, isInitialized, onStreamComplete, updateDynamicSpeed, config.charsPerFrame]);

  // Hide cursor after completion
  useEffect(() => {
    if (!isStreaming && showCursor) {
      const timer = setTimeout(() => setShowCursor(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isStreaming, showCursor]);

  return (
    <>
      <style jsx global>{`
        @keyframes ultra-smooth-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
        
        /* Anti-squash styles */
        .streaming-message-container {
          min-height: ${config.minHeight};
          transition: min-height 0.2s ease-out;
        }
        
        .streaming-message-container.initialized {
          min-height: auto;
        }
        
        /* Prevent text reflow during streaming */
        .streaming-message-container * {
          line-height: 1.6;
          letter-spacing: normal;
        }
        
        /* Stable code block rendering */
        .streaming-message-container pre {
          min-height: 2.5rem;
          transition: height 0.2s ease-out;
        }
      `}</style>
      
      <div 
        className={cn(
          "prose prose-invert max-w-none streaming-message-container text-gray-200",
          isInitialized && "initialized",
          isStreaming && "streaming",
          className
        )}
        style={{
          // Ensure minimum height to prevent squashing
          minHeight: isInitialized ? 'auto' : config.minHeight,
          // Smooth height transitions
          transition: 'min-height 0.2s ease-out',
          // Prevent layout recalculation
          contain: 'layout',
          // Optimize for content changes
          willChange: isStreaming ? 'contents' : 'auto',
        }}
      >
        {/* Pre-allocated space for smooth start */}
        {!isInitialized && isStreaming && (
          <div className="h-[2.5rem] flex items-center">
            <span className="inline-block w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
          </div>
        )}
        
        {/* Main content */}
        {(isInitialized || !isStreaming) && (
          <div className="relative">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={markdownComponents}
            >
              {displayedContent}
            </ReactMarkdown>
            <StreamingCursor show={showCursor && isStreaming} />
          </div>
        )}
      </div>
    </>
  );
}