'use client';

import { useState, useEffect, useRef } from 'react';
import { Message, MessageRole } from '@/types';
import { MessageItem } from './message-item';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SmoothStreamingMessageProps {
  isLoading: boolean;
  isProcessingAttachments: boolean;
  currentAssistantMessage: string;
  streamingMessage: Message | null;
  webSearchActive?: boolean;
  webSearchQuery?: string;
  webSearchQueries?: string[];
  webSearchResults?: number;
}

export function SmoothStreamingMessage({
  isLoading,
  isProcessingAttachments,
  currentAssistantMessage,
  streamingMessage,
  webSearchActive,
  webSearchQuery,
  webSearchQueries,
  webSearchResults
}: SmoothStreamingMessageProps) {
  const [displayMessage, setDisplayMessage] = useState<Message | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const messageIdRef = useRef<string>('assistant-loading');
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (streamingMessage) {
      // Use streaming message with smooth transition
      setDisplayMessage(streamingMessage);
      setIsVisible(true);
    } else if (isLoading && currentAssistantMessage) {
      // Create placeholder message that matches streaming format
      const placeholderMessage: Message = {
        id: messageIdRef.current,
        role: MessageRole.ASSISTANT,
        content: currentAssistantMessage,
        createdAt: new Date(),
        conversationId: '',
        updatedAt: new Date(),
        streaming: true
      };
      setDisplayMessage(placeholderMessage);
      setIsVisible(true);
    } else {
      // Hide gracefully
      setIsVisible(false);
      // Clear after animation completes
      setTimeout(() => setDisplayMessage(null), 200);
    }
  }, [streamingMessage, isLoading, currentAssistantMessage]);

  // Auto-scroll when content updates
  useEffect(() => {
    if (displayMessage?.content && containerRef.current) {
      requestAnimationFrame(() => {
        containerRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest',
          inline: 'nearest'
        });
      });
    }
  }, [displayMessage?.content]);

  if (!displayMessage || !isVisible) {
    return null;
  }

  return (
    <AnimatePresence mode="wait">
      <motion.div
        ref={containerRef}
        key={streamingMessage ? streamingMessage.id : 'loading'}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -5 }}
        transition={{ 
          duration: 0.2,
          ease: 'easeOut'
        }}
        className="space-y-4 w-full"
      >
        {/* Processing indicators */}
        {isProcessingAttachments && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gray-700 p-3 rounded-md flex items-center"
          >
            <div className="w-4 h-4 mr-3 animate-spin border-2 border-gray-400 border-t-transparent rounded-full" />
            <span className="text-sm text-gray-500">
              Processing attachments...
            </span>
          </motion.div>
        )}


        {/* Message content with smooth streaming */}
        <motion.div
          layout
          className={cn(
            "transition-all duration-200 ease-out",
            !streamingMessage && "animate-pulse" // Subtle pulse for loading
          )}
        >
          <MessageItem
            message={displayMessage}
            isStreaming={!streamingMessage || displayMessage.streaming}
            webSearchActive={webSearchActive}
            webSearchQuery={webSearchQuery}
            webSearchQueries={webSearchQueries}
            webSearchResults={webSearchResults}
          />
        </motion.div>

        {/* Typing indicator for active streaming */}
        {!streamingMessage && currentAssistantMessage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-1 px-4 py-1"
          >
            <span className="text-xs text-gray-500">AI is thinking</span>
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-1.5 h-1.5 bg-blue-400 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}