'use client';

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { CodeBlock, InlineCode } from './code-block';
import { cn } from '@/lib/utils';

interface TypingAnimationStreamingProps {
  content: string;
  isStreaming: boolean;
  onStreamComplete?: () => void;
  className?: string;
  characterDelay?: number; // Delay between characters in ms (default: 35ms)
  cursorBlinkSpeed?: number; // Cursor blink speed in ms (default: 530ms)
}

interface CharacterBuffer {
  chunks: string[];
  currentChunkIndex: number;
  currentCharIndex: number;
}

// CSS for typewriter animation
const typewriterStyles = `
  @keyframes typewriter {
    from {
      opacity: 0;
      transform: translateY(0.1em);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes cursor-blink {
    0%, 49% {
      opacity: 1;
    }
    50%, 100% {
      opacity: 0;
    }
  }

  @keyframes cursor-fade-out {
    from {
      opacity: 1;
      transform: scaleX(1);
    }
    to {
      opacity: 0;
      transform: scaleX(0);
    }
  }

  .typewriter-char {
    animation: typewriter 0.1s ease-out forwards;
    opacity: 0;
  }

  .typewriter-cursor {
    display: inline-block;
    width: 2px;
    height: 1.1em;
    background-color: rgb(168, 85, 247);
    margin-left: 2px;
    vertical-align: baseline;
    animation: cursor-blink var(--blink-speed, 530ms) step-end infinite;
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
    transform-origin: left center;
    position: relative;
    top: -0.05em;
  }

  .typewriter-cursor.fade-out {
    animation: cursor-fade-out 0.3s ease-out forwards;
  }

  .typewriter-container {
    position: relative;
    transition: min-height 0.3s ease-out;
  }

  .typewriter-content {
    position: relative;
    display: block;
    width: 100%;
  }
  
  /* Ensure cursor stays inline with last text */
  .typewriter-content > :last-child {
    display: inline;
  }
  
  .typewriter-content > :last-child:not(pre) {
    display: inline;
  }
  
  /* Keep paragraphs block except for the last one during streaming */
  .prose p {
    display: block;
  }
  
  .prose p:last-child {
    display: inline;
  }
`;

// Custom hook for buffered character streaming
function useBufferedCharacterStream(
  content: string,
  isStreaming: boolean,
  characterDelay: number,
  onComplete?: () => void
) {
  const [displayedContent, setDisplayedContent] = useState('');
  const [showCursor, setShowCursor] = useState(false);
  const [cursorFading, setCursorFading] = useState(false);
  
  const bufferRef = useRef<CharacterBuffer>({
    chunks: [],
    currentChunkIndex: 0,
    currentCharIndex: 0,
  });
  
  const lastContentRef = useRef('');
  const animationFrameRef = useRef<number | undefined>(undefined);
  const lastUpdateTimeRef = useRef<number>(0);
  const completedRef = useRef(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | undefined>(undefined);

  // Buffer incoming content chunks
  useEffect(() => {
    if (content !== lastContentRef.current) {
      const newContent = content.slice(lastContentRef.current.length);
      if (newContent) {
        bufferRef.current.chunks.push(newContent);
      }
      lastContentRef.current = content;
    }
  }, [content]);

  // Character release animation
  const releaseCharacter = useCallback((timestamp: number) => {
    if (!lastUpdateTimeRef.current) {
      lastUpdateTimeRef.current = timestamp;
    }

    const elapsed = timestamp - lastUpdateTimeRef.current;

    if (elapsed >= characterDelay) {
      const buffer = bufferRef.current;
      
      if (buffer.currentChunkIndex < buffer.chunks.length) {
        const currentChunk = buffer.chunks[buffer.currentChunkIndex];
        
        if (buffer.currentCharIndex < currentChunk.length) {
          // Release one character
          const newChar = currentChunk[buffer.currentCharIndex];
          setDisplayedContent(prev => prev + newChar);
          buffer.currentCharIndex++;
          
          // Move to next chunk if current is exhausted
          if (buffer.currentCharIndex >= currentChunk.length) {
            buffer.currentChunkIndex++;
            buffer.currentCharIndex = 0;
          }
        }
        
        lastUpdateTimeRef.current = timestamp;
      }
      
      // Check if we've displayed all content
      if (displayedContent.length + 1 >= content.length && !completedRef.current) {
        completedRef.current = true;
        setCursorFading(true);
        
        // Delay hiding cursor and clearing min-height to allow smooth transition
        setTimeout(() => {
          setShowCursor(false);
          if (containerRef.current) {
            containerRef.current.style.minHeight = '';
          }
          onComplete?.();
        }, 300);
      }
    }

    // Continue animation if streaming and have more content
    if (isStreaming && displayedContent.length < content.length) {
      animationFrameRef.current = requestAnimationFrame(releaseCharacter);
    }
  }, [characterDelay, content.length, displayedContent.length, isStreaming, onComplete]);

  // Start/stop animation based on streaming state
  useEffect(() => {
    if (isStreaming) {
      setShowCursor(true);
      if (displayedContent.length < content.length) {
        animationFrameRef.current = requestAnimationFrame(releaseCharacter);
      }
    } else {
      // Show all content immediately when not streaming
      setDisplayedContent(content);
      setShowCursor(false);
      bufferRef.current = {
        chunks: [],
        currentChunkIndex: 0,
        currentCharIndex: 0,
      };
      if (!completedRef.current && content.length > 0) {
        completedRef.current = true;
        onComplete?.();
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isStreaming, content, releaseCharacter, displayedContent.length, onComplete]);

  // Clear min-height when not streaming
  useEffect(() => {
    if (!isStreaming && containerRef.current) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.style.minHeight = '';
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isStreaming]);

  // Set up ResizeObserver to prevent layout shifts
  useEffect(() => {
    if (containerRef.current && !resizeObserverRef.current) {
      resizeObserverRef.current = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // Maintain minimum height to prevent collapse during streaming
          if (isStreaming) {
            const { height } = entry.contentRect;
            const currentMinHeight = parseFloat((entry.target as HTMLElement).style.minHeight) || 0;
            // Only increase min-height, never decrease during streaming
            if (height > currentMinHeight) {
              (entry.target as HTMLElement).style.minHeight = `${height}px`;
            }
          }
        }
      });
      
      resizeObserverRef.current.observe(containerRef.current);
    }

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [isStreaming]);

  // Reset when content changes significantly
  useEffect(() => {
    if (content.length < displayedContent.length) {
      setDisplayedContent('');
      bufferRef.current = {
        chunks: [],
        currentChunkIndex: 0,
        currentCharIndex: 0,
      };
      lastContentRef.current = '';
      completedRef.current = false;
      lastUpdateTimeRef.current = 0;
    }
  }, [content.length, displayedContent.length]);

  return { displayedContent, showCursor, cursorFading, containerRef };
}

// Component for rendering text with character-by-character animation
function AnimatedText({ text, baseDelay = 0 }: { text: string; baseDelay?: number }) {
  return (
    <>
      {text.split('').map((char, index) => (
        <span
          key={`${index}-${char}`}
          className="typewriter-char"
          style={{
            animationDelay: `${baseDelay + index * 20}ms`,
          }}
        >
          {char}
        </span>
      ))}
    </>
  );
}

export function TypingAnimationStreaming({
  content,
  isStreaming,
  onStreamComplete,
  className,
  characterDelay = 35,
  cursorBlinkSpeed = 530,
}: TypingAnimationStreamingProps) {
  const { displayedContent, showCursor, cursorFading, containerRef } = useBufferedCharacterStream(
    content,
    isStreaming,
    characterDelay,
    onStreamComplete
  );

  // Memoize markdown components for performance
  const markdownComponents = useMemo(() => ({
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '');
      const filename = props['data-filename'] || undefined;
      
      return !inline && match ? (
        <CodeBlock
          language={match[1]}
          value={String(children).replace(/\n$/, '')}
          filename={filename}
          showLineNumbers={true}
        />
      ) : (
        <InlineCode>{children}</InlineCode>
      );
    },
    // Custom paragraph component to handle spacing and inline cursor
    p: ({ children }: any) => {
      // If this is the last paragraph and we're streaming, wrap in span to keep cursor inline
      return <p className="mb-3 last:mb-0 inline-block">{children}</p>;
    },
    // Custom list components
    ul: ({ children }: any) => (
      <ul className="list-disc pl-6 mb-3 last:mb-0">{children}</ul>
    ),
    ol: ({ children }: any) => (
      <ol className="list-decimal pl-6 mb-3 last:mb-0">{children}</ol>
    ),
    li: ({ children }: any) => (
      <li className="mb-1">{children}</li>
    ),
  }), []);

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: typewriterStyles }} />
      <div 
        ref={containerRef}
        className={cn(
          "typewriter-container prose prose-invert max-w-none",
          "text-gray-200 leading-relaxed",
          className
        )}
      >
        <div className="typewriter-content">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={markdownComponents}
          >
            {displayedContent}
          </ReactMarkdown>
          {showCursor && displayedContent && (
            <span 
              className={cn(
                "typewriter-cursor",
                cursorFading && "fade-out"
              )}
              style={{ '--blink-speed': `${cursorBlinkSpeed}ms` } as React.CSSProperties}
              aria-hidden="true"
            />
          )}
        </div>
      </div>
    </>
  );
}

// Export a version with predefined delays for common use cases
export function FastTypingAnimation(props: Omit<TypingAnimationStreamingProps, 'characterDelay'>) {
  return <TypingAnimationStreaming {...props} characterDelay={20} />;
}

export function SlowTypingAnimation(props: Omit<TypingAnimationStreamingProps, 'characterDelay'>) {
  return <TypingAnimationStreaming {...props} characterDelay={50} />;
}