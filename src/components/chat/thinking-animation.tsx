"use client"

import React, { useState, useEffect, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Brain, Sparkles, Zap, CheckCircle, Search } from "lucide-react"
import { useModelCount } from "@/hooks/useModelStats"

interface ThinkingAnimationProps {
  webSearchEnabled?: boolean;
  webSearchQueries?: string[];
  webSearchActive?: boolean;
}

interface Stage {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string | (() => string);
  duration: number;
  color: string;
}

export function ThinkingAnimation({ 
  webSearchEnabled = false, 
  webSearchQueries = [], 
  webSearchActive = false 
}: ThinkingAnimationProps = {}) {
  const [currentStage, setCurrentStage] = useState(0)
  const [currentSearchQuery, setCurrentSearchQuery] = useState(0)
  const [webSearchMessage, setWebSearchMessage] = useState("");
  const { totalRounded } = useModelCount()
  
  const stages = useMemo((): Stage[] => {
    const baseStages: Stage[] = [
      {
        icon: Sparkles,
        title: "Analyzing Prompt",
        description: "Understanding your request and extracting key requirements",
        duration: 1000,
        color: "text-blue-400"
      },
      {
        icon: Brain,
        title: "Selecting AI Model",
        description: `Choosing the optimal AI from ${totalRounded} available models`,
        duration: 800,
        color: "text-purple-400"
      }
    ];

    // Add web search stage if enabled
    if (webSearchEnabled) {
      baseStages.push({
        icon: Search,
        title: "Searching the Web",
        description: () => {
          if (webSearchMessage) return webSearchMessage;
          return webSearchQueries.length > 0 
            ? `Searching: "${webSearchQueries[currentSearchQuery] || webSearchQueries[0] || 'latest information'}"` 
            : "Gathering real-time information from reliable sources";
        },
        duration: 15000, // Longer duration for web search
        color: "text-cyan-400"
      });
    }

    baseStages.push(
      {
        icon: Zap,
        title: "Optimizing Response",
        description: "Configuring the best parameters for your specific task",
        duration: 600,
        color: "text-green-400"
      },
      {
        icon: CheckCircle,
        title: "Preparing Response",
        description: "Initializing the selected AI model for your query",
        duration: 500,
        color: "text-emerald-400"
      }
    );

    return baseStages;
  }, [totalRounded, webSearchEnabled, webSearchQueries, currentSearchQuery, webSearchMessage])
  
  // Cycle through search queries and progressive messages during web search
  useEffect(() => {
    if (webSearchEnabled) {
      const isWebSearchStage = stages[currentStage]?.icon === Search;
      if (isWebSearchStage) {
        let queryIndex = 0;
        let messageIndex = 0;
        
        const progressMessages = [
          "Gathering real-time information...",
          "Almost there, processing results...", 
          "Just a few more seconds...",
          "Finalizing search results...",
          "Nearly complete..."
        ];
        
        // Cycle through queries every 2 seconds
        const queryInterval = setInterval(() => {
          if (webSearchQueries.length > 1) {
            queryIndex = (queryIndex + 1) % webSearchQueries.length;
            setCurrentSearchQuery(queryIndex);
          }
        }, 2000);
        
        // Show progressive messages every 10 seconds
        const messageInterval = setInterval(() => {
          setWebSearchMessage(progressMessages[messageIndex % progressMessages.length]);
          messageIndex++;
        }, 10000);
        
        return () => {
          clearInterval(queryInterval);
          clearInterval(messageInterval);
        };
      }
    }
  }, [currentStage, webSearchEnabled, webSearchQueries, stages])

  useEffect(() => {
    let timeoutId: NodeJS.Timeout
    
    const advanceStage = () => {
      if (currentStage < stages.length - 1) {
        timeoutId = setTimeout(() => {
          setCurrentStage((prev: number) => prev + 1)
        }, stages[currentStage].duration)
      }
    }

    advanceStage()

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [currentStage, stages])

    return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      style={{ marginBottom: '1rem' }}
    >
      {/* Main Card - Full Width like RouterSelectionBox */}
      <motion.div 
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <div className="relative overflow-hidden bg-gradient-to-br from-gray-900/80 to-gray-800/60 border border-gray-700/50 rounded-lg p-4 w-full backdrop-blur-sm">
        {/* Animated background shimmer */}
        <motion.div
          animate={{ x: ['-100%', '200%'] }}
          transition={{ 
            duration: 2, 
            repeat: Infinity, 
            ease: "linear"
          }}
          style={{ position: 'absolute', inset: 0 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-purple-500/10 to-transparent" />
        </motion.div>
        
        {/* Progress bar */}
        <div className="relative mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs font-medium text-gray-400">JustSimpleChat AI Router</span>
            <span className="text-xs text-gray-500">{currentStage + 1}/{stages.length}</span>
          </div>
          <div className="w-full bg-gray-800 rounded-full h-1.5 overflow-hidden">
            <motion.div
              initial={{ width: "0%" }}
              animate={{ width: `${((currentStage + 1) / stages.length) * 100}%` }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              style={{ height: '100%' }}
            >
              <div className="h-full bg-gradient-to-r from-purple-500 via-blue-500 to-green-500" />
            </motion.div>
          </div>
        </div>

        {/* Current Stage */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStage}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="relative">
            {/* Icon and Title */}
            <div className="flex items-center gap-3 mb-3">
              <motion.div
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <div className={`p-2 rounded-lg bg-gray-800/50 ${stages[currentStage].color}`}>
                  {React.createElement(stages[currentStage].icon, { className: "w-5 h-5" })}
                </div>
              </motion.div>
              <div className="flex-1">
                <h3 className="text-white font-semibold text-lg">
                  {stages[currentStage].title}
                </h3>
                <p className="text-gray-400 text-sm leading-relaxed">
                  {typeof stages[currentStage].description === 'function' 
                    ? stages[currentStage].description() 
                    : stages[currentStage].description}
                </p>
              </div>
            </div>
            
            {/* Animated dots */}
            <div className="flex items-center gap-1 mt-4">
              <span className="text-gray-500 text-sm">Processing</span>
              <div className="flex gap-1">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    animate={{ 
                      opacity: [0.3, 1, 0.3],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{ 
                      duration: 1.5, 
                      repeat: Infinity,
                      delay: i * 0.2
                    }}
                  >
                    <div className="w-1 h-1 bg-gray-500 rounded-full" />
                  </motion.div>
                ))}
              </div>
            </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Stage indicators */}
        <div className="flex justify-center gap-2 mt-6">
          {stages.map((_, index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <div className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                index <= currentStage 
                  ? 'bg-purple-400' 
                  : 'bg-gray-600'
              }`} />
            </motion.div>
          ))}
        </div>
        </div>
      </motion.div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            style={{
              position: 'absolute',
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 2) * 40}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.3,
            }}
          >
            <div className="w-1 h-1 bg-purple-400/30 rounded-full" />
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}