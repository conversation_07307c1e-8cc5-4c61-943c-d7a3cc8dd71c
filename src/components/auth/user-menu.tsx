'use client';

import { signOut, useSession } from 'next-auth/react';
import { useConversationStore } from '@/stores/conversationStore';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  User, 
  Settings, 
  CreditCard, 
  LogOut,
  Sparkles,
  Shield,
  Sliders
} from 'lucide-react';
import Link from 'next/link';

export function UserMenu() {
  const { data: session } = useSession();
  const resetStore = useConversationStore(state => state.resetStore);
  
  if (!session?.user) return null;
  
  // Handle logout with proper cleanup
  const handleSignOut = async () => {
    try {
      // First, clear all conversation data and localStorage
      resetStore();
      
      // Then sign out
      await signOut({ callbackUrl: '/' });
      
      console.log('[UserMenu] Logout completed with cleanup');
    } catch (error) {
      console.error('[UserMenu] Error during logout:', error);
      // Still try to sign out even if cleanup fails
      await signOut({ callbackUrl: '/' });
    }
  };
  
  const initials = session.user.name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase() || '?';
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="relative group">
          <Avatar className="h-8 w-8 transition-transform group-hover:scale-105">
            <AvatarImage src={session.user.image || undefined} />
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-cyan-500 text-white">
              {initials}
            </AvatarFallback>
          </Avatar>
          {(session.user.plan === 'PLUS' || session.user.plan === 'ADVANCED' || session.user.plan === 'MAX') && (
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full ring-2 ring-background" />
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{session.user.name}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {session.user.email}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-muted-foreground">
                {session.user.credits.toLocaleString()} credits
              </span>
              {session.user.plan !== 'FREE' && (
                <span className="inline-flex items-center gap-1 text-xs bg-gradient-to-r from-amber-400 to-orange-500 text-white px-2 py-0.5 rounded-full">
                  <Sparkles className="w-3 h-3" />
                  {session.user.plan}
                </span>
              )}
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/settings" className="cursor-pointer">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/billing" className="cursor-pointer">
            <CreditCard className="mr-2 h-4 w-4" />
            Billing
          </Link>
        </DropdownMenuItem>
        {(session.user.plan === 'MAX' || ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(session.user.email || '')) && (
          <>
            <DropdownMenuItem asChild>
              <Link href="/admin/models" className="cursor-pointer">
                <Shield className="mr-2 h-4 w-4" />
                Admin Panel
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/admin/model-selection" className="cursor-pointer">
                <Sliders className="mr-2 h-4 w-4" />
                Model Selection
              </Link>
            </DropdownMenuItem>
          </>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleSignOut}
          className="cursor-pointer text-red-600"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}