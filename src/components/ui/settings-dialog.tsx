'use client';

import { useSession } from 'next-auth/react';
import { useState, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  <PERSON>, 
  Shield, 
  <PERSON>lette, 
  Globe,
  Moon,
  Sun,
  Check,
  X,
  Save,
  Loader2,
  Brain,
  Sparkles,
  Clock,
  Search
} from 'lucide-react';
import { useTheme } from '@/contexts/theme-context';
import { TIMEZONE_REGIONS, getCurrentTimeInTimezone, getUserTimezone, type TimezoneOption } from '@/lib/constants/timezones';

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

// Timezone selector component
function TimezoneSelector({ value, onChange }: { value: string; onChange: (value: string) => void }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<string>('Popular');
  
  // Auto-detect user timezone on mount if no value set
  useEffect(() => {
    if (!value || value === 'UTC') {
      const userTimezone = getUserTimezone();
      if (userTimezone && userTimezone !== 'UTC') {
        onChange(userTimezone);
      }
    }
  }, [value, onChange]);
  
  // Filter timezones based on search
  const filteredTimezones = useMemo(() => {
    if (!searchQuery) return TIMEZONE_REGIONS[selectedRegion as keyof typeof TIMEZONE_REGIONS] || [];
    
    const query = searchQuery.toLowerCase();
    const allTimezones = Object.entries(TIMEZONE_REGIONS).flatMap(([region, zones]) => 
      zones.map(zone => ({ ...zone, region }))
    );
    
    return allTimezones.filter(tz => 
      tz.label.toLowerCase().includes(query) || 
      tz.value.toLowerCase().includes(query)
    );
  }, [searchQuery, selectedRegion]);
  
  // Get current timezone info
  const currentTimezone = useMemo(() => {
    const allTimezones = Object.values(TIMEZONE_REGIONS).flat();
    return allTimezones.find(tz => tz.value === value) || { value: 'UTC', label: 'UTC', offset: 'UTC±0' };
  }, [value]);
  
  const currentTime = getCurrentTimeInTimezone(value);
  
  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 dark:text-white text-left flex items-center justify-between"
      >
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-gray-500" />
            <span className="font-medium">{currentTimezone.label}</span>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
            {currentTimezone.offset} • {currentTime && `Current time: ${currentTime}`}
          </div>
        </div>
        <svg className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-96 overflow-hidden">
          {/* Search bar */}
          <div className="p-2 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search timezones..."
                className="w-full pl-8 pr-3 py-1.5 text-sm bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded focus:ring-1 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex">
            {/* Region tabs */}
            {!searchQuery && (
              <div className="w-32 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <div className="p-1">
                  {Object.keys(TIMEZONE_REGIONS).map((region) => (
                    <button
                      key={region}
                      onClick={() => setSelectedRegion(region)}
                      className={`w-full text-left px-3 py-2 text-xs rounded transition-colors ${
                        selectedRegion === region
                          ? 'bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
                      }`}
                    >
                      {region}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {/* Timezone list */}
            <div className="flex-1 max-h-80 overflow-y-auto">
              <div className="p-1">
                {filteredTimezones.length === 0 ? (
                  <div className="px-3 py-8 text-center text-sm text-gray-500 dark:text-gray-400">
                    No timezones found
                  </div>
                ) : (
                  filteredTimezones.map((timezone) => {
                    const time = getCurrentTimeInTimezone(timezone.value);
                    return (
                      <button
                        key={timezone.value}
                        onClick={() => {
                          onChange(timezone.value);
                          setIsOpen(false);
                          setSearchQuery('');
                        }}
                        className={`w-full text-left px-3 py-2 rounded transition-colors hover:bg-gray-100 dark:hover:bg-gray-800 ${
                          value === timezone.value ? 'bg-purple-100 dark:bg-purple-900/50' : ''
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {timezone.label}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {timezone.offset} {time && `• ${time}`}
                            </div>
                          </div>
                          {value === timezone.value && (
                            <Check className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                          )}
                        </div>
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export function SettingsDialog({ isOpen, onClose }: SettingsDialogProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  console.log('[SettingsDialog] isOpen:', isOpen);
  
  const { data: session } = useSession();
  const theme = useTheme();

  const [activeTab, setActiveTab] = useState('profile');
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  // Settings state
  const [settings, setSettings] = useState({
    profile: {
      name: session?.user?.name || '',
      email: session?.user?.email || '',
      bio: '',
      avatar: session?.user?.image || '',
      preferredTopics: [] as string[]
    },
    personalization: {
      preferredName: '',
      personalContext: '',
      personality: 'balanced',
      formality: 'casual',
      interests: '',
      profession: '',
      goals: '',
      communicationStyle: 'concise',
      humorLevel: 'moderate',
      technicalLevel: 'intermediate'
    },
    notifications: {
      emailNotifications: true,
      browserNotifications: false,
      marketingEmails: false,
      productUpdates: true,
      agentLaunchNotifications: true
    },
    preferences: {
      theme: 'dark',
      language: 'en',
      timezone: 'UTC',
      messageStyle: 'standard'
    },
    privacy: {
      shareUsageData: false,
      publicProfile: false,
      conversationHistory: true
    }
  });

  const tabs = [
    { id: 'profile', label: 'Profile', icon: <User className="w-4 h-4" /> },
    { id: 'personalization', label: 'AI', icon: <Brain className="w-4 h-4" /> },
    { id: 'preferences', label: 'Theme', icon: <Palette className="w-4 h-4" /> },
    { id: 'notifications', label: 'Alerts', icon: <Bell className="w-4 h-4" /> },
    { id: 'privacy', label: 'Privacy', icon: <Shield className="w-4 h-4" /> },
  ];

  // Load preferences on mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const response = await fetch('/api/user/preferences');
        if (response.ok) {
          const data = await response.json();
          if (data.preferences) {
            setSettings(prevSettings => ({
              ...prevSettings,
              ...data.preferences,
              profile: {
                ...prevSettings.profile,
                ...(data.preferences.profile || {})
              }
            }));
          }
        }
      } catch (error) {
        console.error('Failed to load preferences:', error);
      }
    };

    if (session && isOpen) {
      loadPreferences();
    }
  }, [session, isOpen]);

  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/user/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferences: settings }),
      });

      if (response.ok) {
        setSaved(true);
        setTimeout(() => {
          setSaved(false);
          onClose(); // Close dialog after showing success message
        }, 1500);
      } else {
        console.error('Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
    } finally {
      setSaving(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Display Name</label>
              <input
                type="text"
                value={settings.profile.name}
                onChange={(e) => setSettings({
                  ...settings,
                  profile: { ...settings.profile, name: e.target.value }
                })}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Email Address</label>
              <input
                type="email"
                value={settings.profile.email}
                disabled
                className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md opacity-50 cursor-not-allowed text-gray-900 dark:text-white"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Email cannot be changed for security reasons
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Bio</label>
              <textarea
                value={settings.profile.bio}
                onChange={(e) => setSettings({
                  ...settings,
                  profile: { ...settings.profile, bio: e.target.value }
                })}
                rows={3}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                placeholder="Tell us a bit about yourself..."
              />
            </div>
          </div>
        );

      case 'personalization':
        return (
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-purple-100/60 via-purple-50/40 to-green-50/30 dark:from-purple-900/40 dark:via-purple-800/30 dark:to-green-900/20 border border-purple-300/50 dark:border-purple-700/40 rounded-lg p-3 shadow-sm shadow-purple-500/10">
              <div className="flex items-center gap-2 mb-1">
                <Sparkles className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                <h3 className="font-medium text-sm text-purple-800 dark:text-purple-300">AI Personalization</h3>
              </div>
              <p className="text-xs text-purple-700 dark:text-purple-400">
                Customize how AI interacts with you across all conversations.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">What should I call you?</label>
              <input
                type="text"
                value={settings.personalization.preferredName}
                onChange={(e) => setSettings({
                  ...settings,
                  personalization: { ...settings.personalization, preferredName: e.target.value }
                })}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                placeholder="e.g., Alex, Dr. Smith, Captain"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Personal Context</label>
              <textarea
                value={settings.personalization.personalContext}
                onChange={(e) => setSettings({
                  ...settings,
                  personalization: { ...settings.personalization, personalContext: e.target.value }
                })}
                rows={3}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                placeholder="Brief context about yourself, your work, interests..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Interests</label>
              <input
                type="text"
                value={settings.personalization.interests}
                onChange={(e) => setSettings({
                  ...settings,
                  personalization: { ...settings.personalization, interests: e.target.value }
                })}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                placeholder="Your hobbies, interests, passions..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Profession</label>
              <input
                type="text"
                value={settings.personalization.profession}
                onChange={(e) => setSettings({
                  ...settings,
                  personalization: { ...settings.personalization, profession: e.target.value }
                })}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                placeholder="Your job title, field, or profession..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Goals</label>
              <textarea
                value={settings.personalization.goals}
                onChange={(e) => setSettings({
                  ...settings,
                  personalization: { ...settings.personalization, goals: e.target.value }
                })}
                rows={2}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                placeholder="What you're currently working toward..."
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Personality</label>
                <select
                  value={settings.personalization.personality}
                  onChange={(e) => setSettings({
                    ...settings,
                    personalization: { ...settings.personalization, personality: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <option value="balanced">Balanced</option>
                  <option value="creative">Creative</option> 
                  <option value="analytical">Analytical</option>
                  <option value="friendly">Friendly</option>
                  <option value="professional">Professional</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Formality</label>
                <select
                  value={settings.personalization.formality}
                  onChange={(e) => setSettings({
                    ...settings,
                    personalization: { ...settings.personalization, formality: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <option value="casual">Casual</option>
                  <option value="professional">Professional</option>
                  <option value="formal">Formal</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Communication</label>
                <select
                  value={settings.personalization.communicationStyle}
                  onChange={(e) => setSettings({
                    ...settings,
                    personalization: { ...settings.personalization, communicationStyle: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <option value="concise">Concise</option>
                  <option value="detailed">Detailed</option>
                  <option value="conversational">Conversational</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Technical Level</label>
                <select
                  value={settings.personalization.technicalLevel}
                  onChange={(e) => setSettings({
                    ...settings,
                    personalization: { ...settings.personalization, technicalLevel: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                  <option value="expert">Expert</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Humor Level</label>
              <select
                value={settings.personalization.humorLevel}
                onChange={(e) => setSettings({
                  ...settings,
                  personalization: { ...settings.personalization, humorLevel: e.target.value }
                })}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                <option value="none">None</option>
                <option value="light">Light</option>
                <option value="moderate">Moderate</option>
                <option value="heavy">Heavy</option>
              </select>
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-3 text-gray-700 dark:text-gray-300">Theme</label>
              <div className="grid grid-cols-3 gap-3">
                <button
                  onClick={() => theme.setTheme('dark')}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    theme.theme === 'dark'
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  <Moon className="w-5 h-5 mx-auto mb-1" />
                  <p className="text-xs">Dark</p>
                </button>
                <button
                  onClick={() => theme.setTheme('light')}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    theme.theme === 'light'
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  <Sun className="w-5 h-5 mx-auto mb-1" />
                  <p className="text-xs">Light</p>
                </button>
                <button
                  onClick={() => theme.setTheme('system')}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    theme.theme === 'system'
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300'
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  <Globe className="w-5 h-5 mx-auto mb-1" />
                  <p className="text-xs">System</p>
                </button>
              </div>
              {theme.theme === 'system' && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Currently using: {theme.resolvedTheme} mode
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Language</label>
              <select
                value={settings.preferences.language}
                onChange={(e) => setSettings({
                  ...settings,
                  preferences: { ...settings.preferences, language: e.target.value }
                })}
                className="w-full px-3 py-2 bg-gradient-to-r from-white via-purple-50/30 to-green-50/20 dark:from-gray-800 dark:via-purple-900/20 dark:to-green-900/10 border border-purple-300/40 dark:border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-400 text-gray-900 dark:text-white shadow-sm hover:shadow-md transition-all duration-200"
              >
                <option value="en">English</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="ja">日本語</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Timezone</label>
              <TimezoneSelector
                value={settings.preferences.timezone}
                onChange={(value) => setSettings({
                  ...settings,
                  preferences: { ...settings.preferences, timezone: value }
                })}
              />
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-3">
            {[
              { key: 'emailNotifications', label: 'Email Notifications', desc: 'Get notified via email' },
              { key: 'browserNotifications', label: 'Browser Notifications', desc: 'Show browser notifications' },
              { key: 'marketingEmails', label: 'Marketing Emails', desc: 'Product updates and tips' },
              { key: 'productUpdates', label: 'Product Updates', desc: 'New features and improvements' },
            ].map((item) => (
              <div key={item.key} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <p className="font-medium text-sm text-gray-900 dark:text-white">{item.label}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{item.desc}</p>
                </div>
                <button
                  onClick={() => setSettings({
                    ...settings,
                    notifications: { 
                      ...settings.notifications, 
                      [item.key]: !settings.notifications[item.key as keyof typeof settings.notifications]
                    }
                  })}
                  className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                    settings.notifications[item.key as keyof typeof settings.notifications] 
                      ? 'bg-purple-600' 
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      settings.notifications[item.key as keyof typeof settings.notifications] ? 'translate-x-5' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        );

      case 'privacy':
        return (
          <div className="space-y-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <p className="text-sm text-blue-700 dark:text-blue-400">
                Your privacy is important to us. Control how your data is used.
              </p>
            </div>
            
            <div className="space-y-3">
              {[
                { key: 'shareUsageData', label: 'Share Usage Data', desc: 'Help improve our service' },
                { key: 'publicProfile', label: 'Public Profile', desc: 'Let others see your profile' },
                { key: 'conversationHistory', label: 'Save Conversations', desc: 'Keep chat history' },
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <p className="font-medium text-sm text-gray-900 dark:text-white">{item.label}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">{item.desc}</p>
                  </div>
                  <button
                    onClick={() => setSettings({
                      ...settings,
                      privacy: { 
                        ...settings.privacy, 
                        [item.key]: !settings.privacy[item.key as keyof typeof settings.privacy]
                      }
                    })}
                    className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                      settings.privacy[item.key as keyof typeof settings.privacy] 
                        ? 'bg-purple-600' 
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                        settings.privacy[item.key as keyof typeof settings.privacy] ? 'translate-x-5' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!session || !isMounted) return null;

  // Render to document.body instead of modal-root
  const modalRoot = document.body;
  if (!modalRoot) return null;

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
          />
          
          {/* Dialog */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 sm:inset-auto sm:left-1/2 sm:top-1/2 sm:-translate-x-1/2 sm:-translate-y-1/2 w-full sm:w-[600px] lg:w-[800px] h-full sm:h-auto bg-gradient-to-br from-white via-purple-50/20 to-green-50/10 dark:from-gray-900 dark:via-purple-900/10 dark:to-green-900/5 border-0 sm:border border-purple-200/30 dark:border-purple-700/20 sm:rounded-xl shadow-2xl z-[101] flex flex-col sm:max-h-[90vh] overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/30 via-white to-green-50/20 dark:from-purple-900/20 dark:via-gray-900 dark:to-green-900/10">
              <div>
                <h2 className="text-lg font-semibold bg-gradient-to-r from-purple-700 via-purple-600 to-green-600 dark:from-purple-300 dark:via-purple-200 dark:to-green-300 bg-clip-text text-transparent">Settings</h2>
                <p className="text-sm text-purple-600/80 dark:text-purple-300/80">Manage your account preferences</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-purple-100/60 dark:hover:bg-purple-900/40 rounded-lg transition-colors text-purple-500 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-200"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="flex-1 flex overflow-hidden">
              {/* Sidebar */}
              <div className="w-16 sm:w-48 border-r border-purple-200/30 dark:border-purple-700/20 p-2 sm:p-4 overflow-y-auto bg-gradient-to-b from-purple-50/20 to-green-50/10 dark:from-purple-900/10 dark:to-green-900/5">
                <nav className="space-y-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center sm:gap-3 px-2 sm:px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-gradient-to-r from-purple-100/70 to-green-100/40 dark:from-purple-900/40 dark:to-green-900/25 text-purple-700 dark:text-purple-300 border border-purple-200/50 dark:border-purple-600/30'
                          : 'hover:bg-gradient-to-r hover:from-purple-50/40 hover:to-green-50/20 dark:hover:from-purple-900/20 dark:hover:to-green-900/10 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-300'
                      }`}
                      title={tab.label}
                    >
                      <div className="flex items-center justify-center sm:justify-start gap-3 w-full">
                        <div className="flex-shrink-0">{tab.icon}</div>
                        <span className="hidden sm:inline">{tab.label}</span>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-3 sm:p-4 bg-gradient-to-br from-white via-purple-50/10 to-green-50/5 dark:from-gray-900 dark:via-purple-900/5 dark:to-green-900/3">
                {renderTabContent()}
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-4 border-t border-purple-200/40 dark:border-purple-700/30 bg-gradient-to-r from-purple-50/30 via-white/90 to-green-50/20 dark:from-purple-900/20 dark:via-gray-900/90 dark:to-green-900/10 backdrop-blur-sm">
              <div className="flex items-center gap-2">
                {saved && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center gap-1 text-green-600 dark:text-green-400 text-sm"
                  >
                    <Check className="w-4 h-4" />
                    Saved
                  </motion.div>
                )}
              </div>
              
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <button
                  onClick={onClose}
                  className="flex-1 sm:flex-initial px-4 py-2.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors text-gray-700 dark:text-gray-300 font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving}
                  className="flex-1 sm:flex-initial px-4 py-2.5 bg-gradient-to-r from-purple-600 via-purple-600 to-green-600 hover:from-purple-700 hover:via-purple-700 hover:to-green-700 text-white text-sm rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 font-medium shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300"
                >
                  {saving && <Loader2 className="w-4 h-4 animate-spin" />}
                  Save Changes
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>,
    modalRoot
  );
} 