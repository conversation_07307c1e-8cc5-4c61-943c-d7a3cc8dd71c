import { Metadata } from 'next'
import { createComparisonPageMetadata } from '@/lib/metadata'

const breadcrumbSchema = {
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://www.justsimple.chat"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "ChatGPT Alternative",
      "item": "https://www.justsimple.chat/chatgpt-alternative"
    }
  ]
}

const structuredData = [
  {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "ChatGPT Alternative - JustSimpleChat",
    "description": "Looking for a ChatGPT alternative? JustSimpleChat gives you access to GPT-4, Claude, Gemini and 200+ AI models for less than ChatGPT Plus. Compare features and save money.",
    "url": "https://www.justsimple.chat/chatgpt-alternative",
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": "JustSimpleChat",
      "applicationCategory": "ProductivityApplication",
      "offers": {
        "@type": "Offer",
        "price": "7.99",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "1247"
      }
    }
  },
  breadcrumbSchema
]

export const metadata: Metadata = {
  ...createComparisonPageMetadata('ChatGPT', {
    models: 200,
    savings: '$12.01',
    features: ['GPT-4 access', 'Claude access', 'Gemini access', 'smart routing', 'web search']
  }),
  other: {
    'application/ld+json': JSON.stringify(structuredData)
  }
}