'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON>, <PERSON>rkles, Code, MessageSquare, Image, 
  FileText, BarChart3, Zap, Clock, Target, 
  Users, Award, TrendingUp, ChevronRight,
  Cpu, Brain, Eye, Mic, Globe, Calculator
} from 'lucide-react'
import Link from 'next/link'

const ModelCard = ({ 
  name, 
  provider, 
  description, 
  strengths, 
  icon: Icon, 
  color, 
  demoType,
  isNew = false 
}: any) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="relative group cursor-pointer"
  >
    <div className={`absolute inset-0 bg-gradient-to-br ${color} rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300 opacity-20`} />
    
    <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 hover:bg-gray-800/70 transition-all duration-300">
      {isNew && (
        <div className="absolute -top-2 -right-2 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-xs font-bold text-white">
          NEW
        </div>
      )}
      
      <div className="flex items-start justify-between mb-4">
        <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${color} flex items-center justify-center`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-400">{provider}</div>
          <div className="text-xs text-purple-400">{demoType}</div>
        </div>
      </div>
      
      <h3 className="text-lg font-semibold text-white mb-2">{name}</h3>
      <p className="text-gray-400 mb-4 text-sm">{description}</p>
      
      <div className="space-y-2 mb-4">
        {strengths.map((strength: string, idx: number) => (
          <div key={idx} className="flex items-center gap-2">
            <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
            <span className="text-xs text-gray-300">{strength}</span>
          </div>
        ))}
      </div>
      
      <button className="w-full px-4 py-2 rounded-lg bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-300 hover:from-purple-500/30 hover:to-pink-500/30 transition-all duration-200 flex items-center justify-center gap-2 group">
        <Play className="w-4 h-4" />
        Try Live Demo
        <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
      </button>
    </div>
  </motion.div>
)

const LiveDemo = () => {
  const [selectedModel, setSelectedModel] = React.useState('gpt-4o')
  const [prompt, setPrompt] = React.useState('Write a haiku about artificial intelligence')
  const [isGenerating, setIsGenerating] = React.useState(false)
  const [response, setResponse] = React.useState('')

  const models = [
    { id: 'gpt-4o', name: 'GPT-4o', provider: 'OpenAI', color: 'from-green-500 to-emerald-500' },
    { id: 'claude-3-5-sonnet', name: 'Claude 3.5 Sonnet', provider: 'Anthropic', color: 'from-orange-500 to-red-500' },
    { id: 'gemini-1-5-pro', name: 'Gemini 1.5 Pro', provider: 'Google', color: 'from-blue-500 to-cyan-500' },
    { id: 'deepseek-r1', name: 'DeepSeek R1', provider: 'DeepSeek', color: 'from-purple-500 to-pink-500' }
  ]

  const samplePrompts = [
    "Write a haiku about artificial intelligence",
    "Explain quantum computing in simple terms",
    "Create a Python function to calculate fibonacci numbers",
    "Write a compelling product description for wireless headphones",
    "Analyze the pros and cons of remote work"
  ]

  const generateResponse = async () => {
    setIsGenerating(true)
    setResponse('')
    
    // Simulate API call with realistic timing
    const words = [
      "Artificial minds", "Digital thoughts emerge,", "Silicon dreams unfold—", 
      "Intelligence flows", "Through circuits of light,", "Creating new worlds."
    ]
    
    for (let i = 0; i < words.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300))
      setResponse((prev: string) => prev + (i > 0 ? ' ' : '') + words[i])
    }
    
    setIsGenerating(false)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm border border-purple-500/20 p-8"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5" />
      
      <div className="relative">
        <div className="flex items-center gap-3 mb-6">
          <Sparkles className="w-8 h-8 text-purple-400" />
          <h3 className="text-2xl font-bold text-white">Live Model Demo</h3>
        </div>

        {/* Model Selection */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-3">Choose a model:</label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {models.map((model) => (
              <label
                key={model.id}
                className={`flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-all ${
                  selectedModel === model.id
                    ? 'border-purple-500 bg-purple-500/10'
                    : 'border-gray-700 bg-gray-800/50 hover:bg-gray-800/70'
                }`}
              >
                <input
                  type="radio"
                  name="model"
                  value={model.id}
                  checked={selectedModel === model.id}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="text-purple-500 focus:ring-purple-500"
                />
                <div className="flex-1">
                  <div className="font-medium text-white">{model.name}</div>
                  <div className="text-sm text-gray-400">{model.provider}</div>
                </div>
                <div className={`w-3 h-3 rounded-full bg-gradient-to-br ${model.color}`} />
              </label>
            ))}
          </div>
        </div>

        {/* Sample Prompts */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-3">Try a sample prompt:</label>
          <div className="flex flex-wrap gap-2">
            {samplePrompts.map((sample, idx) => (
              <button
                key={idx}
                onClick={() => setPrompt(sample)}
                className="px-3 py-1 rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 transition-colors text-sm"
              >
                {sample}
              </button>
            ))}
          </div>
        </div>

        {/* Prompt Input */}
        <div className="mb-6">
          <label className="block text-gray-400 mb-2">Your prompt:</label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="w-full p-3 rounded-lg bg-gray-800 border border-gray-700 text-white focus:border-purple-500 focus:ring-1 focus:ring-purple-500 resize-none"
            rows={3}
            placeholder="Type your prompt here..."
          />
        </div>

        {/* Generate Button */}
        <button
          onClick={generateResponse}
          disabled={!prompt.trim() || isGenerating}
          className="w-full px-6 py-3 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 mb-6"
        >
          {isGenerating ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Play className="w-5 h-5" />
              Generate Response
            </>
          )}
        </button>

        {/* Response */}
        {(response || isGenerating) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            className="p-4 rounded-lg bg-gray-800/50 border border-gray-700"
          >
            <div className="flex items-center gap-2 mb-3">
              <div className={`w-3 h-3 rounded-full bg-gradient-to-br ${
                models.find(m => m.id === selectedModel)?.color
              }`} />
              <span className="text-sm font-medium text-white">
                {models.find(m => m.id === selectedModel)?.name}
              </span>
            </div>
            <div className="text-gray-300">
              {response || (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-100" />
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-200" />
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  )
}

const CategoryShowcase = ({ title, icon: Icon, models, color }: any) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="mb-12"
  >
    <div className="flex items-center gap-3 mb-6">
      <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${color} flex items-center justify-center`}>
        <Icon className="w-5 h-5 text-white" />
      </div>
      <h3 className="text-2xl font-bold text-white">{title}</h3>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {models.map((model: any, idx: number) => (
        <ModelCard key={idx} {...model} />
      ))}
    </div>
  </motion.div>
)

export default function ModelShowcasePage() {
  const conversationalModels = [
    {
      name: 'GPT-4o',
      provider: 'OpenAI',
      description: 'Fastest and most capable model for general conversation and complex reasoning.',
      strengths: ['Natural conversation', 'Complex reasoning', 'Fast responses'],
      icon: MessageSquare,
      color: 'from-green-500 to-emerald-500',
      demoType: 'Chat Demo',
      isNew: false
    },
    {
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      description: 'Excellent for nuanced conversation and thoughtful, detailed responses.',
      strengths: ['Thoughtful responses', 'Nuanced understanding', 'Ethical reasoning'],
      icon: Brain,
      color: 'from-orange-500 to-red-500',
      demoType: 'Chat Demo'
    },
    {
      name: 'Gemini 1.5 Pro',
      provider: 'Google',
      description: 'Large context window perfect for analyzing long documents and conversations.',
      strengths: ['Long context', 'Document analysis', 'Multi-step reasoning'],
      icon: Eye,
      color: 'from-blue-500 to-cyan-500',
      demoType: 'Chat Demo'
    }
  ]

  const codingModels = [
    {
      name: 'DeepSeek R1',
      provider: 'DeepSeek',
      description: 'Specialized reasoning model excellent for complex programming tasks.',
      strengths: ['Advanced reasoning', 'Complex algorithms', 'Code optimization'],
      icon: Code,
      color: 'from-purple-500 to-pink-500',
      demoType: 'Code Demo',
      isNew: true
    },
    {
      name: 'GPT-4o',
      provider: 'OpenAI',
      description: 'Versatile coding assistant with broad language support and debugging.',
      strengths: ['Multi-language support', 'Quick debugging', 'Code explanation'],
      icon: Cpu,
      color: 'from-green-500 to-emerald-500',
      demoType: 'Code Demo'
    },
    {
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      description: 'Excellent for code review, refactoring, and architectural discussions.',
      strengths: ['Code review', 'Refactoring', 'Best practices'],
      icon: Target,
      color: 'from-orange-500 to-red-500',
      demoType: 'Code Demo'
    }
  ]

  const contentModels = [
    {
      name: 'GPT-4o',
      provider: 'OpenAI',
      description: 'Versatile content creator for blogs, marketing copy, and creative writing.',
      strengths: ['Creative writing', 'Marketing copy', 'Blog posts'],
      icon: FileText,
      color: 'from-green-500 to-emerald-500',
      demoType: 'Writing Demo'
    },
    {
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      description: 'Thoughtful content creation with excellent tone and style adaptation.',
      strengths: ['Tone adaptation', 'Long-form content', 'Editorial quality'],
      icon: FileText,
      color: 'from-orange-500 to-red-500',
      demoType: 'Writing Demo'
    },
    {
      name: 'Gemini 1.5 Pro',
      provider: 'Google',
      description: 'Great for research-heavy content with fact-checking and citations.',
      strengths: ['Research integration', 'Fact checking', 'Academic writing'],
      icon: BarChart3,
      color: 'from-blue-500 to-cyan-500',
      demoType: 'Writing Demo'
    }
  ]

  const analysisModels = [
    {
      name: 'GPT-4o',
      provider: 'OpenAI',
      description: 'Powerful data analysis with visualization suggestions and insights.',
      strengths: ['Data insights', 'Visualization ideas', 'Trend analysis'],
      icon: BarChart3,
      color: 'from-green-500 to-emerald-500',
      demoType: 'Analysis Demo'
    },
    {
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      description: 'Thorough analysis with detailed explanations and methodology.',
      strengths: ['Detailed analysis', 'Clear methodology', 'Statistical insights'],
      icon: Calculator,
      color: 'from-orange-500 to-red-500',
      demoType: 'Analysis Demo'
    },
    {
      name: 'DeepSeek R1',
      provider: 'DeepSeek',
      description: 'Advanced reasoning for complex analytical and mathematical problems.',
      strengths: ['Complex reasoning', 'Mathematical analysis', 'Pattern recognition'],
      icon: Brain,
      color: 'from-purple-500 to-pink-500',
      demoType: 'Analysis Demo'
    }
  ]

  const multimodalModels = [
    {
      name: 'GPT-4o',
      provider: 'OpenAI',
      description: 'Advanced vision capabilities for image analysis and understanding.',
      strengths: ['Image analysis', 'Visual reasoning', 'Chart interpretation'],
      icon: Eye,
      color: 'from-green-500 to-emerald-500',
      demoType: 'Vision Demo'
    },
    {
      name: 'Claude 3.5 Sonnet',
      provider: 'Anthropic',
      description: 'Detailed image analysis with contextual understanding.',
      strengths: ['Detailed descriptions', 'Context awareness', 'Visual storytelling'],
      icon: Image,
      color: 'from-orange-500 to-red-500',
      demoType: 'Vision Demo'
    },
    {
      name: 'Gemini 1.5 Pro',
      provider: 'Google',
      description: 'Multi-modal processing with video and document understanding.',
      strengths: ['Video analysis', 'Document processing', 'Multi-format input'],
      icon: Globe,
      color: 'from-blue-500 to-cyan-500',
      demoType: 'Vision Demo'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        {/* Animated background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-br from-purple-500/20 to-transparent rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-br from-pink-500/20 to-transparent rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-50 animate-pulse" />
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <Sparkles className="w-10 h-10 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-6">
              Model{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                Showcase
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Test and explore 180+ AI models with live, interactive demos. 
              See each model&apos;s unique strengths in action.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#demo"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Play className="w-5 h-5" />
                Try Live Demo
              </Link>
              <Link
                href="#models"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <Eye className="w-5 h-5" />
                Browse Models
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Live Demo Section */}
      <section id="demo" className="relative py-20 bg-gray-800/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Interactive Model Testing
            </h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Compare models side-by-side with your own prompts or try our curated examples
            </p>
          </motion.div>

          <LiveDemo />
        </div>
      </section>

      {/* Models by Category */}
      <section id="models" className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Models by Use Case
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Explore models optimized for specific tasks and discover their unique capabilities
            </p>
          </motion.div>

          <CategoryShowcase
            title="Conversational AI"
            icon={MessageSquare}
            models={conversationalModels}
            color="from-blue-500 to-cyan-500"
          />

          <CategoryShowcase
            title="Code Generation"
            icon={Code}
            models={codingModels}
            color="from-purple-500 to-pink-500"
          />

          <CategoryShowcase
            title="Content Writing"
            icon={FileText}
            models={contentModels}
            color="from-green-500 to-emerald-500"
          />

          <CategoryShowcase
            title="Data Analysis"
            icon={BarChart3}
            models={analysisModels}
            color="from-orange-500 to-red-500"
          />

          <CategoryShowcase
            title="Vision & Multimodal"
            icon={Eye}
            models={multimodalModels}
            color="from-pink-500 to-rose-500"
          />
        </div>
      </section>

      {/* Performance Metrics */}
      <section className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Real Performance Metrics
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              All demos run on live models with real performance data
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Zap className="w-8 h-8 text-yellow-400 mx-auto mb-4" />
              <div className="text-3xl font-bold text-white mb-2">&lt; 2s</div>
              <div className="text-gray-400">Average Response Time</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Target className="w-8 h-8 text-green-400 mx-auto mb-4" />
              <div className="text-3xl font-bold text-white mb-2">99.2%</div>
              <div className="text-gray-400">Success Rate</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Users className="w-8 h-8 text-blue-400 mx-auto mb-4" />
              <div className="text-3xl font-bold text-white mb-2">500K+</div>
              <div className="text-gray-400">Monthly Tests</div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 text-center"
            >
              <Award className="w-8 h-8 text-purple-400 mx-auto mb-4" />
              <div className="text-3xl font-bold text-white mb-2">180+</div>
              <div className="text-gray-400">Available Models</div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
              Ready to Explore Every AI Model?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get unlimited access to all models and build amazing AI-powered applications.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Sparkles className="w-5 h-5" />
                Start Free Trial
              </Link>
              <Link
                href="/compare"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <BarChart3 className="w-5 h-5" />
                Compare Models
              </Link>
            </div>

            <p className="mt-6 text-gray-400">
              No credit card required • Full model access • Cancel anytime
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  )
}