'use client'

import { ChatInterface } from "@/components/chat/chat-interface"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function ChatPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    // Always redirect logged-in users to home for new conversations
    if (status === 'authenticated' && session?.user) {
      // Check if we're in a specific conversation
      const hasConversation = window.location.search.includes('conversation=') || 
                            window.location.search.includes('id=') ||
                            sessionStorage.getItem('activeConversation')
      
      if (!hasConversation) {
        router.replace('/')
        return
      }
    }
  }, [session, status, router])

  // Show loading while checking session
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <ChatInterface />
  )
}