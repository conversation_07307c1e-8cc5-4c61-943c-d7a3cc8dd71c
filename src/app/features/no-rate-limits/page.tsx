'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON>, <PERSON>, AlertTriangle, Check, X, Infinity, 
  TrendingUp, Users, Shield, Gauge, Timer, 
  BarChart3, Activity, Rocket, Target
} from 'lucide-react'
import Link from 'next/link'

const MetricCard = ({ value, label, icon: Icon, color = "purple" }: any) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    whileInView={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5 }}
    className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6"
  >
    <div className={`absolute inset-0 bg-gradient-to-br ${
      color === 'purple' ? 'from-purple-500/5 to-pink-500/5' :
      color === 'blue' ? 'from-blue-500/5 to-cyan-500/5' :
      color === 'green' ? 'from-green-500/5 to-emerald-500/5' :
      'from-orange-500/5 to-red-500/5'
    }`} />
    <div className="relative">
      <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${
        color === 'purple' ? 'from-purple-500 to-pink-500' :
        color === 'blue' ? 'from-blue-500 to-cyan-500' :
        color === 'green' ? 'from-green-500 to-emerald-500' :
        'from-orange-500 to-red-500'
      } flex items-center justify-center mb-4`}>
        <Icon className="w-6 h-6 text-white" />
      </div>
      <div className="text-3xl font-bold text-white mb-2">{value}</div>
      <div className="text-gray-400">{label}</div>
    </div>
  </motion.div>
)

const ComparisonTable = () => {
  const providers = [
    { 
      name: 'OpenAI', 
      gpt4Limit: '40 messages / 3 hours',
      gpt4oLimit: '80 messages / day',
      claudeLimit: 'Not available',
      concurrent: '1 request',
      peak: 'Severe throttling'
    },
    { 
      name: 'Anthropic', 
      gpt4Limit: 'Not available',
      gpt4oLimit: 'Not available', 
      claudeLimit: '50 messages / day',
      concurrent: '1 request',
      peak: 'Queue delays'
    },
    { 
      name: 'Google', 
      gpt4Limit: 'Not available',
      gpt4oLimit: 'Not available',
      claudeLimit: 'Not available', 
      concurrent: '2 requests',
      peak: 'Rate limiting'
    },
    { 
      name: 'JustSimpleChat', 
      gpt4Limit: 'Unlimited',
      gpt4oLimit: 'Unlimited',
      claudeLimit: 'Unlimited',
      concurrent: 'Unlimited',
      peak: 'Full speed'
    }
  ]

  return (
    <div className="relative overflow-x-auto">
      <table className="w-full text-left">
        <thead>
          <tr className="border-b border-gray-700">
            <th className="pb-4 pr-6 text-gray-400 font-medium">Provider</th>
            <th className="pb-4 px-4 text-gray-400 font-medium">GPT-4 Limit</th>
            <th className="pb-4 px-4 text-gray-400 font-medium">GPT-4o Limit</th>
            <th className="pb-4 px-4 text-gray-400 font-medium">Claude Limit</th>
            <th className="pb-4 px-4 text-gray-400 font-medium">Concurrent</th>
            <th className="pb-4 pl-6 text-gray-400 font-medium">Peak Hours</th>
          </tr>
        </thead>
        <tbody>
          {providers.map((provider, idx) => (
            <tr 
              key={idx} 
              className={`border-b border-gray-800 ${
                provider.name === 'JustSimpleChat' 
                  ? 'bg-gradient-to-r from-purple-500/10 to-pink-500/10' 
                  : ''
              }`}
            >
              <td className="py-4 pr-6">
                <div className="flex items-center gap-2">
                  <span className="text-white font-medium">{provider.name}</span>
                  {provider.name === 'JustSimpleChat' && (
                    <span className="px-2 py-0.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-semibold">
                      NO LIMITS
                    </span>
                  )}
                </div>
              </td>
              <td className="py-4 px-4">
                <span className={provider.gpt4Limit === 'Unlimited' ? 'text-green-400 font-semibold' : 'text-orange-400'}>
                  {provider.gpt4Limit}
                </span>
              </td>
              <td className="py-4 px-4">
                <span className={provider.gpt4oLimit === 'Unlimited' ? 'text-green-400 font-semibold' : 'text-orange-400'}>
                  {provider.gpt4oLimit}
                </span>
              </td>
              <td className="py-4 px-4">
                <span className={provider.claudeLimit === 'Unlimited' ? 'text-green-400 font-semibold' : 
                             provider.claudeLimit === 'Not available' ? 'text-gray-500' : 'text-orange-400'}>
                  {provider.claudeLimit}
                </span>
              </td>
              <td className="py-4 px-4">
                <span className={provider.concurrent === 'Unlimited' ? 'text-green-400 font-semibold' : 'text-orange-400'}>
                  {provider.concurrent}
                </span>
              </td>
              <td className="py-4 pl-6">
                <span className={provider.peak === 'Full speed' ? 'text-green-400 font-semibold' : 'text-red-400'}>
                  {provider.peak}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

const UsageDemo = () => {
  const [activeDemo, setActiveDemo] = React.useState(0)
  const demos = [
    {
      title: "Batch Processing",
      description: "Process 100+ documents simultaneously",
      requests: 127,
      time: "2.3 minutes",
      status: "success"
    },
    {
      title: "Peak Hour Usage",
      description: "Heavy workload during business hours",
      requests: 340,
      time: "8.7 minutes", 
      status: "success"
    },
    {
      title: "Continuous Development",
      description: "Code review and generation all day",
      requests: 890,
      time: "Throughout day",
      status: "success"
    }
  ]

  React.useEffect(() => {
    const interval = setInterval(() => {
      setActiveDemo((prev: number) => (prev + 1) % demos.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [demos.length])

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-sm border border-purple-500/20 p-8"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5" />
      
      <div className="relative">
        <div className="flex items-center gap-3 mb-6">
          <Activity className="w-8 h-8 text-purple-400" />
          <h3 className="text-2xl font-bold text-white">Live Usage Demo</h3>
        </div>

        <div className="space-y-4">
          {demos.map((demo, idx) => (
            <div
              key={idx}
              className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
                idx === activeDemo
                  ? 'bg-purple-500/20 border-purple-500/50'
                  : 'bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70'
              }`}
              onClick={() => setActiveDemo(idx)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-white">{demo.title}</h4>
                  <p className="text-gray-400">{demo.description}</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-400">{demo.requests}</div>
                  <div className="text-sm text-gray-400">requests</div>
                </div>
              </div>
              
              {idx === activeDemo && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  className="mt-4 pt-4 border-t border-purple-500/30"
                >
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-purple-400" />
                      <span className="text-purple-300">Completed in {demo.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">No throttling</span>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  )
}

export default function NoRateLimitsPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        {/* Animated background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-br from-purple-500/20 to-transparent rounded-full blur-3xl animate-pulse" />
          <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-br from-pink-500/20 to-transparent rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-lg opacity-50 animate-pulse" />
                <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <Infinity className="w-10 h-10 text-white" />
                </div>
              </div>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold text-white mb-6">
              No Rate Limits.{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                No Waiting.
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Use AI models as much as you need, when you need them. No daily caps, 
              no hourly restrictions, no throttling during peak hours.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Rocket className="w-5 h-5" />
                Start Unlimited Access
              </Link>
              <Link
                href="#comparison"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                <BarChart3 className="w-5 h-5" />
                Compare Limits
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* The Problem Section */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Rate Limits Kill Productivity
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Every major AI provider throttles your usage, forcing you to wait or pay more.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-orange-500/10" />
              <div className="relative">
                <AlertTriangle className="w-12 h-12 text-red-400 mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Hit the Wall</h3>
                <p className="text-gray-400">
                  Reach your daily limit and your work stops. Mid-project, mid-conversation.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-yellow-500/10" />
              <div className="relative">
                <Clock className="w-12 h-12 text-orange-400 mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Wait Times</h3>
                <p className="text-gray-400">
                  Queue for minutes during peak hours. Your ideas can&apos;t wait.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/10 to-red-500/10" />
              <div className="relative">
                <Gauge className="w-12 h-12 text-yellow-400 mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Throttling</h3>
                <p className="text-gray-400">
                  Slower responses when servers are busy. Quality degrades.
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="relative overflow-hidden rounded-xl bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 p-6"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-purple-500/10" />
              <div className="relative">
                <Target className="w-12 h-12 text-red-400 mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">Context Loss</h3>
                <p className="text-gray-400">
                  Forced breaks interrupt your flow and lose conversation context.
                </p>
              </div>
            </motion.div>
          </div>

          {/* Real Examples */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-8"
          >
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              Real User Experiences
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 rounded-lg p-6">
                <div className="text-red-400 mb-2">⚠️ OpenAI ChatGPT Plus</div>
                <p className="text-gray-300 text-sm italic">
                  &ldquo;Hit my 40 message limit at 2pm. Had to wait 3 hours to continue my presentation prep.&rdquo;
                </p>
              </div>
              <div className="bg-gray-900/50 rounded-lg p-6">
                <div className="text-orange-400 mb-2">⚠️ Claude Pro</div>
                <p className="text-gray-300 text-sm italic">
                  &ldquo;Ran out of messages during a coding session. Lost my train of thought waiting for reset.&rdquo;
                </p>
              </div>
              <div className="bg-gray-900/50 rounded-lg p-6">
                <div className="text-yellow-400 mb-2">⚠️ Google Bard</div>
                <p className="text-gray-300 text-sm italic">
                  &ldquo;Slow responses during busy hours. Can&apos;t rely on it for urgent work.&rdquo;
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Comparison Section */}
      <section id="comparison" className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Compare Rate Limits
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              See how JustSimpleChat stacks up against the competition
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-8"
          >
            <ComparisonTable />
          </motion.div>
        </div>
      </section>

      {/* Our Solution Section */}
      <section className="relative py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Unlimited AI. Unlimited Productivity.
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              No caps, no throttling, no interruptions. Just pure AI power when you need it.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Infinity className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Truly Unlimited Usage</h3>
                  <p className="text-gray-400">
                    Use any model as many times as you need. No daily, hourly, or monthly caps. 
                    Process hundreds of documents or have marathon coding sessions.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Peak Performance Always</h3>
                  <p className="text-gray-400">
                    Full speed responses even during busy hours. Our infrastructure scales 
                    automatically to handle any load.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Concurrent Conversations</h3>
                  <p className="text-gray-400">
                    Run multiple AI conversations simultaneously. Compare outputs, 
                    test different approaches, collaborate with teams.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center flex-shrink-0 mt-1">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Guaranteed Availability</h3>
                  <p className="text-gray-400">
                    99.9% uptime SLA. When you need AI, it&apos;s there. No &ldquo;service unavailable&rdquo; 
                    messages during critical moments.
                  </p>
                </div>
              </div>
            </motion.div>

            <UsageDemo />
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              value="∞"
              label="Daily message limit"
              icon={Infinity}
              color="purple"
            />
            <MetricCard
              value="< 2s"
              label="Average response time"
              icon={Zap}
              color="blue"
            />
            <MetricCard
              value="99.9%"
              label="Uptime guarantee"
              icon={Shield}
              color="green"
            />
            <MetricCard
              value="24/7"
              label="Peak performance"
              icon={TrendingUp}
              color="orange"
            />
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="relative py-20 bg-gray-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Perfect for Heavy AI Users
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              See how unlimited access transforms your workflow
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mb-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Content Creation</h3>
                <ul className="space-y-2 mb-4">
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Generate 50+ articles per day
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Continuous ideation sessions
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Batch content optimization
                  </li>
                </ul>
                <p className="text-sm text-gray-400">No waiting between requests</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mb-4">
                  <Activity className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Software Development</h3>
                <ul className="space-y-2 mb-4">
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    All-day code reviews
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Continuous refactoring
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Debug session marathons
                  </li>
                </ul>
                <p className="text-sm text-gray-400">Code when you&apos;re in the flow</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mb-4">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">Data Analysis</h3>
                <ul className="space-y-2 mb-4">
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Process massive datasets
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Multiple analysis angles
                  </li>
                  <li className="flex items-center gap-2 text-gray-300">
                    <Check className="w-4 h-4 text-green-400" />
                    Continuous insights
                  </li>
                </ul>
                <p className="text-sm text-gray-400">Analyze without interruption</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/20 to-orange-900/20" />
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
              Break Free from Rate Limits
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Experience truly unlimited AI access. No waiting, no caps, no compromise.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/signup"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              >
                <Infinity className="w-5 h-5" />
                Start Unlimited Trial
              </Link>
              <Link
                href="/models"
                className="inline-flex items-center justify-center gap-2 px-8 py-4 rounded-lg bg-gray-800 text-white font-semibold hover:bg-gray-700 transition-all duration-200"
              >
                View All Models
              </Link>
            </div>

            <p className="mt-6 text-gray-400">
              No credit card required • Full access from day one • Cancel anytime
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  )
}