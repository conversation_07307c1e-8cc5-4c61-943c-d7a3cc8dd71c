import { <PERSON><PERSON><PERSON> } from 'next'
import Link from 'next/link'
import { 
  ArrowRight, 
  Sparkles, 
  Zap,
  Brain,
  Target,
  TrendingUp,
  CheckCircle2,
  Clock,
  DollarSign,
  Shield,
  Gauge,
  Users,
  Star,
  Lightbulb,
  Code,
  MessageSquare,
  FileText,
  BarChart3,
  Rocket
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { generatePageStructuredData } from '@/lib/structured-data-enhanced'

export const metadata: Metadata = {
  title: 'Smart AI Model Routing: Always Get the Best Model for Every Task | JustSimpleChat',
  description: 'Our AI automatically selects the optimal model for each task. GPT-4 for creativity, Claude for coding, Perplexity for research. No manual switching required.',
  keywords: [
    'smart AI routing',
    'AI model selection',
    'intelligent model switching',
    'best AI model for task',
    'automatic model routing',
    'AI workflow optimization',
    'multi-model AI platform'
  ],
  alternates: {
    canonical: '/features/smart-routing'
  },
  openGraph: {
    title: 'Smart AI Model Routing: Always Get the Best Model for Every Task',
    description: 'Our AI automatically selects the optimal model for each task. No manual switching required.',
    type: 'article',
    publishedTime: new Date().toISOString(),
    authors: ['JustSimpleChat Team'],
    tags: ['AI Features', 'Smart Routing', 'Model Selection']
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Smart AI Model Routing: Always Get the Best Model',
    description: 'Our AI automatically selects the optimal model for each task. No manual switching required.'
  }
}

const routingExamples = [
  {
    task: 'Write creative story',
    input: 'Write a mystery story about a detective who can see ghosts',
    selectedModel: 'GPT-4',
    reason: 'Best for creative writing and storytelling',
    icon: MessageSquare,
    color: 'from-green-600 to-emerald-600'
  },
  {
    task: 'Debug Python code',
    input: 'Fix this function that\'s supposed to sort a list but returns empty',
    selectedModel: 'Claude 3.5 Sonnet',
    reason: 'Superior code analysis and debugging capabilities',
    icon: Code,
    color: 'from-blue-600 to-cyan-600'
  },
  {
    task: 'Research latest news',
    input: 'What are the latest developments in renewable energy?',
    selectedModel: 'Perplexity Sonar',
    reason: 'Real-time web search with accurate citations',
    icon: FileText,
    color: 'from-purple-600 to-pink-600'
  },
  {
    task: 'Analyze data trends',
    input: 'Analyze this sales data and identify key patterns',
    selectedModel: 'Claude 3.5 Sonnet',
    reason: 'Excellent at pattern recognition and analysis',
    icon: BarChart3,
    color: 'from-orange-600 to-red-600'
  }
]

const benefits = [
  {
    icon: Target,
    title: 'Always Optimal Results',
    description: 'Get the best possible output by automatically using the most suitable model for each specific task',
    stats: '95% accuracy'
  },
  {
    icon: Clock,
    title: 'Zero Learning Curve',
    description: 'No need to learn which model works best for what - our AI figures it out instantly',
    stats: '0 seconds setup'
  },
  {
    icon: DollarSign,
    title: 'Cost Optimization',
    description: 'Automatically uses the most cost-effective model that meets quality requirements',
    stats: '40% cost savings'
  },
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Model selection happens in milliseconds with zero delay to your workflow',
    stats: '<50ms routing'
  }
]

const competitors = [
  {
    platform: 'ChatGPT Plus',
    routing: 'Manual only',
    models: '1 model (GPT-4)',
    optimization: 'None',
    learning: 'User must learn',
    color: 'text-gray-400'
  },
  {
    platform: 'Claude Pro',
    routing: 'Manual only', 
    models: '1 model (Claude)',
    optimization: 'None',
    learning: 'User must learn',
    color: 'text-gray-400'
  },
  {
    platform: 'JustSimpleChat',
    routing: 'Smart + Manual',
    models: '200+ models',
    optimization: 'Automatic',
    learning: 'AI learns for you',
    color: 'text-purple-400'
  }
]

export default function SmartRoutingPage() {
  const structuredData = generatePageStructuredData('feature', {
    featureName: 'Smart AI Model Routing',
    description: 'Intelligent automatic selection of the best AI model for each task',
    benefits: benefits.map(b => b.title),
    breadcrumbs: [
      { name: 'Home', url: '/' },
      { name: 'Features', url: '/features' },
      { name: 'Smart Routing', url: '/features/smart-routing' }
    ]
  })

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="min-h-screen bg-gray-950">
        {/* Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-purple-900/20 via-gray-950 to-blue-900/20 pointer-events-none" />
        <div className="fixed inset-0 bg-[url('/grid.svg')] bg-center opacity-10 pointer-events-none" />
        
        {/* Floating Particles */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute w-96 h-96 bg-purple-600/20 rounded-full blur-3xl animate-float top-20 -left-48" />
          <div className="absolute w-96 h-96 bg-blue-600/20 rounded-full blur-3xl animate-float animation-delay-2000 bottom-20 -right-48" />
          <div className="absolute w-64 h-64 bg-green-600/20 rounded-full blur-3xl animate-float animation-delay-4000 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="mb-8">
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Link href="/" className="hover:text-white transition-colors">Home</Link>
              <span>/</span>
              <Link href="/features" className="hover:text-white transition-colors">Features</Link>
              <span>/</span>
              <span className="text-white">Smart Routing</span>
            </div>
          </nav>

          {/* Hero Section */}
          <header className="text-center mb-16 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 blur-3xl opacity-20 -z-10" />
            
            {/* Feature Icon */}
            <div className="inline-flex items-center justify-center mb-8 relative">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl blur-xl opacity-50" />
                <div className="relative bg-gray-900 rounded-2xl p-6 border border-gray-800">
                  <Brain className="w-12 h-12 text-purple-400" />
                </div>
              </div>
            </div>
            
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-xl text-purple-300 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-purple-600/30">
              <Sparkles className="w-4 h-4" />
              Exclusive to JustSimpleChat
              <Badge className="bg-green-600/20 text-green-300 border-green-600/30 ml-2">
                Patent Pending
              </Badge>
            </div>
            
            <h1 className="text-5xl sm:text-6xl font-bold mb-6">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400">
                Smart AI Routing
              </span>
              <br />
              <span className="text-white">
                Always Get the Best Model
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
              Our AI automatically selects the optimal model for every task. 
              <strong className="text-white"> GPT-4 for creativity</strong>, 
              <strong className="text-white"> Claude for coding</strong>, 
              <strong className="text-white"> Perplexity for research</strong>. 
              No manual switching required.
            </p>
            
            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center gap-6 mb-12">
              <div className="flex items-center gap-2 text-gray-400">
                <Users className="w-5 h-5 text-purple-400" />
                <span>10K+ Users Trust Our Routing</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400">
                <Star className="w-5 h-5 text-yellow-400" />
                <span>95% Accuracy Rate</span>
              </div>
              <div className="flex items-center gap-2 text-gray-400">
                <Zap className="w-5 h-5 text-green-400" />
                <span>50ms Response Time</span>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/signup">
                <Button 
                  size="lg" 
                  className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-6 text-lg rounded-xl shadow-xl hover:shadow-2xl transition-all transform hover:scale-105"
                >
                  Try Smart Routing Free
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="#demo">
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="w-full sm:w-auto border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-8 py-6 text-lg rounded-xl"
                >
                  See Live Demo
                </Button>
              </Link>
            </div>
          </header>

          {/* How It Works */}
          <section id="demo" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                See Smart Routing in Action
              </h2>
              <p className="text-lg text-gray-400">
                Watch our AI automatically select the perfect model for different tasks
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-6">
              {routingExamples.map((example, index) => {
                const Icon = example.icon
                return (
                  <Card key={index} className="bg-gray-900/60 backdrop-blur-xl border-gray-800 overflow-hidden">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg bg-gradient-to-r ${example.color}`}>
                            <Icon className="w-5 h-5 text-white" />
                          </div>
                          <CardTitle className="text-lg">{example.task}</CardTitle>
                        </div>
                        <Badge className="bg-purple-600/20 text-purple-300 border-purple-600/40">
                          Auto-Selected
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="bg-gray-800/50 rounded-lg p-3 border border-gray-700">
                          <p className="text-sm text-gray-400 mb-1">Your Input:</p>
                          <p className="text-white text-sm">&ldquo;{example.input}&rdquo;</p>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <Lightbulb className="w-5 h-5 text-yellow-400" />
                          <div className="flex-1">
                            <p className="text-sm text-gray-400">AI Selected:</p>
                            <p className="font-semibold text-white">{example.selectedModel}</p>
                          </div>
                        </div>
                        
                        <div className="bg-purple-900/20 rounded-lg p-3 border border-purple-600/20">
                          <p className="text-sm text-purple-300">
                            <strong>Why:</strong> {example.reason}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </section>

          {/* Benefits Grid */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-center text-white mb-12">
              Why Smart Routing Changes Everything
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon
                return (
                  <Card key={index} className="group hover:scale-105 transition-transform duration-300 bg-gray-900/60 backdrop-blur-xl border-gray-800 hover:border-purple-600/50 text-center">
                    <CardHeader>
                      <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl w-fit mx-auto mb-4">
                        <Icon className="w-8 h-8 text-white" />
                      </div>
                      <CardTitle className="text-xl">{benefit.title}</CardTitle>
                      <div className="text-2xl font-bold text-purple-400">{benefit.stats}</div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-gray-400">
                        {benefit.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </section>

          {/* Comparison Table */}
          <section className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Compare Smart Routing vs Manual Selection
              </h2>
              <p className="text-lg text-gray-400">
                See why users prefer our intelligent routing over manual model switching
              </p>
            </div>
            
            <Card className="bg-gray-900/80 backdrop-blur-xl border-gray-800 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-800">
                      <th className="text-left p-6 text-gray-400 font-medium">Platform</th>
                      <th className="text-center p-6 text-gray-400 font-medium">Model Selection</th>
                      <th className="text-center p-6 text-gray-400 font-medium">Available Models</th>
                      <th className="text-center p-6 text-gray-400 font-medium">Cost Optimization</th>
                      <th className="text-center p-6 text-gray-400 font-medium">Learning Required</th>
                    </tr>
                  </thead>
                  <tbody>
                    {competitors.map((competitor, index) => (
                      <tr 
                        key={index} 
                        className={`border-b border-gray-800/50 ${
                          competitor.platform === 'JustSimpleChat' ? 'bg-purple-900/20' : ''
                        }`}
                      >
                        <td className="p-6 font-semibold">
                          {competitor.platform === 'JustSimpleChat' && (
                            <Badge className="bg-purple-600/20 text-purple-300 border-purple-600/40 mr-2">
                              Our Platform
                            </Badge>
                          )}
                          {competitor.platform}
                        </td>
                        <td className={`text-center p-6 ${competitor.color}`}>{competitor.routing}</td>
                        <td className={`text-center p-6 ${competitor.color}`}>{competitor.models}</td>
                        <td className={`text-center p-6 ${competitor.color}`}>{competitor.optimization}</td>
                        <td className={`text-center p-6 ${competitor.color}`}>{competitor.learning}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </section>

          {/* Technical Details */}
          <section className="mb-16">
            <Card className="bg-gray-900/60 backdrop-blur-xl border-gray-800">
              <CardHeader>
                <CardTitle className="text-2xl flex items-center gap-3">
                  <Gauge className="w-8 h-8 text-purple-400" />
                  How Our Smart Routing Works
                </CardTitle>
                <CardDescription className="text-lg">
                  Advanced AI analyzes your request and selects the optimal model in milliseconds
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-purple-400 font-bold">1</span>
                    </div>
                    <h3 className="font-semibold text-white mb-2">Analyze Request</h3>
                    <p className="text-sm text-gray-400">
                      Our AI examines your prompt to understand the task type, complexity, and requirements
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-blue-400 font-bold">2</span>
                    </div>
                    <h3 className="font-semibold text-white mb-2">Select Model</h3>
                    <p className="text-sm text-gray-400">
                      Choose from 200+ models based on performance benchmarks and cost optimization
                    </p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-green-400 font-bold">3</span>
                    </div>
                    <h3 className="font-semibold text-white mb-2">Deliver Results</h3>
                    <p className="text-sm text-gray-400">
                      Get the best possible output with optimal speed and cost efficiency
                    </p>
                  </div>
                </div>
                
                <div className="bg-purple-900/20 rounded-lg p-6 border border-purple-600/20">
                  <h4 className="font-semibold text-white mb-3 flex items-center gap-2">
                    <Shield className="w-5 h-5 text-purple-400" />
                    Advanced Features
                  </h4>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">Real-time performance monitoring</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">Cost-benefit optimization</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">Learning from user feedback</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">Fallback model redundancy</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* CTA Section */}
          <section className="relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-10 blur-3xl" />
            <div className="relative bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-3xl p-1">
              <div className="bg-gray-900 rounded-3xl p-12 text-center">
                <div className="inline-flex items-center justify-center mb-6">
                  <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl">
                    <Rocket className="w-10 h-10 text-white" />
                  </div>
                </div>
                
                <h2 className="text-4xl font-bold text-white mb-4">
                  Experience the Future of AI Interaction
                </h2>
                
                <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                  Stop manually switching between AI platforms. 
                  Let our smart routing give you the best model for every task, automatically.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                  <Link href="/signup">
                    <Button 
                      size="lg" 
                      className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-10 py-7 text-xl rounded-xl shadow-2xl hover:shadow-3xl transition-all transform hover:scale-105"
                    >
                      Start Free Trial
                      <ArrowRight className="ml-3 h-6 w-6" />
                    </Button>
                  </Link>
                  <Link href="/pricing">
                    <Button 
                      size="lg" 
                      variant="outline" 
                      className="w-full sm:w-auto border-gray-700 hover:border-gray-600 hover:bg-gray-900 px-10 py-7 text-xl rounded-xl"
                    >
                      View Pricing
                    </Button>
                  </Link>
                </div>
                
                <div className="flex flex-wrap justify-center gap-6 text-sm text-gray-400">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                    <span>3-day free trial</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                    <span>No setup required</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                    <span>Cancel anytime</span>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </>
  )
}