'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { Search, ChevronRight, Zap, Brain, DollarSign, Star, Check, X, Sparkles } from 'lucide-react';

// Types
interface Category {
  id: string;
  name: string;
  displayName: string;
  description: string;
  modelCount: number;
  avgScore: number | null;
  manualSelections: number;
  isActive: boolean;
}

interface Model {
  id: string;
  canonicalName: string;
  displayName: string;
  family: string;
  generation?: string;
  speedRating: number;
  qualityRating: number;
  inputCostPer1M: number;
  outputCostPer1M: number;
  categoryScore?: number;
  provider: {
    name: string;
  };
}

interface ManualSelection {
  id: string;
  category: string;
  complexity: string;
  planType?: string;
  priority?: number;
  model: Model;
  createdAt: string;
  updatedAt: string;
}

const planTypes = [
  { key: 'all', label: 'All Plans', icon: '🌍', color: 'from-gray-500 to-gray-600', description: 'Default for all plan types' },
  { key: 'free', label: 'Free', icon: '🆓', color: 'from-green-500 to-green-600', description: 'Free tier users' },
  { key: 'freemium', label: 'Freemium', icon: '⭐', color: 'from-blue-500 to-blue-600', description: 'Freemium plan users' },
  { key: 'plus', label: 'Plus', icon: '➕', color: 'from-purple-500 to-purple-600', description: 'Plus plan users' },
  { key: 'advanced', label: 'Advanced', icon: '🚀', color: 'from-orange-500 to-orange-600', description: 'Advanced plan users' },
  { key: 'max', label: 'Max', icon: '🔥', color: 'from-red-500 to-red-600', description: 'Max plan users' },
  { key: 'enterprise', label: 'Enterprise', icon: '🏢', color: 'from-indigo-500 to-indigo-600', description: 'Enterprise customers' }
];

const complexityLevels = [
  { key: 'simple', label: 'Simple', icon: '⚡', color: 'from-green-500 to-emerald-600', description: 'Quick questions, basic requests' },
  { key: 'standard', label: 'Standard', icon: '🎯', color: 'from-blue-500 to-blue-600', description: 'Regular conversations and tasks' },
  { key: 'complex', label: 'Complex', icon: '🧩', color: 'from-purple-500 to-purple-600', description: 'Detailed analysis, longer responses' },
  { key: 'difficult', label: 'Difficult', icon: '🚀', color: 'from-orange-500 to-orange-600', description: 'Advanced technical tasks' },
  { key: 'expert', label: 'Expert', icon: '🧠', color: 'from-red-500 to-pink-600', description: 'Specialized domain knowledge' },
  { key: 'ultra', label: 'Ultra', icon: '💡', color: 'from-indigo-600 to-purple-700', description: 'Deep thinking mode (Think button)' }
];

export default function ModelSelectionPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [selections, setSelections] = useState<ManualSelection[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedComplexity, setSelectedComplexity] = useState<string>('simple');
  const [selectedPlanType, setSelectedPlanType] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categorySearch, setCategorySearch] = useState('');

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/categories');
      const data = await response.json();
      setCategories(data.categories || []);
      
      // Set first category as selected if none selected
      if (!selectedCategory && data.categories?.length > 0) {
        setSelectedCategory(data.categories[0].name);
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      toast.error('Failed to load categories');
    } finally {
      setLoading(false);
    }
  }, [selectedCategory]);

  const fetchAllModels = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/models');
      const data = await response.json();
      setModels(data.models || []);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      toast.error('Failed to load models');
    }
  }, []);

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
    fetchAllModels();
  }, [fetchCategories, fetchAllModels]);

  // Fetch selections when category, complexity, or plan changes
  useEffect(() => {
    if (selectedCategory && selectedComplexity && selectedPlanType) {
      fetchSelections(selectedCategory, selectedComplexity, selectedPlanType);
    }
  }, [selectedCategory, selectedComplexity, selectedPlanType]);

  const fetchSelections = async (category: string, complexity: string, planType: string) => {
    try {
      const response = await fetch(`/api/admin/model-selections?category=${category}&complexity=${complexity}&planType=${planType}`);
      const data = await response.json();
      setSelections(data.selections || []);
    } catch (error) {
      console.error('Failed to fetch selections:', error);
    }
  };

  const saveSelection = async (modelId: string) => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin/model-selections', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          category: selectedCategory,
          complexity: selectedComplexity,
          planType: selectedPlanType,
          modelId
        })
      });

      if (response.ok) {
        toast.success('Model selection saved successfully');
        fetchSelections(selectedCategory, selectedComplexity, selectedPlanType);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to save selection');
      }
    } catch (error) {
      console.error('Failed to save selection:', error);
      toast.error('Failed to save selection');
    } finally {
      setSaving(false);
    }
  };

  const deleteSelection = async (id: string) => {
    try {
      setSaving(true);
      const response = await fetch(`/api/admin/model-selections?id=${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('Selection removed successfully');
        fetchSelections(selectedCategory, selectedComplexity, selectedPlanType);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to remove selection');
      }
    } catch (error) {
      console.error('Failed to remove selection:', error);
      toast.error('Failed to remove selection');
    } finally {
      setSaving(false);
    }
  };

  // Filter models based on search term
  const filteredModels = useMemo(() => {
    return models.filter(model => 
      model.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.canonicalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.family.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [models, searchTerm]);

  // Filter categories based on search
  const filteredCategories = useMemo(() => {
    return categories.filter(cat =>
      cat.displayName.toLowerCase().includes(categorySearch.toLowerCase()) ||
      cat.name.toLowerCase().includes(categorySearch.toLowerCase())
    );
  }, [categories, categorySearch]);

  const currentSelections = selections.filter(s => 
    s.category === selectedCategory && 
    s.complexity === selectedComplexity &&
    s.planType?.toLowerCase() === selectedPlanType
  ).sort((a, b) => (a.priority || 1) - (b.priority || 1));

  const currentComplexity = complexityLevels.find(l => l.key === selectedComplexity);
  const currentPlanType = planTypes.find(p => p.key === selectedPlanType);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Header */}
      <div className="bg-gray-900 backdrop-blur-sm border-b-2 border-gray-700 sticky top-0 z-10 shadow-2xl">
        <div className="max-w-7xl mx-auto px-6 py-5">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-600/20 rounded-xl">
              <Sparkles className="h-8 w-8 text-blue-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Manual Model Selection</h1>
              <p className="text-gray-300 text-base">Configure AI models for specific categories and complexity levels</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-gray-800 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-5 shadow-xl">
              <div className="mb-5">
                <h2 className="text-xl font-bold text-white mb-4">Categories</h2>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search categories..."
                    className="w-full pl-11 pr-4 py-3 bg-gray-900 border-2 border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                    value={categorySearch}
                    onChange={(e) => setCategorySearch(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-1 max-h-[calc(100vh-300px)] overflow-y-auto custom-scrollbar">
                {filteredCategories.map(cat => (
                  <button
                    key={cat.id}
                    onClick={() => setSelectedCategory(cat.name)}
                    className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-200 group ${
                      selectedCategory === cat.name
                        ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg shadow-blue-600/30 transform scale-[1.02]'
                        : 'bg-gray-700/50 hover:bg-gray-700 text-gray-200 hover:text-white border border-gray-600 hover:border-gray-500'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-base">{cat.displayName}</span>
                      <ChevronRight className={`h-5 w-5 transition-transform ${
                        selectedCategory === cat.name ? 'translate-x-1' : 'group-hover:translate-x-1'
                      }`} />
                    </div>
                    <div className="text-sm mt-1 font-medium">
                      <span className={selectedCategory === cat.name ? 'text-blue-200' : 'text-gray-400'}>
                        {selections.filter(s => s.category === cat.name).length} configured
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Complexity Selector */}
            <div className="bg-gray-800 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-6 shadow-xl">
              <h3 className="text-xl font-bold text-white mb-5">Complexity Level</h3>
              <div className="grid grid-cols-6 gap-3">
                {complexityLevels.map(level => (
                  <button
                    key={level.key}
                    onClick={() => setSelectedComplexity(level.key)}
                    className={`relative p-5 rounded-xl transition-all duration-300 transform hover:scale-105 ${
                      selectedComplexity === level.key
                        ? `bg-gradient-to-br ${level.color} text-white shadow-2xl scale-105 border-2 border-white/30`
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-200 hover:text-white border-2 border-gray-600'
                    }`}
                  >
                    <div className="text-3xl mb-2 filter drop-shadow-lg">{level.icon}</div>
                    <div className="text-sm font-bold">{level.label}</div>
                    {selectedComplexity === level.key && (
                      <div className="absolute inset-0 rounded-xl bg-white/20 animate-pulse" />
                    )}
                  </button>
                ))}
              </div>
              {currentComplexity && (
                <div className="mt-5 p-4 bg-gray-900 rounded-lg border border-gray-600">
                  <p className="text-base text-gray-300 text-center font-medium">
                    {currentComplexity.description}
                  </p>
                </div>
              )}
            </div>

            {/* Plan Type Selector */}
            <div className="bg-gray-800 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-6 shadow-xl">
              <h3 className="text-xl font-bold text-white mb-5">Plan Type</h3>
              <div className="grid grid-cols-7 gap-3">
                {planTypes.map(plan => (
                  <button
                    key={plan.key}
                    onClick={() => setSelectedPlanType(plan.key)}
                    className={`relative p-5 rounded-xl transition-all duration-300 transform hover:scale-105 ${
                      selectedPlanType === plan.key
                        ? `bg-gradient-to-br ${plan.color} text-white shadow-2xl scale-105 border-2 border-white/30`
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-200 hover:text-white border-2 border-gray-600'
                    }`}
                  >
                    <div className="text-2xl mb-2 filter drop-shadow-lg">{plan.icon}</div>
                    <div className="text-xs font-bold">{plan.label}</div>
                    {selectedPlanType === plan.key && (
                      <div className="absolute inset-0 rounded-xl bg-white/20 animate-pulse" />
                    )}
                  </button>
                ))}
              </div>
              {currentPlanType && (
                <div className="mt-5 p-4 bg-gray-900 rounded-lg border border-gray-600">
                  <p className="text-base text-gray-300 text-center font-medium">
                    {currentPlanType.description}
                  </p>
                </div>
              )}
            </div>

            {/* Current Selections */}
            {selectedCategory && selectedComplexity && (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-bold text-white">
                    Current Selections
                  </h3>
                  <span className="text-sm text-gray-400">
                    {currentSelections.length} model{currentSelections.length !== 1 ? 's' : ''} selected
                  </span>
                </div>
                
                {currentSelections.length === 0 ? (
                  <div className="bg-gray-800/50 rounded-xl border-2 border-gray-700 border-dashed p-8 text-center">
                    <p className="text-gray-400">No models selected for this configuration</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {currentSelections.map((selection, index) => (
                      <div 
                        key={selection.id}
                        className="bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-pink-600/20 backdrop-blur-sm rounded-xl border-2 border-blue-500/50 p-5 shadow-xl shadow-blue-500/10"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <span className="px-2 py-1 bg-blue-600/30 rounded text-sm font-bold text-blue-300">
                                Priority {selection.priority || 1}
                              </span>
                              <h4 className="text-xl font-bold text-white">
                                {selection.model.displayName}
                              </h4>
                            </div>
                            <div className="flex flex-wrap items-center gap-4">
                              <span className="px-3 py-1 bg-gray-700/50 rounded-lg text-white font-medium text-sm">
                                {selection.model.provider.name}
                              </span>
                              <div className="flex items-center gap-2 bg-yellow-500/10 px-3 py-1 rounded-lg">
                                <Zap className="h-4 w-4 text-yellow-400" />
                                <span className="text-yellow-300 font-semibold text-sm">{selection.model.speedRating}/10</span>
                              </div>
                              <div className="flex items-center gap-2 bg-purple-500/10 px-3 py-1 rounded-lg">
                                <Brain className="h-4 w-4 text-purple-400" />
                                <span className="text-purple-300 font-semibold text-sm">{selection.model.qualityRating}/10</span>
                              </div>
                              <div className="flex items-center gap-2 bg-green-500/10 px-3 py-1 rounded-lg">
                                <DollarSign className="h-4 w-4 text-green-400" />
                                <span className="text-green-300 font-semibold text-sm">
                                  ${selection.model.inputCostPer1M.toFixed(2)}/1M
                                </span>
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={() => deleteSelection(selection.id)}
                            disabled={saving}
                            className="p-2 rounded-lg bg-red-600/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 transition-all duration-200 disabled:opacity-50 hover:scale-110"
                            title="Remove selection"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Model Search and List */}
            <div className="bg-gray-800 backdrop-blur-sm rounded-xl border-2 border-gray-700 p-6 shadow-xl">
              <div className="mb-6">
                <h3 className="text-xl font-bold text-white mb-4">Available Models</h3>
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search models by name, provider, or family..."
                    className="w-full pl-12 pr-4 py-3 bg-gray-900 border-2 border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-base"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-3 max-h-[calc(100vh-500px)] overflow-y-auto custom-scrollbar">
                {filteredModels.map(model => {
                  const isSelected = currentSelections.some(s => s.model.id === model.id);
                  const canSelect = !isSelected;
                  
                  return (
                    <div
                      key={model.id}
                      className={`p-5 rounded-xl border-2 transition-all duration-200 ${
                        isSelected
                          ? 'bg-gradient-to-r from-blue-900/40 to-purple-900/40 border-blue-500 shadow-xl shadow-blue-600/20 transform scale-[1.01]'
                          : 'bg-gray-700/50 border-gray-600 hover:bg-gray-700 hover:border-gray-500 hover:shadow-lg'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <h4 className="text-lg font-bold text-white">{model.displayName}</h4>
                            {isSelected && (
                              <span className="px-3 py-1 text-xs bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold shadow-lg">
                                ✓ Selected
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-4 mt-2 text-sm">
                            <span className="text-gray-400">{model.provider.name}</span>
                            <span className="text-gray-500">•</span>
                            <span className="text-gray-400">{model.family}</span>
                          </div>
                          <div className="flex items-center gap-6 mt-3">
                            <div className="flex items-center gap-2">
                              <div className="flex items-center gap-1">
                                <Zap className="h-3 w-3 text-yellow-500" />
                                <span className="text-xs text-gray-300">Speed</span>
                              </div>
                              <div className="w-20 h-2 bg-gray-700 rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-gradient-to-r from-yellow-500 to-yellow-600"
                                  style={{ width: `${model.speedRating * 10}%` }}
                                />
                              </div>
                              <span className="text-xs text-gray-400">{model.speedRating}/10</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="flex items-center gap-1">
                                <Brain className="h-3 w-3 text-purple-500" />
                                <span className="text-xs text-gray-300">Quality</span>
                              </div>
                              <div className="w-20 h-2 bg-gray-700 rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-gradient-to-r from-purple-500 to-purple-600"
                                  style={{ width: `${model.qualityRating * 10}%` }}
                                />
                              </div>
                              <span className="text-xs text-gray-400">{model.qualityRating}/10</span>
                            </div>
                            <div className="flex items-center gap-1 text-xs">
                              <DollarSign className="h-3 w-3 text-green-500" />
                              <span className="text-gray-300">
                                ${model.inputCostPer1M.toFixed(2)}/1M in
                              </span>
                              <span className="text-gray-500">•</span>
                              <span className="text-gray-300">
                                ${model.outputCostPer1M.toFixed(2)}/1M out
                              </span>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => saveSelection(model.id)}
                          disabled={saving || !canSelect}
                          className={`ml-4 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                            isSelected
                              ? 'bg-green-600/20 text-green-400 cursor-not-allowed border border-green-500'
                              : canSelect
                              ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-600/20 hover:shadow-blue-600/30'
                              : 'bg-gray-700 text-gray-500 cursor-not-allowed'
                          } disabled:opacity-50`}
                        >
                          {isSelected ? (
                            <div className="flex items-center gap-2">
                              <Check className="h-4 w-4" />
                              Selected
                            </div>
                          ) : (
                            'Add'
                          )}
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Scrollbar Styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(31, 41, 55, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(107, 114, 128, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.5);
        }
      `}</style>
    </div>
  );
}