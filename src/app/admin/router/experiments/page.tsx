'use client'

import { useState, useEffect } from 'react'
import { 
  Beaker, Plus, Play, Pause, Square, BarChart3,
  TrendingUp, TrendingDown, Users, Target,
  Calendar, Settings, AlertCircle, CheckCircle,
  Archive, Edit3, <PERSON><PERSON>, Trash2, Eye
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { format, differenceInDays } from 'date-fns'

interface Experiment {
  id: string
  name: string
  description: string
  status: 'draft' | 'running' | 'paused' | 'completed' | 'archived'
  type: 'router_algorithm' | 'model_selection' | 'category_mapping' | 'ui_feature'
  
  // Configuration
  variants: {
    id: string
    name: string
    description: string
    weight: number // Traffic allocation percentage
    config: any
  }[]
  
  // Targeting
  targeting: {
    userSegments: string[]
    categories: string[]
    models: string[]
    conditions: any[]
  }
  
  // Metrics
  metrics: {
    primary: string
    secondary: string[]
    customEvents: string[]
  }
  
  // Schedule
  startDate: string
  endDate?: string
  duration?: number // days
  
  // Results
  results?: {
    totalUsers: number
    conversions: number
    conversionRate: number
    confidence: number
    winner?: string
    variants: Array<{
      id: string
      users: number
      conversions: number
      conversionRate: number
      uplift: number
      significance: number
    }>
  }
  
  createdAt: string
  updatedAt: string
  createdBy: string
}

export default function ExperimentsPage() {
  const [experiments, setExperiments] = useState<Experiment[]>([
    {
      id: '1',
      name: 'Advanced Router vs Simple Router',
      description: 'Test advanced model selection algorithm against simple round-robin',
      status: 'running',
      type: 'router_algorithm',
      variants: [
        {
          id: 'control',
          name: 'Current Router',
          description: 'Existing Thompson Sampling router',
          weight: 50,
          config: { algorithm: 'thompson_sampling' }
        },
        {
          id: 'treatment',
          name: 'Simple Router',
          description: 'Round-robin model selection',
          weight: 50,
          config: { algorithm: 'round_robin' }
        }
      ],
      targeting: {
        userSegments: ['free', 'plus'],
        categories: ['coding', 'general'],
        models: [],
        conditions: []
      },
      metrics: {
        primary: 'user_satisfaction',
        secondary: ['response_time', 'cost_per_query', 'success_rate'],
        customEvents: ['model_switch', 'retry_count']
      },
      startDate: '2025-01-10T00:00:00Z',
      endDate: '2025-01-24T23:59:59Z',
      duration: 14,
      results: {
        totalUsers: 2847,
        conversions: 2456,
        conversionRate: 86.3,
        confidence: 89.2,
        variants: [
          {
            id: 'control',
            users: 1423,
            conversions: 1241,
            conversionRate: 87.2,
            uplift: 0,
            significance: 0
          },
          {
            id: 'treatment',
            users: 1424,
            conversions: 1215,
            conversionRate: 85.3,
            uplift: -2.2,
            significance: 76.4
          }
        ]
      },
      createdAt: '2025-01-09T15:30:00Z',
      updatedAt: '2025-01-15T10:30:00Z',
      createdBy: 'admin'
    },
    {
      id: '2',
      name: 'GPT-4o vs Claude 3.5 for Coding',
      description: 'Compare performance of GPT-4o vs Claude 3.5 Sonnet for coding tasks',
      status: 'completed',
      type: 'model_selection',
      variants: [
        {
          id: 'gpt4o',
          name: 'GPT-4o',
          description: 'Use GPT-4o for coding prompts',
          weight: 50,
          config: { model: 'gpt-4o' }
        },
        {
          id: 'claude35',
          name: 'Claude 3.5 Sonnet',
          description: 'Use Claude 3.5 Sonnet for coding prompts',
          weight: 50,
          config: { model: 'claude-3-5-sonnet-20241022' }
        }
      ],
      targeting: {
        userSegments: ['all'],
        categories: ['coding', 'debugging'],
        models: [],
        conditions: []
      },
      metrics: {
        primary: 'code_quality_score',
        secondary: ['response_time', 'user_rating', 'follow_up_questions'],
        customEvents: ['code_execution', 'syntax_error']
      },
      startDate: '2025-01-01T00:00:00Z',
      endDate: '2025-01-08T23:59:59Z',
      duration: 7,
      results: {
        totalUsers: 1247,
        conversions: 1089,
        conversionRate: 87.3,
        confidence: 95.8,
        winner: 'claude35',
        variants: [
          {
            id: 'gpt4o',
            users: 623,
            conversions: 521,
            conversionRate: 83.6,
            uplift: 0,
            significance: 0
          },
          {
            id: 'claude35',
            users: 624,
            conversions: 568,
            conversionRate: 91.0,
            uplift: 8.9,
            significance: 95.8
          }
        ]
      },
      createdAt: '2024-12-28T12:00:00Z',
      updatedAt: '2025-01-08T23:59:59Z',
      createdBy: 'admin'
    }
  ])
  
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null)
  const [filter, setFilter] = useState<'all' | 'running' | 'completed' | 'draft'>('all')
  
  const filteredExperiments = experiments.filter(exp => 
    filter === 'all' || exp.status === filter
  )
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="w-4 h-4 text-green-400" />
      case 'paused': return <Pause className="w-4 h-4 text-yellow-400" />
      case 'completed': return <CheckCircle className="w-4 h-4 text-blue-400" />
      case 'draft': return <Edit3 className="w-4 h-4 text-gray-400" />
      case 'archived': return <Archive className="w-4 h-4 text-gray-500" />
      default: return <AlertCircle className="w-4 h-4 text-red-400" />
    }
  }
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-400 bg-green-900/20 border-green-500/30'
      case 'paused': return 'text-yellow-400 bg-yellow-900/20 border-yellow-500/30'
      case 'completed': return 'text-blue-400 bg-blue-900/20 border-blue-500/30'
      case 'draft': return 'text-gray-400 bg-gray-800/50 border-gray-600/30'
      case 'archived': return 'text-gray-500 bg-gray-800/30 border-gray-700/30'
      default: return 'text-red-400 bg-red-900/20 border-red-500/30'
    }
  }
  
  const handleStartExperiment = async (experimentId: string) => {
    // Start experiment logic
    setExperiments((prev: Experiment[]) => prev.map((exp: Experiment) => 
      exp.id === experimentId 
        ? { ...exp, status: 'running' as const, startDate: new Date().toISOString() }
        : exp
    ))
  }
  
  const handlePauseExperiment = async (experimentId: string) => {
    // Pause experiment logic
    setExperiments((prev: Experiment[]) => prev.map((exp: Experiment) => 
      exp.id === experimentId ? { ...exp, status: 'paused' as const } : exp
    ))
  }
  
  const handleStopExperiment = async (experimentId: string) => {
    // Stop experiment logic
    setExperiments((prev: Experiment[]) => prev.map((exp: Experiment) => 
      exp.id === experimentId 
        ? { ...exp, status: 'completed' as const, endDate: new Date().toISOString() }
        : exp
    ))
  }
  
  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white flex items-center gap-3">
            <Beaker className="w-8 h-8 text-green-400" />
            A/B Testing & Experiments
          </h1>
          <p className="text-gray-400 mt-2">
            Test and validate router improvements with controlled experiments
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New Experiment
        </button>
      </div>
      
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-400 text-sm">Running</span>
            <Play className="w-4 h-4 text-green-400" />
          </div>
          <div className="text-2xl font-bold text-white">
            {experiments.filter(e => e.status === 'running').length}
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-400 text-sm">Completed</span>
            <CheckCircle className="w-4 h-4 text-blue-400" />
          </div>
          <div className="text-2xl font-bold text-white">
            {experiments.filter(e => e.status === 'completed').length}
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-400 text-sm">Total Users</span>
            <Users className="w-4 h-4 text-purple-400" />
          </div>
          <div className="text-2xl font-bold text-white">
            {experiments.reduce((sum: number, e: any) => sum + (e.results?.totalUsers || 0), 0).toLocaleString()}
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-400 text-sm">Avg Confidence</span>
            <Target className="w-4 h-4 text-yellow-400" />
          </div>
          <div className="text-2xl font-bold text-white">
            {Math.round(experiments.reduce((sum: number, e: any) => sum + (e.results?.confidence || 0), 0) / experiments.length)}%
          </div>
        </div>
      </div>
      
      {/* Filters */}
      <div className="flex items-center gap-2">
        <span className="text-gray-400 text-sm">Filter:</span>
        {['all', 'running', 'completed', 'draft'].map((status) => (
          <button
            key={status}
            onClick={() => setFilter(status as any)}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              filter === status
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </button>
        ))}
      </div>
      
      {/* Experiments List */}
      <div className="space-y-4">
        {filteredExperiments.map((experiment) => (
          <motion.div
            key={experiment.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-white">{experiment.name}</h3>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium border flex items-center gap-1 ${getStatusColor(experiment.status)}`}>
                      {getStatusIcon(experiment.status)}
                      {experiment.status.charAt(0).toUpperCase() + experiment.status.slice(1)}
                    </div>
                  </div>
                  <p className="text-gray-400 mb-3">{experiment.description}</p>
                  
                  <div className="flex items-center gap-6 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {format(new Date(experiment.startDate), 'MMM d')}
                      {experiment.endDate && ` - ${format(new Date(experiment.endDate), 'MMM d')}`}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {experiment.results?.totalUsers.toLocaleString() || 0} users
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="w-4 h-4" />
                      {experiment.variants.length} variants
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setSelectedExperiment(experiment)}
                    className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  
                  {experiment.status === 'draft' && (
                    <button
                      onClick={() => handleStartExperiment(experiment.id)}
                      className="p-2 text-green-400 hover:text-white hover:bg-green-600 rounded-lg transition-colors"
                    >
                      <Play className="w-4 h-4" />
                    </button>
                  )}
                  
                  {experiment.status === 'running' && (
                    <>
                      <button
                        onClick={() => handlePauseExperiment(experiment.id)}
                        className="p-2 text-yellow-400 hover:text-white hover:bg-yellow-600 rounded-lg transition-colors"
                      >
                        <Pause className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleStopExperiment(experiment.id)}
                        className="p-2 text-red-400 hover:text-white hover:bg-red-600 rounded-lg transition-colors"
                      >
                        <Square className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  
                  <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
                    <Settings className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              {/* Results Preview */}
              {experiment.results && (
                <div className="border-t border-gray-700 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {experiment.results.conversionRate.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-400">Conversion Rate</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {experiment.results.confidence.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-400">Confidence</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1">
                        {experiment.results.winner ? (
                          <>
                            <TrendingUp className="w-4 h-4 text-green-400" />
                            <span className="text-lg font-bold text-green-400">
                              +{experiment.results.variants.find(v => v.id === experiment.results?.winner)?.uplift.toFixed(1)}%
                            </span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-gray-400">No Winner</span>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">Best Variant</div>
                    </div>
                    
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">
                        {experiment.results.totalUsers.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-400">Total Users</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
      
      {/* Empty State */}
      {filteredExperiments.length === 0 && (
        <div className="text-center py-12">
          <Beaker className="w-12 h-12 text-gray-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No experiments found</h3>
          <p className="text-gray-500 mb-4">
            {filter === 'all' ? 'Create your first experiment to start testing' : `No ${filter} experiments`}
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            Create Experiment
          </button>
        </div>
      )}
      
      {/* Create Modal Placeholder */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4">
            <h2 className="text-xl font-bold text-white mb-4">Create New Experiment</h2>
            <p className="text-gray-400 mb-6">
              Experiment creation form would go here with options for:
            </p>
            <ul className="list-disc list-inside text-gray-400 space-y-1 mb-6">
              <li>Experiment name and description</li>
              <li>Experiment type (router, model, UI feature)</li>
              <li>Variant configurations</li>
              <li>Target audience and conditions</li>
              <li>Success metrics and goals</li>
              <li>Duration and scheduling</li>
            </ul>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Create Experiment
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}