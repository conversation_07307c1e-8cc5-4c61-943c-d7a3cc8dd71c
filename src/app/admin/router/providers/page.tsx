'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { 
  Plus, Search, Edit3, Trash2, Save, X, Globe, 
  Key, FileText, CheckCircle, XCircle, RefreshCw,
  Link as LinkIcon, Shield, AlertCircle
} from 'lucide-react'

interface AIProvider {
  id: string
  slug: string
  name: string
  description?: string
  websiteUrl?: string
  docsUrl?: string
  apiDocsUrl?: string
  statusPageUrl?: string
  logoUrl?: string
  auth_type?: string
  auth_config?: any
  base_url?: string
  features?: string[]
  isActive: boolean
  isVerified: boolean
  lastVerifiedAt?: string
  modelCount?: number
}

export default function ProviderManagementPage() {
  const [providers, setProviders] = useState<AIProvider[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [editingProvider, setEditingProvider] = useState<AIProvider | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState<Partial<AIProvider>>({})
  const [savingProvider, setSavingProvider] = useState(false)
  const [deletingProvider, setDeletingProvider] = useState<string | null>(null)

  useEffect(() => {
    fetchProviders()
  }, [])

  const fetchProviders = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/providers')
      const data = await response.json()
      setProviders(data.providers || [])
    } catch (error) {
      console.error('Failed to fetch providers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProvider = () => {
    setIsCreating(true)
    setEditingProvider(null)
    setFormData({
      slug: '',
      name: '',
      description: '',
      websiteUrl: '',
      docsUrl: '',
      apiDocsUrl: '',
      base_url: '',
      auth_type: 'api_key',
      isActive: true,
      isVerified: false
    })
  }

  const handleEditProvider = (provider: AIProvider) => {
    setEditingProvider(provider)
    setIsCreating(false)
    setFormData(provider)
  }

  const handleSaveProvider = async () => {
    setSavingProvider(true)
    try {
      const url = isCreating ? '/api/admin/providers' : `/api/admin/providers/${editingProvider?.id}`
      const method = isCreating ? 'POST' : 'PUT'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (!response.ok) throw new Error('Failed to save provider')
      
      await fetchProviders()
      setEditingProvider(null)
      setIsCreating(false)
      setFormData({})
    } catch (error) {
      console.error('Failed to save provider:', error)
      alert('Failed to save provider. Please try again.')
    } finally {
      setSavingProvider(false)
    }
  }

  const handleDeleteProvider = async (providerId: string) => {
    const provider = providers.find(p => p.id === providerId)
    if (!confirm(`Are you sure you want to delete ${provider?.name}? This will affect all models associated with this provider.`)) {
      return
    }
    
    setDeletingProvider(providerId)
    try {
      const response = await fetch(`/api/admin/providers/${providerId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) throw new Error('Failed to delete provider')
      
      await fetchProviders()
    } catch (error) {
      console.error('Failed to delete provider:', error)
      alert('Failed to delete provider. Please try again.')
    } finally {
      setDeletingProvider(null)
    }
  }

  const updateFormField = (field: string, value: any) => {
    setFormData((prev: Partial<AIProvider>) => ({
      ...prev,
      [field]: value
    }))
  }

  const filteredProviders = providers.filter(provider => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        provider.name?.toLowerCase().includes(query) ||
        provider.slug?.toLowerCase().includes(query) ||
        provider.description?.toLowerCase().includes(query)
      )
    }
    return true
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-white">Provider Management</h1>
          <p className="text-gray-300 mt-1">
            Configure AI providers and their authentication settings
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={fetchProviders}
            className="flex items-center gap-2 px-4 py-2 bg-gray-800 border rounded-lg hover:bg-gray-700"
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button
            onClick={handleCreateProvider}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Provider
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-gray-800 p-4 rounded-lg border">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search providers..."
            className="w-full pl-10 pr-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Providers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredProviders.map(provider => (
          <div key={provider.id} className="bg-gray-800 rounded-lg border p-4 hover:border-gray-600 transition-colors">
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center gap-3">
                {provider.logoUrl && (
                  <Image src={provider.logoUrl} alt={provider.name} width={32} height={32} className="w-8 h-8 rounded" />
                )}
                <div>
                  <h3 className="font-medium text-white">{provider.name}</h3>
                  <p className="text-sm text-gray-500">{provider.slug}</p>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleEditProvider(provider)}
                  className="p-1 text-gray-300 hover:text-blue-600"
                  title="Edit provider"
                >
                  <Edit3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteProvider(provider.id)}
                  disabled={deletingProvider === provider.id}
                  className="p-1 text-gray-300 hover:text-red-600 disabled:opacity-50"
                  title="Delete provider"
                >
                  {deletingProvider === provider.id ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-600 border-t-transparent" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            {provider.description && (
              <p className="text-sm text-gray-400 mb-3">{provider.description}</p>
            )}

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                  provider.isActive ? 'bg-green-800 text-green-300' : 'bg-red-800 text-red-300'
                }`}>
                  {provider.isActive ? (
                    <>
                      <CheckCircle className="w-3 h-3" />
                      Active
                    </>
                  ) : (
                    <>
                      <XCircle className="w-3 h-3" />
                      Inactive
                    </>
                  )}
                </span>
                {provider.isVerified && (
                  <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-800 text-blue-300 rounded-full text-xs font-medium">
                    <Shield className="w-3 h-3" />
                    Verified
                  </span>
                )}
              </div>

              {provider.modelCount !== undefined && (
                <p className="text-sm text-gray-500">
                  {provider.modelCount} models
                </p>
              )}

              <div className="flex gap-2 text-xs">
                {provider.websiteUrl && (
                  <a href={provider.websiteUrl} target="_blank" rel="noopener noreferrer" 
                     className="text-blue-400 hover:text-blue-300 flex items-center gap-1">
                    <Globe className="w-3 h-3" />
                    Website
                  </a>
                )}
                {provider.docsUrl && (
                  <a href={provider.docsUrl} target="_blank" rel="noopener noreferrer"
                     className="text-blue-400 hover:text-blue-300 flex items-center gap-1">
                    <FileText className="w-3 h-3" />
                    Docs
                  </a>
                )}
                {provider.apiDocsUrl && (
                  <a href={provider.apiDocsUrl} target="_blank" rel="noopener noreferrer"
                     className="text-blue-400 hover:text-blue-300 flex items-center gap-1">
                    <Key className="w-3 h-3" />
                    API
                  </a>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Edit/Create Modal */}
      {(editingProvider || isCreating) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold">
                  {isCreating ? 'Create New Provider' : 'Edit Provider'}
                </h2>
                <button
                  onClick={() => {
                    setEditingProvider(null)
                    setIsCreating(false)
                    setFormData({})
                  }}
                  className="text-gray-400 hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Slug <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.slug || ''}
                      onChange={(e) => updateFormField('slug', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="openai"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={formData.name || ''}
                      onChange={(e) => updateFormField('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="OpenAI"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description || ''}
                    onChange={(e) => updateFormField('description', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                    placeholder="AI provider description..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Base URL
                    </label>
                    <input
                      type="url"
                      value={formData.base_url || ''}
                      onChange={(e) => updateFormField('base_url', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://api.openai.com/v1"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Auth Type
                    </label>
                    <select
                      value={formData.auth_type || 'api_key'}
                      onChange={(e) => updateFormField('auth_type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="api_key">API Key</option>
                      <option value="oauth2">OAuth2</option>
                      <option value="bearer">Bearer Token</option>
                      <option value="custom">Custom</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    URLs
                  </label>
                  <input
                    type="url"
                    value={formData.websiteUrl || ''}
                    onChange={(e) => updateFormField('websiteUrl', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Website URL"
                  />
                  <input
                    type="url"
                    value={formData.docsUrl || ''}
                    onChange={(e) => updateFormField('docsUrl', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Documentation URL"
                  />
                  <input
                    type="url"
                    value={formData.apiDocsUrl || ''}
                    onChange={(e) => updateFormField('apiDocsUrl', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-600 rounded-lg bg-gray-700 text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="API Documentation URL"
                  />
                </div>

                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.isActive !== false}
                      onChange={(e) => updateFormField('isActive', e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Provider is active</span>
                  </label>
                  
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.isVerified === true}
                      onChange={(e) => updateFormField('isVerified', e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Provider is verified</span>
                  </label>
                </div>
              </div>
            </div>
            
            <div className="p-6 border-t bg-gray-800">
              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setEditingProvider(null)
                    setIsCreating(false)
                    setFormData({})
                  }}
                  className="px-4 py-2 border rounded-lg hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveProvider}
                  disabled={savingProvider}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {savingProvider ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      {isCreating ? 'Create Provider' : 'Save Changes'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}