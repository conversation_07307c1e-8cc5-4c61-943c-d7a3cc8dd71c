/**
 * Admin Layout
 * Shared layout for all admin pages with sidebar navigation
 */

'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin/router',
    icon: '🎯',
    description: 'System overview'
  },
  {
    name: 'Categories',
    href: '/admin/router/categories',
    icon: '📁',
    description: 'Manage prompt categories'
  },
  {
    name: 'Model Mappings',
    href: '/admin/router/mappings',
    icon: '🔗',
    description: 'Configure model assignments'
  },
  {
    name: 'Model Selection',
    href: '/admin/model-selection',
    icon: '🎛️',
    description: 'Manual model selection per category'
  },
  {
    name: 'Model Management',
    href: '/admin/router/models',
    icon: '🤖',
    description: 'Manage AI models & provider partnerships'
  },
  {
    name: 'Provider Management',
    href: '/admin/router/providers',
    icon: '🌐',
    description: 'Configure AI providers'
  },
  {
    name: 'Logs Viewer',
    href: '/admin/router/logs',
    icon: '📜',
    description: 'View all prompts & responses'
  },
  {
    name: 'Conversations',
    href: '/admin/router/logs/conversations',
    icon: '💬',
    description: 'Full conversation history'
  },
  {
    name: 'Testing Playground',
    href: '/admin/router/playground',
    icon: '🧪',
    description: 'Test router decisions'
  },
  {
    name: 'Analytics',
    href: '/admin/router/analytics',
    icon: '📊',
    description: 'Performance metrics'
  },
  {
    name: 'Feedback',
    href: '/admin/router/feedback',
    icon: '💬',
    description: 'User satisfaction data'
  },
  {
    name: 'Prompt Improvements',
    href: '/admin/router/prompt-improvements',
    icon: '✨',
    description: 'AI-powered suggestions'
  },
  {
    name: 'Thompson Sampling',
    href: '/admin/router/sampling',
    icon: '🎰',
    description: 'Exploration controls'
  },
  {
    name: 'Experiments',
    href: '/admin/router/experiments',
    icon: '🔬',
    description: 'A/B testing'
  },
  {
    name: 'Configuration',
    href: '/admin/router/config',
    icon: '⚙️',
    description: 'System settings'
  }
];

function NavItem({ item, isActive }: { item: typeof navigation[0], isActive: boolean }) {
  return (
    <Link
      href={item.href}
      className={`
        group flex items-center px-3 py-2 text-sm font-medium rounded-lg
        transition-all duration-200 ease-in-out
        ${isActive 
          ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/20' 
          : 'text-gray-300 hover:bg-gray-800 hover:text-white'
        }
      `}
    >
      <span className="mr-3 text-lg">{item.icon}</span>
      <div className="flex-1">
        <div>{item.name}</div>
        <div className={`text-xs ${isActive ? 'text-blue-300' : 'text-gray-500'}`}>
          {item.description}
        </div>
      </div>
      {isActive && (
        <div className="w-1 h-8 bg-gray-800 rounded-full ml-2" />
      )}
    </Link>
  );
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Top Header */}
      <header className="fixed top-0 left-0 right-0 h-16 bg-gray-800 border-b border-gray-700 z-40">
        <div className="flex items-center justify-between h-full px-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold">
              <span className="text-blue-400">🎯</span> Intelligent Router Admin
            </h1>
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-400">
              <span>/</span>
              <span>{navigation.find(n => pathname === n.href)?.name || 'Dashboard'}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Export Config
            </button>
            <div className="text-sm text-gray-400">
              <span className="text-green-400">●</span> System Healthy
            </div>
          </div>
        </div>
      </header>

      <div className="flex pt-16">
        {/* Sidebar */}
        <aside className="fixed left-0 top-16 bottom-0 w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto">
          <nav className="p-4 space-y-1">
            {navigation.map((item) => (
              <NavItem 
                key={item.href} 
                item={item} 
                isActive={pathname === item.href}
              />
            ))}
          </nav>
          
          {/* Quick Stats */}
          <div className="mx-4 mt-8 p-4 bg-gray-900 rounded-lg">
            <h3 className="text-sm font-medium text-gray-400 mb-3">Quick Stats</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Categories</span>
                <span className="font-medium">25</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Models</span>
                <span className="font-medium">182</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Success Rate</span>
                <span className="font-medium text-green-400">87.3%</span>
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 ml-64">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}