import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { isLocalhostDebugRequest, createMockSession, logLocalhostDebug } from '@/lib/debug';
import { deduplicateMessages, hasDuplicateMessages, mergeDuplicateResponses } from '@/lib/utils/message-deduplication';
import { Message as AppMessage, MessageRole } from '@/types';

// POST /api/conversations/[id]/deduplicate - Clean up duplicate messages
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  
  try {
    let session = await auth();
    
    // Check for localhost debugging
    if (!session?.user?.id && isLocalhostDebugRequest(request)) {
      logLocalhostDebug('Using mock session for conversation deduplication', { conversationId: id });
      session = createMockSession();
    }
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify conversation ownership
    const conversation = await prisma.conversation.findFirst({
      where: {
        id,
        userId: session.user.id,
        deletedAt: null,
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
        },
      },
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Convert Prisma messages to app messages for deduplication
    const appMessages: AppMessage[] = conversation.messages.map(msg => ({
      id: msg.id,
      conversationId: msg.conversationId,
      role: msg.role as MessageRole,
      content: msg.content,
      model: msg.model || undefined,
      provider: msg.provider || undefined,
      metadata: msg.metadata as any,
      promptTokens: msg.promptTokens,
      completionTokens: msg.completionTokens,
      totalTokens: msg.totalTokens,
      cost: msg.cost ? Number(msg.cost) : undefined,
      createdAt: msg.createdAt,
      updatedAt: msg.createdAt, // Use createdAt as fallback since updatedAt may not exist
      editedAt: msg.editedAt || undefined,
      reasoningContent: msg.reasoningContent || undefined,
    }));

    // Check if deduplication is needed
    if (!hasDuplicateMessages(appMessages)) {
      return NextResponse.json({
        message: 'No duplicate messages found',
        processed: 0,
        removed: 0,
      });
    }

    // Perform deduplication
    const dedupedMessages = deduplicateMessages(appMessages);
    const removedCount = appMessages.length - dedupedMessages.length;

    if (removedCount > 0) {
      // Get IDs of messages to keep
      const keepIds = new Set(dedupedMessages.map(m => m.id));
      
      // Get IDs of messages to remove
      const removeIds = appMessages
        .filter(m => !keepIds.has(m.id))
        .map(m => m.id);

      console.log(`[Deduplication] Removing ${removeIds.length} duplicate messages from conversation ${id}`);

      // Delete the duplicate messages
      await prisma.message.deleteMany({
        where: {
          id: { in: removeIds },
          conversationId: id,
        },
      });

      // Update conversation message count
      await prisma.conversation.update({
        where: { id },
        data: {
          messageCount: dedupedMessages.length,
        },
      });
    }

    // Also check for messages that need content correction (duplicated within content)
    const mergedMessages = mergeDuplicateResponses(dedupedMessages);
    let correctedCount = 0;

    for (const msg of mergedMessages) {
      if (msg.metadata?.wasDeduplicated) {
        // Update the message content to remove internal duplication
        await prisma.message.update({
          where: { id: msg.id },
          data: {
            content: msg.content,
            metadata: {
              ...(msg.metadata as any),
              correctedAt: new Date().toISOString(),
              correctedBy: 'deduplication_api',
            },
          },
        });
        correctedCount++;
      }
    }

    return NextResponse.json({
      message: 'Deduplication completed successfully',
      processed: appMessages.length,
      removed: removedCount,
      corrected: correctedCount,
      remaining: dedupedMessages.length,
    });

  } catch (error) {
    console.error('Error deduplicating conversation:', error);
    return NextResponse.json(
      { error: 'Failed to deduplicate conversation' },
      { status: 500 }
    );
  }
}

// GET /api/conversations/[id]/deduplicate - Check if conversation needs deduplication
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  
  try {
    let session = await auth();
    
    // Check for localhost debugging
    if (!session?.user?.id && isLocalhostDebugRequest(request)) {
      logLocalhostDebug('Using mock session for deduplication check', { conversationId: id });
      session = createMockSession();
    }
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const conversation = await prisma.conversation.findFirst({
      where: {
        id,
        userId: session.user.id,
        deletedAt: null,
      },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
          select: {
            id: true,
            role: true,
            content: true,
            createdAt: true,
          },
        },
      },
    });

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Convert to app messages for checking
    const appMessages: AppMessage[] = conversation.messages.map(msg => ({
      id: msg.id,
      conversationId: id,
      role: msg.role as MessageRole,
      content: msg.content,
      createdAt: msg.createdAt,
      updatedAt: msg.createdAt,
    } as AppMessage));

    const hasDuplicates = hasDuplicateMessages(appMessages);
    
    if (hasDuplicates) {
      // Count how many would be removed
      const dedupedMessages = deduplicateMessages(appMessages);
      const duplicateCount = appMessages.length - dedupedMessages.length;
      
      return NextResponse.json({
        hasDuplicates: true,
        totalMessages: appMessages.length,
        duplicateCount,
        recommendation: 'Run deduplication to clean up duplicate messages',
      });
    }

    return NextResponse.json({
      hasDuplicates: false,
      totalMessages: appMessages.length,
      duplicateCount: 0,
      recommendation: 'No deduplication needed',
    });

  } catch (error) {
    console.error('Error checking for duplicates:', error);
    return NextResponse.json(
      { error: 'Failed to check for duplicates' },
      { status: 500 }
    );
  }
}