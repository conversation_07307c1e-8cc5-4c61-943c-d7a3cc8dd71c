import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch user preferences
export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { 
        preferences: true,
        name: true,
        theme: true,
        language: true,
        timezone: true
      }
    });

    // Default preferences with fallbacks from user profile
    const userName = user?.name || '';
    const firstName = userName?.split(' ')[0] || '';
    
    const defaultPreferences = {
      profile: {
        name: userName,
        email: session.user.email || '',
        bio: '',
        avatar: session.user.image || '',
        preferredTopics: []
      },
      personalization: {
        preferredName: firstName, // Show current name as fallback
        personalContext: '',
        personality: 'balanced',
        formality: 'casual',
        interests: '',
        profession: '',
        goals: '',
        communicationStyle: 'concise',
        humorLevel: 'moderate',
        technicalLevel: 'intermediate'
      },
      preferences: {
        theme: user?.theme || 'dark',
        language: user?.language || 'en',
        timezone: user?.timezone || 'UTC',
        messageStyle: 'standard'
      },
      notifications: {
        emailNotifications: true,
        browserNotifications: false,
        marketingEmails: false,
        productUpdates: true,
        agentLaunchNotifications: true
      },
      privacy: {
        shareUsageData: false,
        publicProfile: false,
        conversationHistory: true
      }
    };

    // Merge saved preferences with defaults
    const savedPreferences = (user?.preferences as any) || {};
    const preferences = {
      ...defaultPreferences,
      ...savedPreferences,
      profile: {
        ...defaultPreferences.profile,
        ...(savedPreferences.profile || {})
      },
      personalization: {
        ...defaultPreferences.personalization,
        ...(savedPreferences.personalization || {})
      },
      preferences: {
        ...defaultPreferences.preferences,
        ...(savedPreferences.preferences || {})
      },
      notifications: {
        ...defaultPreferences.notifications,
        ...(savedPreferences.notifications || {})
      },
      privacy: {
        ...defaultPreferences.privacy,
        ...(savedPreferences.privacy || {})
      }
    };

    return NextResponse.json({ preferences });
  } catch (error) {
    console.error('Error fetching preferences:', error);
    return NextResponse.json(
      { error: 'Failed to fetch preferences' },
      { status: 500 }
    );
  }
}

// PUT - Update user preferences
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { preferences } = await request.json();
    
    if (!preferences) {
      return NextResponse.json(
        { error: 'No preferences provided' },
        { status: 400 }
      );
    }

    // Update user preferences
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: { 
        preferences: preferences,
        // Also update individual fields if they're set
        ...(preferences.profile?.name && { name: preferences.profile.name }),
        ...(preferences.preferences?.theme && { theme: preferences.preferences.theme }),
        ...(preferences.preferences?.language && { language: preferences.preferences.language }),
        ...(preferences.preferences?.timezone && { timezone: preferences.preferences.timezone })
      },
      select: { 
        id: true, 
        preferences: true,
        name: true,
        theme: true,
        language: true,
        timezone: true
      }
    });

    return NextResponse.json({ 
      success: true,
      preferences: updatedUser.preferences 
    });
  } catch (error) {
    console.error('Error updating preferences:', error);
    return NextResponse.json(
      { error: 'Failed to update preferences' },
      { status: 500 }
    );
  }
}