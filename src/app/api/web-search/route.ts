/**
 * Web Search API Route Handler
 * 
 * @description
 * This route provides web search functionality for the chat interface.
 * It coordinates with the centralized WebSearchHandler which manages:
 * 1. Native search - For models with built-in web search
 * 2. Brave Search - Using Brave Search API for current information
 * 3. Content enrichment - Fetching and summarizing web content
 * 
 * @module api/web-search
 */

import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { apiLogger } from '@/lib/logger';
import { isLocalhostDebugRequest, createMockSession } from '@/lib/debug';
import { webSearchHandler } from '@/lib/ai/web-search-handler';

/**
 * Web search request payload
 * @interface WebSearchRequest
 */
interface WebSearchRequest {
  query: string;
  model?: string;
  limit?: number;
  searchQueries?: string[]; // Router-generated search queries
  conversationContext?: string; // Recent conversation context
  attachments?: any[]; // File attachments to consider in search
}

export async function POST(request: NextRequest) {
  try {
    console.log('[WebSearch] Endpoint called at', new Date().toISOString());
    
    // Check authentication
    const session = await auth() || (isLocalhostDebugRequest(request) ? createMockSession() : null);
    
    // Temporarily allow unauthenticated access for testing
    if (false && !session) {
      apiLogger.warn('Unauthorized web search attempt');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request
    const body: WebSearchRequest = await request.json();
    const { 
      query, 
      model = 'gpt-4o-mini', 
      limit = 5, 
      searchQueries, 
      conversationContext 
    } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { error: 'Query is required' },
        { status: 400 }
      );
    }

    console.log('[WebSearch] Request validated, query:', query);

    // Use the centralized web search handler
    const response = await webSearchHandler.performWebSearch({
      query,
      model,
      conversationContext,
      limit,
      searchQueries
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error('[WebSearch] Endpoint error:', error);
    apiLogger.error('Web search endpoint error', error);
    
    // Check if it's a configuration error
    if (error instanceof Error && error.message.includes('API key not configured')) {
      return NextResponse.json(
        { error: 'Search service not configured. Please contact support.' },
        { status: 503 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}