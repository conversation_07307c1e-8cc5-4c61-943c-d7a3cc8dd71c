/**
 * Admin API for managing manual model selections
 * GET /api/admin/model-selections - Get all manual model selections
 * POST /api/admin/model-selections - Create/update manual model selection
 * 
 * NO AUTH FOR NOW - Will add later
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { apiLogger } from '@/lib/logger';
import { manualSelectionManager } from '@/lib/ai/router/manual-selections';
import crypto from 'crypto';

// Type for the result of the SQL query with model details
interface ManualModelSelectionWithDetails {
  id: string;
  category: string;
  complexity: string;
  plan_type: string;
  priority: number;
  model_id: string;
  created_at: Date;
  updated_at: Date;
  canonicalName: string;
  displayName: string;
  family: string;
  generation: string;
  speedRating: number;
  qualityRating: number;
  inputCostPer1M: number;
  outputCostPer1M: number;
  extendedMetadata: any;
  providerName: string;
}

// GET /api/admin/model-selections
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category');
    const complexity = searchParams.get('complexity');
    const planType = searchParams.get('planType');

    // Build where clause
    let whereClause: any = {};
    if (category) {
      whereClause.category = category;
    }
    if (complexity) {
      whereClause.complexity = complexity.toUpperCase();
    }
    if (planType) {
      whereClause.plan_type = planType.toUpperCase();
    }

    // Get manual selections with model details
    let selections: ManualModelSelectionWithDetails[];
    if (category && complexity && planType) {
      selections = await prisma.$queryRaw<ManualModelSelectionWithDetails[]>`
        SELECT 
          mms.id,
          mms.category,
          mms.complexity,
          mms.plan_type,
          mms.priority,
          mms.model_id,
          mms.created_at,
          mms.updated_at,
          m.canonicalName,
          m.displayName,
          m.family,
          m.generation,
          m.speedRating,
          m.qualityRating,
          m.inputCostPer1M,
          m.outputCostPer1M,
          m.extendedMetadata,
          p.name as providerName
        FROM manual_model_selections mms
        JOIN Models m ON CAST(mms.model_id AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_unicode_ci = m.id
        JOIN Providers p ON m.providerId = p.id
        WHERE mms.category = ${category} 
          AND mms.complexity = ${complexity.toUpperCase()}
          AND mms.plan_type = ${planType.toUpperCase()}
        ORDER BY mms.category, mms.complexity, mms.plan_type, mms.priority
      `;
    } else if (category && complexity) {
      selections = await prisma.$queryRaw<ManualModelSelectionWithDetails[]>`
        SELECT 
          mms.id,
          mms.category,
          mms.complexity,
          mms.plan_type,
          mms.priority,
          mms.model_id,
          mms.created_at,
          mms.updated_at,
          m.canonicalName,
          m.displayName,
          m.family,
          m.generation,
          m.speedRating,
          m.qualityRating,
          m.inputCostPer1M,
          m.outputCostPer1M,
          m.extendedMetadata,
          p.name as providerName
        FROM manual_model_selections mms
        JOIN Models m ON CAST(mms.model_id AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_unicode_ci = m.id
        JOIN Providers p ON m.providerId = p.id
        WHERE mms.category = ${category} AND mms.complexity = ${complexity.toUpperCase()}
        ORDER BY mms.category, mms.complexity, mms.plan_type, mms.priority
      `;
    } else if (category) {
      selections = await prisma.$queryRaw<ManualModelSelectionWithDetails[]>`
        SELECT 
          mms.id,
          mms.category,
          mms.complexity,
          mms.plan_type,
          mms.priority,
          mms.model_id,
          mms.created_at,
          mms.updated_at,
          m.canonicalName,
          m.displayName,
          m.family,
          m.generation,
          m.speedRating,
          m.qualityRating,
          m.inputCostPer1M,
          m.outputCostPer1M,
          m.extendedMetadata,
          p.name as providerName
        FROM manual_model_selections mms
        JOIN Models m ON CAST(mms.model_id AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_unicode_ci = m.id
        JOIN Providers p ON m.providerId = p.id
        WHERE mms.category = ${category}
        ORDER BY mms.category, mms.complexity, mms.plan_type, mms.priority
      `;
    } else {
      selections = await prisma.$queryRaw<ManualModelSelectionWithDetails[]>`
        SELECT 
          mms.id,
          mms.category,
          mms.complexity,
          mms.plan_type,
          mms.priority,
          mms.model_id,
          mms.created_at,
          mms.updated_at,
          m.canonicalName,
          m.displayName,
          m.family,
          m.generation,
          m.speedRating,
          m.qualityRating,
          m.inputCostPer1M,
          m.outputCostPer1M,
          m.extendedMetadata,
          p.name as providerName
        FROM manual_model_selections mms
        JOIN Models m ON CAST(mms.model_id AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_unicode_ci = m.id
        JOIN Providers p ON m.providerId = p.id
        ORDER BY mms.category, mms.complexity, mms.plan_type, mms.priority
      `;
    }

    // Format selections for admin UI
    const formattedSelections = selections.map(sel => {
      const categoryScores = sel.extendedMetadata ? 
        (sel.extendedMetadata as any)?.categoryScores || {} : {};
      const categoryScore = categoryScores[sel.category]?.score || 0;

      return {
        id: sel.id,
        category: sel.category,
        complexity: sel.complexity.toLowerCase(),
        planType: sel.plan_type?.toLowerCase() || 'all',
        priority: sel.priority || 1,
        model: {
          id: sel.model_id,
          canonicalName: sel.canonicalName,
          displayName: sel.displayName,
          family: sel.family,
          generation: sel.generation,
          speedRating: sel.speedRating,
          qualityRating: sel.qualityRating,
          inputCostPer1M: Number(sel.inputCostPer1M),
          outputCostPer1M: Number(sel.outputCostPer1M),
          categoryScore,
          provider: {
            name: sel.providerName
          }
        },
        createdAt: sel.created_at,
        updatedAt: sel.updated_at
      };
    });

    return NextResponse.json({
      selections: formattedSelections,
      total: formattedSelections.length
    });
    
  } catch (error) {
    apiLogger.error('Failed to fetch model selections', error);
    return NextResponse.json(
      { error: 'Failed to fetch model selections' },
      { status: 500 }
    );
  }
}

// POST /api/admin/model-selections
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { category, complexity, modelId, planType = 'ALL' } = body;

    // Validate required fields
    if (!category || !complexity || !modelId) {
      return NextResponse.json(
        { error: 'Missing required fields: category, complexity, modelId' },
        { status: 400 }
      );
    }

    // Validate complexity enum
    const validComplexities = ['SIMPLE', 'STANDARD', 'COMPLEX', 'DIFFICULT', 'EXPERT', 'ULTRA'];
    if (!validComplexities.includes(complexity.toUpperCase())) {
      return NextResponse.json(
        { error: `Invalid complexity. Must be one of: ${validComplexities.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate plan type enum
    const validPlanTypes = ['ALL', 'FREE', 'FREEMIUM', 'PLUS', 'ADVANCED', 'MAX', 'ENTERPRISE'];
    if (!validPlanTypes.includes(planType.toUpperCase())) {
      return NextResponse.json(
        { error: `Invalid plan type. Must be one of: ${validPlanTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Verify model exists
    const model = await prisma.models.findUnique({
      where: { id: modelId },
      select: { id: true, displayName: true, isEnabled: true }
    });

    if (!model) {
      return NextResponse.json(
        { error: 'Model not found' },
        { status: 404 }
      );
    }

    if (!model.isEnabled) {
      return NextResponse.json(
        { error: 'Cannot select disabled model' },
        { status: 400 }
      );
    }

    // Check if this model is already selected
    const existingSelection = await prisma.$queryRaw<{count: bigint}[]>`
      SELECT COUNT(*) as count 
      FROM manual_model_selections 
      WHERE category = ${category} 
        AND complexity = ${complexity.toUpperCase()}
        AND plan_type = ${planType.toUpperCase()}
        AND model_id = ${modelId}
    `;
    
    if (Number(existingSelection[0]?.count || 0n) > 0) {
      return NextResponse.json(
        { error: 'This model is already selected for this configuration' },
        { status: 400 }
      );
    }
    
    // Get the next priority for this category/complexity/plan
    const existingCount = await prisma.$queryRaw<{count: bigint}[]>`
      SELECT COUNT(*) as count 
      FROM manual_model_selections 
      WHERE category = ${category} 
        AND complexity = ${complexity.toUpperCase()}
        AND plan_type = ${planType.toUpperCase()}
    `;
    
    const nextPriority = Number(existingCount[0]?.count || 0n) + 1;
    
    // No limit on number of selections - you can add as many as you want
    
    // Generate a unique ID for the new selection
    const newId = crypto.randomUUID();
    
    // Insert new manual selection
    const selection = await prisma.$queryRaw`
      INSERT INTO manual_model_selections (id, category, complexity, plan_type, priority, model_id, created_at, updated_at)
      VALUES (${newId}, ${category}, ${complexity.toUpperCase()}, ${planType.toUpperCase()}, ${nextPriority}, ${modelId}, NOW(), NOW())
    `;

    // Get the newly inserted selection with model details
    const updatedSelection = await prisma.$queryRaw<ManualModelSelectionWithDetails[]>`
      SELECT 
        mms.id,
        mms.category,
        mms.complexity,
        mms.plan_type,
        mms.priority,
        mms.model_id,
        mms.created_at,
        mms.updated_at,
        m.canonicalName,
        m.displayName,
        m.family,
        m.generation,
        m.speedRating,
        m.qualityRating,
        m.inputCostPer1M,
        m.outputCostPer1M,
        m.extendedMetadata,
        p.name as providerName
      FROM manual_model_selections mms
      JOIN Models m ON CAST(mms.model_id AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_unicode_ci = m.id
      JOIN Providers p ON m.providerId = p.id
      WHERE mms.id = ${newId}
      LIMIT 1
    `;

    // Invalidate cache for this selection
    await manualSelectionManager.invalidateSelection(category, complexity, planType);

    apiLogger.info('Manual model selection saved', {
      category,
      complexity,
      planType,
      modelId,
      modelName: model.displayName
    });

    return NextResponse.json({
      success: true,
      selection: updatedSelection[0] || null
    });
    
  } catch (error) {
    apiLogger.error('Failed to save model selection', error);
    return NextResponse.json(
      { error: 'Failed to save model selection' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/model-selections
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    const category = searchParams.get('category');
    const complexity = searchParams.get('complexity');
    const planType = searchParams.get('planType') || 'ALL';

    if (id) {
      // First get the selection details before deleting
      const selection = await prisma.$queryRaw<{category: string, complexity: string, plan_type: string}[]>`
        SELECT category, complexity, plan_type 
        FROM manual_model_selections 
        WHERE id = ${id}
        LIMIT 1
      `;
      
      if (selection.length > 0) {
        // Delete by ID
        await prisma.$queryRaw`
          DELETE FROM manual_model_selections 
          WHERE id = ${id}
        `;
        
        // Invalidate cache with the actual values
        await manualSelectionManager.invalidateSelection(
          selection[0].category, 
          selection[0].complexity, 
          selection[0].plan_type
        );
      }
    } else if (category && complexity) {
      // Delete all for category/complexity/plan
      await prisma.$queryRaw`
        DELETE FROM manual_model_selections 
        WHERE category = ${category} 
          AND complexity = ${complexity.toUpperCase()}
          AND plan_type = ${planType.toUpperCase()}
      `;
      
      // Invalidate cache for this selection
      await manualSelectionManager.invalidateSelection(category, complexity, planType);
    } else {
      return NextResponse.json(
        { error: 'Missing required parameters: id OR (category, complexity)' },
        { status: 400 }
      );
    }

    apiLogger.info('Manual model selection deleted', {
      category,
      complexity,
      planType
    });

    return NextResponse.json({
      success: true
    });
    
  } catch (error) {
    apiLogger.error('Failed to delete model selection', error);
    return NextResponse.json(
      { error: 'Failed to delete model selection' },
      { status: 500 }
    );
  }
}