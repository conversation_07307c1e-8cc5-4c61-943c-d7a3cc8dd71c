import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { Prisma } from '@prisma/client'
import { apiLogger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  try {
    // Fetch ALL models from database with provider info
    const models = await prisma.$queryRaw<Array<{
      id: string;
      providerId: string;
      providerName: string;
      canonicalName: string;
      displayName: string;
      generation: string;
      isEnabled: boolean;
      disabledReason: string | null;
      disabledAt: Date | null;
      supportsVision: boolean;
      supportsFunctionCalling: boolean;
      supportsWebSearch: boolean;
      supportsReasoning: boolean;
      supportsStreaming: boolean;
      contextWindow: number;
      maxOutput: number;
      inputCostPer1M: number;
      outputCostPer1M: number;
      speedRating: number;
      qualityRating: number;
      extendedMetadata: any;
      createdAt: Date;
      updatedAt: Date;
    }>>`
      SELECT m.*, p.name as providerName
      FROM Models m
      JOIN Providers p ON m.providerId = p.id
      ORDER BY p.name, m.displayName
    `

    // Filter out disabled models and transform to match the AIModel interface expected by the frontend
    const transformedModels = models.filter((model: any) => model.isEnabled === 1 || model.isEnabled === true).map((model: any) => {
      const metadata = model.extendedMetadata || {}
      const performanceData = metadata.performance || {}
      
      return {
        id: model.id,
        providerId: model.providerId,
        canonicalName: model.canonicalName,
        displayName: model.displayName,
        description: metadata.description || '',
        family: metadata.family || 'Unknown',
        generation: model.generation,
        speedRating: model.speedRating,
        qualityRating: model.qualityRating,
        inputCostPer1M: Number(model.inputCostPer1M) || 0,
        outputCostPer1M: Number(model.outputCostPer1M) || 0,
        provider: {
          name: model.providerName
        },
        modelType: metadata.modelType || 'CHAT',
        isEnabled: model.isEnabled,
        disabledReason: model.disabledReason,
        disabledAt: model.disabledAt?.toISOString(),
        disabledBy: metadata.disabledBy,
        
        // Capabilities
        capabilities: {
          vision: model.supportsVision || false,
          functionCalling: model.supportsFunctionCalling || false,
          toolUse: model.supportsFunctionCalling || false, // same as function calling
          webSearch: model.supportsWebSearch || false,
          streaming: model.supportsStreaming !== false, // default true
          jsonMode: metadata.supportsJsonMode || false,
          codeExecution: metadata.supportsCodeExecution || false
        },
        
        // Context & Limits
        contextWindow: model.contextWindow || 8192,
        maxOutputTokens: model.maxOutput || 4096,
        optimalContextTokens: metadata.optimalContextTokens,
        
        // Pricing
        pricing: {
          inputCostPer1M: Number(model.inputCostPer1M) || 0,
          outputCostPer1M: Number(model.outputCostPer1M) || 0,
          cachedInputCostPer1M: metadata.cachedInputCostPer1M,
          batchInputCostPer1M: metadata.batchInputCostPer1M,
          trainingCostPer1M: metadata.trainingCostPer1M
        },
        
        // Performance
        performance: {
          ttftMs: performanceData.ttftMs || 500,
          tokensPerSecond: performanceData.tokensPerSecond || 50,
          intelligenceScore: model.qualityRating * 10 || 50, // Convert 1-10 to 0-100
          costScore: metadata.costScore || 50,
          speedScore: model.speedRating * 10 || 50 // Convert 1-10 to 0-100
        },
        
        // Plan Access
        planAccess: {
          FREE: metadata.planAccess?.FREE !== false,
          FREEMIUM: metadata.planAccess?.FREEMIUM !== false,
          PLUS: metadata.planAccess?.PLUS !== false,
          ADVANCED: metadata.planAccess?.ADVANCED !== false,
          MAX: metadata.planAccess?.MAX !== false
        },
        
        // Router Configuration
        routerConfig: {
          priority: metadata.routerPriority || 100,
          routingGroup: metadata.routingGroup,
          categoryScores: metadata.categoryScores || {},
          specialization: metadata.specialization || []
        },
        
        // Metadata
        metadata: {
          releaseDate: metadata.releaseDate,
          deprecationDate: metadata.deprecationDate,
          replacedBy: metadata.replacedBy,
          documentationUrl: metadata.documentationUrl,
          changelogUrl: metadata.changelogUrl,
          tags: metadata.tags || []
        },
        
        createdAt: model.createdAt.toISOString(),
        updatedAt: model.updatedAt.toISOString()
      }
    })

    return NextResponse.json({
      models: transformedModels,
      total: transformedModels.length
    })
  } catch (error) {
    apiLogger.error('Failed to fetch models', error)
    return NextResponse.json(
      { error: 'Failed to fetch models' },
      { status: 500 }
    )
  }
}

// POST /api/admin/models - Create a new model
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.providerSlug || !body.canonicalName || !body.displayName || !body.family) {
      return NextResponse.json(
        { error: 'Missing required fields: providerSlug, canonicalName, displayName, family' },
        { status: 400 }
      )
    }

    // Find provider
    const provider = await prisma.providers.findUnique({
      where: { id: body.providerSlug }
    })

    if (!provider) {
      return NextResponse.json(
        { error: 'Provider not found' },
        { status: 404 }
      )
    }

    // Check if model already exists
    const existingModel = await prisma.models.findFirst({
      where: { canonicalName: body.canonicalName }
    })

    if (existingModel) {
      return NextResponse.json(
        { error: 'Model with this canonical name already exists' },
        { status: 400 }
      )
    }

    // Prepare metadata
    const metadata = {
      family: body.family,
      generation: body.generation,
      modelType: body.modelType || 'CHAT',
      supportsVision: body.capabilities?.vision || false,
      supportsFunctionCalling: body.capabilities?.functionCalling || false,
      supportsToolUse: body.capabilities?.toolUse || false,
      supportsWebSearch: body.capabilities?.webSearch || false,
      supportsStreaming: body.capabilities?.streaming !== false,
      supportsJsonMode: body.capabilities?.jsonMode || false,
      supportsCodeExecution: body.capabilities?.codeExecution || false,
      contextWindow: body.contextWindow || 8192,
      maxOutputTokens: body.maxOutputTokens || 4096,
      optimalContextTokens: body.optimalContextTokens,
      costPer1KInput: body.pricing?.inputCostPer1M ? body.pricing.inputCostPer1M / 1000 : 0,
      costPer1KOutput: body.pricing?.outputCostPer1M ? body.pricing.outputCostPer1M / 1000 : 0,
      cachedInputCostPer1M: body.pricing?.cachedInputCostPer1M,
      batchInputCostPer1M: body.pricing?.batchInputCostPer1M,
      trainingCostPer1M: body.pricing?.trainingCostPer1M,
      performance: {
        ttftMs: body.performance?.ttftMs || 500,
        tokensPerSecond: body.performance?.tokensPerSecond || 50,
        intelligenceScore: body.performance?.intelligenceScore || 50,
        costScore: body.performance?.costScore || 50,
        speedScore: body.performance?.speedScore || 50
      },
      planAccess: body.planAccess,
      routerPriority: body.routerConfig?.priority || 100,
      routingGroup: body.routerConfig?.routingGroup,
      categoryScores: body.routerConfig?.categoryScores || {},
      specialization: body.routerConfig?.specialization || [],
      releaseDate: body.metadata?.releaseDate,
      deprecationDate: body.metadata?.deprecationDate,
      replacedBy: body.metadata?.replacedBy,
      documentationUrl: body.metadata?.documentationUrl,
      changelogUrl: body.metadata?.changelogUrl,
      tags: body.metadata?.tags || []
    }

    // Create model
    const newModel = await prisma.models.create({
      data: {
        providerId: provider.id,
        canonicalName: body.canonicalName,
        displayName: body.displayName,
        generation: body.generation || '',
        family: body.family || '',
        modelType: body.modelType || 'chat',
        isEnabled: body.isEnabled !== false,
        disabledReason: body.disabledReason,
        disabledAt: !body.isEnabled ? new Date() : null,
        // Capability boolean fields
        supportsVision: body.capabilities?.vision || false,
        supportsFunctionCalling: body.capabilities?.functionCalling || false,
        supportsWebSearch: body.capabilities?.webSearch || false,
        supportsReasoning: body.capabilities?.reasoning || false,
        supportsStreaming: body.capabilities?.streaming !== false,
        // Context and pricing
        contextWindow: body.contextWindow || 8192,
        maxOutput: body.maxOutputTokens || 4096,
        inputCostPer1M: new Prisma.Decimal(body.pricing?.inputCostPer1M || 0),
        outputCostPer1M: new Prisma.Decimal(body.pricing?.outputCostPer1M || 0),
        // Performance ratings
        speedRating: Math.round((body.performance?.speedScore || 50) / 10), // Convert 0-100 to 1-10
        qualityRating: Math.round((body.performance?.intelligenceScore || 50) / 10), // Convert 0-100 to 1-10
        // Extended metadata for all other fields
        extendedMetadata: metadata
      }
    })

    apiLogger.info('Model created', {
      modelId: newModel.id,
      canonicalName: newModel.canonicalName,
      provider: provider.name
    })

    return NextResponse.json({
      success: true,
      model: newModel
    })
  } catch (error) {
    apiLogger.error('Failed to create model', error)
    return NextResponse.json(
      { error: 'Failed to create model' },
      { status: 500 }
    )
  }
}