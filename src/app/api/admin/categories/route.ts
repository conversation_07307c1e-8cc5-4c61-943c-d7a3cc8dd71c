/**
 * Admin API for managing dynamic categories from Models table
 * GET /api/admin/categories - List all categories dynamically extracted from Models.extendedMetadata.categoryScores
 * 
 * NO AUTH FOR NOW - Will add later
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { apiLogger } from '@/lib/logger';

// GET /api/admin/categories
export async function GET(req: NextRequest) {
  try {
    // Get static categories list since dynamic extraction is complex
    const staticCategories = [
      'coding', 'math', 'reasoning', 'creative_writing', 'general_chat', 
      'analysis', 'tutorial', 'debugging', 'scientific', 'translation',
      'summarization', 'data_analysis', 'brainstorming', 'factual_qa',
      'question_answering', 'technical_writing', 'business_writing', 
      'academic_writing', 'multimodal', 'image_analysis', 'role_play',
      'philosophical', 'historical', 'medical', 'legal', 'current_events',
      'personal_advice', 'image_generation', 'other'
    ];

    // Format the categories for admin UI
    const categories = staticCategories.map(cat => ({
      id: cat,
      name: cat,
      displayName: cat.split('_').map((word: string) => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' '),
      description: `Category: ${cat}`,
      modelCount: 0, // Will be populated when needed
      avgScore: null,
      manualSelections: 0,
      isActive: true
    }));
    
    return NextResponse.json({
      categories,
      total: categories.length
    });
    
  } catch (error) {
    apiLogger.error('Failed to fetch categories', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// Categories are now dynamically extracted from Models table
// No need for POST/DELETE - categories are managed through model scores