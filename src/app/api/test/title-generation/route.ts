import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('Title generation test endpoint called');
    
    const body = await request.json();
    const { messages } = body;
    
    console.log('Received messages:', messages);
    
    // Try to import the title generator
    console.log('Importing title generator...');
    const { titleGenerator } = await import('@/lib/ai/title-generator-ai-sdk');
    console.log('Title generator imported successfully');
    
    console.log('Calling generateTitle...');
    const title = await titleGenerator.generateTitle(messages, {
      maxLength: 60,
      style: 'auto',
      includeEmoji: true
    });
    
    console.log('Title generated:', title);
    
    return NextResponse.json({ 
      success: true,
      title,
      messagesCount: messages.length
    });
    
  } catch (error) {
    console.error('Title generation test failed with error:', error);
    console.error('Error type:', typeof error);
    console.error('Error constructor:', error?.constructor?.name);
    console.error('Error message:', error instanceof Error ? error.message : String(error));
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack');
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : String(error),
        errorType: error?.constructor?.name || typeof error,
        details: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}