'use client';

import { motion } from 'framer-motion';

// Force dynamic rendering
export const dynamic = 'force-dynamic';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Brain, 
  Code2, 
  FileSearch, 
  MessageSquare,
  Globe,
  Palette,
  Calculator,
  BookOpen,
  Briefcase,
  Heart,
  Rocket,
  Lock,
  Clock,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';
import { useSession, signIn } from 'next-auth/react';

export default function AgentsPage() {
  const { data: session } = useSession();

  const agentCategories = [
    {
      title: "Enterprise Automation",
      icon: <Briefcase className="w-5 h-5" />,
      color: "from-blue-500 to-cyan-500",
      agents: [
        {
          name: "Deep Research Pro",
          description: "Advanced research agent with web search, academic paper analysis, and comprehensive reporting. Uses multi-agent collaboration for faster insights.",
          icon: <FileSearch className="w-8 h-8" />,
          capabilities: ["Real-time Web Search", "Academic Database Access", "Multi-source Analysis", "Citation Management", "Trend Detection"],
          status: "coming-soon"
        },
        {
          name: "Code Architect",
          description: "Multi-agent coding system with autonomous debugging, optimization, and full-stack project support. Powered by agentic AI frameworks.",
          icon: <Code2 className="w-8 h-8" />,
          capabilities: ["Autonomous Debugging", "Code Review & Optimization", "Multi-file Project Management", "Documentation Generation", "Performance Analysis"],
          status: "coming-soon"
        },
        {
          name: "Content Intelligence",
          description: "Enterprise-grade writing assistant with SEO optimization, brand voice matching, and multi-format content generation for modern businesses.",
          icon: <BookOpen className="w-8 h-8" />,
          capabilities: ["Brand Voice Matching", "SEO Optimization", "Multi-format Content", "Performance Analytics", "Compliance Checking"],
          status: "coming-soon"
        }
      ]
    },
    {
      title: "Creative Intelligence",
      icon: <Palette className="w-5 h-5" />,
      color: "from-purple-500 to-pink-500",
      agents: [
        {
          name: "Creative Director AI",
          description: "Advanced creative agent for brand strategy, campaign development, and visual storytelling. Leverages latest generative AI breakthroughs.",
          icon: <Sparkles className="w-8 h-8" />,
          capabilities: ["Brand Strategy", "Campaign Development", "Visual Storytelling", "Trend Analysis", "Creative Optimization"],
          status: "coming-soon"
        },
        {
          name: "Design Systems Pro",
          description: "Autonomous design agent for UI/UX optimization, accessibility compliance, and design system management with real-time feedback.",
          icon: <Palette className="w-8 h-8" />,
          capabilities: ["UI/UX Optimization", "Accessibility Compliance", "Design System Management", "A/B Testing", "User Experience Analytics"],
          status: "coming-soon"
        }
      ]
    },
    {
      title: "Specialized Intelligence",
      icon: <Brain className="w-5 h-5" />,
      color: "from-green-500 to-emerald-500",
      agents: [
        {
          name: "Data Science Pro",
          description: "Autonomous data analysis agent with predictive modeling, advanced visualization, and real-time insights for enterprise decision-making.",
          icon: <Calculator className="w-8 h-8" />,
          capabilities: ["Predictive Modeling", "Advanced Visualization", "Real-time Analytics", "Anomaly Detection", "Business Intelligence"],
          status: "coming-soon"
        },
        {
          name: "Global Communication",
          description: "Multi-language agent with cultural context awareness, real-time translation, and cross-cultural communication optimization.",
          icon: <Globe className="w-8 h-8" />,
          capabilities: ["Multi-language Support", "Cultural Context", "Real-time Translation", "Communication Optimization", "Global Compliance"],
          status: "coming-soon"
        },
        {
          name: "Wellness Intelligence",
          description: "AI-powered wellness coach with personalized health insights, mental health support, and behavioral change optimization.",
          icon: <Heart className="w-8 h-8" />,
          capabilities: ["Personalized Health Insights", "Mental Health Support", "Behavioral Change", "Wellness Tracking", "Goal Achievement"],
          status: "coming-soon"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-16 px-4">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-transparent to-blue-900/20" />
        
        <div className="container mx-auto max-w-6xl relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-500/30 text-sm text-purple-300 mb-6">
              <Rocket className="w-4 h-4" />
              Coming Soon
            </div>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
              AI Agents - The Future of Autonomous Intelligence
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Experience the next generation of AI with specialized autonomous agents. Our advanced multi-agent systems deliver 55% efficiency gains and 35% cost reductions, transforming how businesses operate in 2025.
            </p>
          </motion.div>

          {/* What are Agents */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-16"
          >
            <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/50 rounded-2xl p-8 border border-gray-700/50">
              <h2 className="text-2xl font-bold mb-6 flex items-center gap-3">
                <Bot className="w-6 h-6 text-purple-400" />
                What are AI Agents? The $236B Market Revolution
              </h2>
              
              <div className="mb-6 p-4 bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded-lg border border-blue-500/20">
                <p className="text-sm text-blue-300">
                  <strong>Market Insight:</strong> The AI agents market is exploding from $7.92B in 2025 to $236.03B by 2034 (45.82% CAGR). 
                  Leading companies like Microsoft, Google, and AWS are investing billions in agentic AI platforms.
                </p>
              </div>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Brain className="w-6 h-6 text-blue-400" />
                  </div>
                  <h3 className="font-semibold text-lg">Agentic AI Systems</h3>
                  <p className="text-gray-400 text-sm">
                    Next-generation AI that sets goals, self-corrects, and executes multi-step workflows autonomously. 
                    Powered by frameworks like LangChain, AutoGen, and LangGraph.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-6 h-6 text-purple-400" />
                  </div>
                  <h3 className="font-semibold text-lg">Multi-Agent Collaboration</h3>
                  <p className="text-gray-400 text-sm">
                    Specialized agent teams that collaborate to solve complex problems. 60% faster than single-agent models 
                    with improved cost efficiency and reduced latency.
                  </p>
                </div>
                
                <div className="space-y-3">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Sparkles className="w-6 h-6 text-green-400" />
                  </div>
                  <h3 className="font-semibold text-lg">Enterprise-Grade Automation</h3>
                  <p className="text-gray-400 text-sm">
                    Self-learning systems that optimize performance through experience. 85% of companies plan 
                    AI agent deployments in 2025 for customer support, HR, and supply chain management.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Agent Categories */}
          {agentCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + categoryIndex * 0.1 }}
              className="mb-12"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center`}>
                  {category.icon}
                </div>
                <h2 className="text-2xl font-bold">{category.title} Agents</h2>
              </div>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {category.agents.map((agent, index) => (
                  <motion.div
                    key={agent.name}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.4 + categoryIndex * 0.1 + index * 0.05 }}
                  >
                    <div className="group relative h-full">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-xl blur-xl group-hover:blur-2xl transition-all opacity-0 group-hover:opacity-100" />
                      
                      <div className="relative h-full bg-gray-900/90 border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all">
                        {agent.status === 'coming-soon' && (
                          <div className="absolute top-4 right-4">
                            <span className="flex items-center gap-1 text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full">
                              <Clock className="w-3 h-3" />
                              Soon
                            </span>
                          </div>
                        )}
                        
                        <div className="mb-4 text-purple-400">
                          {agent.icon}
                        </div>
                        
                        <h3 className="text-xl font-semibold mb-2">{agent.name}</h3>
                        <p className="text-gray-400 text-sm mb-4">{agent.description}</p>
                        
                        <div className="space-y-2 mb-4">
                          {agent.capabilities.map((capability) => (
                            <div key={capability} className="flex items-center gap-2 text-xs text-gray-500">
                              <div className="w-1 h-1 bg-gray-500 rounded-full" />
                              {capability}
                            </div>
                          ))}
                        </div>
                        
                        <button
                          disabled
                          className="w-full py-2 bg-gray-800 text-gray-500 rounded-lg cursor-not-allowed flex items-center justify-center gap-2"
                        >
                          <Lock className="w-4 h-4" />
                          Coming Soon
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}

          {/* Early Access CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mt-16"
          >
            <div className="bg-gradient-to-br from-purple-900/30 to-blue-900/30 rounded-2xl p-8 border border-purple-500/30 text-center">
              <h2 className="text-2xl font-bold mb-4">Join the AI Agent Revolution - Early Access Available</h2>
              <p className="text-gray-300 mb-4 max-w-2xl mx-auto">
                Be among the first to experience next-generation autonomous AI agents. Our platform integrates cutting-edge frameworks like LangChain, AutoGen, and LangGraph to deliver enterprise-grade automation solutions.
              </p>
              <div className="grid md:grid-cols-3 gap-4 mb-6 text-sm">
                <div className="bg-gray-800/50 p-3 rounded-lg">
                  <div className="text-green-400 font-semibold">55% Efficiency Gains</div>
                  <div className="text-gray-400">Proven enterprise results</div>
                </div>
                <div className="bg-gray-800/50 p-3 rounded-lg">
                  <div className="text-blue-400 font-semibold">$236B Market</div>
                  <div className="text-gray-400">Growing at 45.82% CAGR</div>
                </div>
                <div className="bg-gray-800/50 p-3 rounded-lg">
                  <div className="text-purple-400 font-semibold">Multi-Agent Systems</div>
                  <div className="text-gray-400">60% faster than single agents</div>
                </div>
              </div>
              
              {session ? (
                <div className="space-y-4">
                  <p className="text-green-400 font-medium">
                    ✓ You&apos;re on the waitlist!
                  </p>
                  <p className="text-sm text-gray-400">
                    We&apos;ll notify you at {session.user?.email} when agents launch.
                  </p>
                  {(session.user?.plan === 'FREE' || session.user?.plan === 'FREEMIUM') && (
                    <Link
                      href="/pricing"
                      className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-full font-medium transition-all transform hover:scale-105"
                    >
                      Upgrade for Priority Access <ArrowRight className="w-4 h-4" />
                    </Link>
                  )}
                </div>
              ) : (
                <button
                  onClick={() => signIn('google', { callbackUrl: '/agents' })}
                  className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-full font-medium transition-all transform hover:scale-105"
                >
                  Sign In to Join Waitlist <ArrowRight className="w-4 h-4" />
                </button>
              )}
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}