'use client';

export const dynamic = 'force-dynamic';

import { useState, useEffect } from 'react';
import { productSchemas, generateJsonLd } from '@/lib/structured-data';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  Check, 
  X, 
  Sparkles, // TODO-AUDIT-2025-Q2: Unused import 'Sparkles'
  Zap, // TODO-AUDIT-2025-Q2: Unused import 'Zap'
  Star, 
  Rocket, 
  Crown,
  Gift,
  Clock,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
// TODO-AUDIT-2025-Q2: Unused import 'CardDescription'
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { UserPlan } from '@/types';
import { getCurrencySymbol } from '@/lib/stripe/client-config';
import { useModelCount, usePlanStats, useModelStats } from '@/hooks/useModelStats';
import { detectUserCurrency, type SupportedCurrency } from '@/lib/geolocation/currency-detector';
import { TOTAL_MODELS_MARKETING, FREE_MESSAGE_LIMITS } from '@/lib/model-stats';
import { PRICING, type Currency as LandingCurrency, formatCurrency, formatLargeCurrency } from '@/lib/currency';

type Currency = SupportedCurrency;

// Plans will be defined inside the component to access hooks

const currencies: Currency[] = ['gbp', 'usd', 'eur'];

export default function PricingPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>('usd');
  const [landingCurrency, setLandingCurrency] = useState<LandingCurrency>('USD');
  const [loading, setLoading] = useState<string | null>(null);
  const [couponCode, setCouponCode] = useState<string>('');
  const [hasLifetimeAccess, setHasLifetimeAccess] = useState(false);
  const [checkingAccess, setCheckingAccess] = useState(true);
  const { total: totalModels, totalRounded, loading: modelsLoading } = useModelCount();
  const { getPlanText, advancedPercentage, maxPercentage } = usePlanStats();
  const { planStats } = useModelStats();

  // Define plans with dynamic model counts
  const plans = [
    {
      id: UserPlan.PLUS,
      name: 'Plus',
      icon: <Star className="w-8 h-8" />,
      popular: false,
      features: [
        { text: '1,500 standard messages per month', included: true },
        { text: '100 premium messages per month', included: true },
        { text: `${planStats?.tiers?.standard || 0} standard + ${planStats?.tiers?.premium || 0} premium models`, included: true },
        { text: 'Web search & file uploads', included: true },
        { text: 'Priority support', included: true },
        { text: 'GPT-4o, Claude Sonnet, Gemini Pro', included: true },
        { text: 'FRONTIER models (O3-Pro, Claude 4 Opus)', included: false },
      ],
    },
    {
      id: UserPlan.ADVANCED,
      name: 'Advanced',
      icon: <Rocket className="w-8 h-8" />,
      popular: true,
      features: [
        { text: '4,500 standard messages per month', included: true },
        { text: '200 premium messages per month', included: true },
        { text: 'All standard + premium models', included: true },
        { text: 'Web search & file uploads', included: true },
        { text: 'Priority support', included: true },
        { text: 'Advanced analytics', included: true },
        { text: 'FRONTIER models (O3-Pro, Claude 4 Opus)', included: false },
      ],
    },
    {
      id: UserPlan.MAX,
      name: 'Max',
      icon: <Crown className="w-8 h-8" />,
      popular: false,
      features: [
        { text: '7,000 standard messages per month', included: true },
        { text: '300 premium messages per month', included: true },
        { text: `ALL ${totalModels} models including ${planStats?.tiers?.frontier || 0} FRONTIER`, included: true },
        { text: 'O3-Pro, Claude 4 Opus, GPT-4.5', included: true },
        { text: 'Web search & file uploads', included: true },
        { text: 'Priority support', included: true },
        { text: 'Advanced analytics & data export', included: true },
      ],
    },
  ];

  // Check for lifetime access
  useEffect(() => {
    const checkLifetimeAccess = async () => {
      if (session?.user) {
        try {
          const response = await fetch('/api/user/subscription');
          if (response.ok) {
            const data = await response.json();
            setHasLifetimeAccess(data.hasLifetimeAccess || data.stripeSubscriptionStatus === 'lifetime_coupon');
          }
        } catch (error) {
          console.error('Failed to check lifetime access:', error);
        }
      }
      setCheckingAccess(false);
    };
    
    checkLifetimeAccess();
  }, [session]);

  useEffect(() => {
    // Detect user's currency using IP-based geolocation
    const detectCurrency = async () => {
      try {
        const currency = await detectUserCurrency();
        setSelectedCurrency(currency);
        // Convert to uppercase for landing page currency system
        const landingCurrencyMap: Record<Currency, LandingCurrency> = {
          usd: 'USD',
          gbp: 'GBP',
          eur: 'EUR'
        };
        setLandingCurrency(landingCurrencyMap[currency]);
      } catch (error) {
        console.warn('Currency detection failed, defaulting to USD:', error);
        setSelectedCurrency('usd');
        setLandingCurrency('USD');
      }
    };

    if (typeof window !== 'undefined') {
      detectCurrency();
    }
  }, []);

  const handleSubscribe = async (plan: UserPlan) => {
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    setLoading(plan);
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          plan, 
          currency: selectedCurrency,
          ...(couponCode.trim() && { coupon: couponCode.trim() })
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { url, appliedCoupon, message } = await response.json();
      
      // If a special coupon was applied, show success message
      if (appliedCoupon && message) {
        alert(message);
      }
      
      window.location.href = url;
    } catch (error: any) {
      console.error('Failed to start checkout:', error);
      alert(error.message || 'Failed to start checkout. Please try again.');
    } finally {
      setLoading(null);
    }
  };

  const getPriceForPlan = (plan: UserPlan): number => {
    const priceMap: Record<UserPlan.PLUS | UserPlan.ADVANCED | UserPlan.MAX, Record<Currency, number>> = {
      [UserPlan.PLUS]: { gbp: 7.99, usd: 7.99, eur: 7.99 },
      [UserPlan.ADVANCED]: { gbp: 19.99, usd: 19.99, eur: 19.99 },
      [UserPlan.MAX]: { gbp: 49.99, usd: 49.99, eur: 49.99 },
    };
    
    return priceMap[plan as UserPlan.PLUS | UserPlan.ADVANCED | UserPlan.MAX]?.[selectedCurrency] || 0;
  };

  // Show lifetime access message if user has it
  if (hasLifetimeAccess && !checkingAccess) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-7xl">
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-full shadow-lg mb-8">
            <Crown className="w-8 h-8" />
            <div>
              <h2 className="text-2xl font-bold">You have Lifetime MAX Access!</h2>
              <p className="text-sm opacity-90">Enjoy unlimited access to all {totalModels} AI models</p>
            </div>
          </div>
          
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl">Your Lifetime Benefits</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div className="text-left">
                    <p className="font-semibold">All {totalModels} AI Models</p>
                    <p className="text-sm text-gray-600">Access to every model including premium ones</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div className="text-left">
                    <p className="font-semibold">Unlimited Messages</p>
                    <p className="text-sm text-gray-600">No daily or monthly limits</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div className="text-left">
                    <p className="font-semibold">Priority Support</p>
                    <p className="text-sm text-gray-600">Get help when you need it</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div className="text-left">
                    <p className="font-semibold">All Future Updates</p>
                    <p className="text-sm text-gray-600">New models and features included</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex gap-3">
              <Button 
                className="flex-1"
                onClick={() => router.push('/')}
              >
                Start Chatting
              </Button>
              <Button 
                variant="outline"
                className="flex-1"
                onClick={() => router.push('/billing')}
              >
                View Account
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">
          Choose Your AI Assistant Plan
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Access {totalModels}+ AI models from free to frontier with flexible pricing
        </p>
        
        {/* Free tier info */}
        <div className="max-w-4xl mx-auto mb-8 grid md:grid-cols-2 gap-4">
          <div className="bg-gray-800/80 p-4 rounded-lg border border-gray-700">
            <h3 className="font-semibold text-white mb-2">🆓 FREE (No Login)</h3>
                            <p className="text-sm text-gray-300">{FREE_MESSAGE_LIMITS.ANONYMOUS} messages/day • {TOTAL_MODELS_MARKETING} AI models • No web search/uploads</p>
          </div>
          <div className="bg-blue-900/80 p-4 rounded-lg border border-blue-700">
            <h3 className="font-semibold text-blue-100 mb-2">💎 FREEMIUM (Login Required)</h3>
            <p className="text-sm text-blue-200">Higher limits • Same {planStats?.tiers?.free || 0} models • Web search & file uploads included</p>
          </div>
        </div>
        
        {/* Trial Badge */}
        <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
          <Clock className="w-5 h-5" />
          <span className="font-semibold">3-day free trial on all plans</span>
          <span className="text-sm">• Cancel anytime</span>
        </div>

        {/* Coupon Code Input - only show if not checking access and not lifetime */}
        {!checkingAccess && !hasLifetimeAccess && (
          <div className="max-w-md mx-auto mt-8">
            <div className="flex items-center gap-2 bg-blue-50 p-4 rounded-lg border border-blue-200">
              <Gift className="w-5 h-5 text-blue-600 flex-shrink-0" />
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Have a coupon code? Enter it here"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  className="border-blue-300 focus:border-blue-500 bg-white text-gray-900 placeholder:text-gray-500"
                />
              </div>
            </div>
            {couponCode.trim() && (
              <p className="text-sm text-blue-600 mt-2 text-center">
                Coupon code will be applied at checkout
              </p>
            )}
          </div>
        )}
      </div>

      {/* Pricing Cards - only show if not checking access and not lifetime */}
      {!checkingAccess && !hasLifetimeAccess && (
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {plans.map((plan) => {
          const price = getPriceForPlan(plan.id);
          
          return (
            <Card 
              key={plan.id}
              className={`relative ${plan.popular ? 'border-blue-500 shadow-xl' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white px-3 py-1">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pt-8">
                <div className="flex justify-center mb-4">
                  {plan.icon}
                </div>
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">
                    {getCurrencySymbol(selectedCurrency)}{price}
                  </span>
                  <span className="text-gray-600">/month</span>
                </div>
                <p className="text-sm text-green-600 mt-2">
                  Start with 3-day free trial
                </p>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-2">
                      {feature.included ? (
                        <Check className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      ) : (
                        <X className="w-5 h-5 text-gray-300 flex-shrink-0 mt-0.5" />
                      )}
                      <span className={feature.included ? '' : 'text-gray-400'}>
                        {feature.text}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              
              <CardFooter>
                <Button
                  className="w-full"
                  variant={plan.popular ? 'default' : 'outline'}
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={loading !== null}
                >
                  {loading === plan.id ? (
                    'Loading...'
                  ) : (
                    'Start Free Trial'
                  )}
                </Button>
              </CardFooter>
            </Card>
          );
        })}
        </div>
      )}

      {/* FAQ Section - show different content for lifetime users */}
      {!checkingAccess && (
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-8">
            {hasLifetimeAccess ? 'Getting Started' : 'Frequently Asked Questions'}
          </h2>
          
          <div className="space-y-6">
            {hasLifetimeAccess ? (
              <>
                <div>
                  <h3 className="font-semibold mb-2">How do I access all {totalModels} models?</h3>
                  <p className="text-gray-600">
                    Simply start a new chat and click the model selector to browse all available models. 
                    You have unlimited access to every model including the latest premium ones.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Are there any usage limits?</h3>
                  <p className="text-gray-600">
                    No! With your lifetime access, you have unlimited messages per day and month. 
                    Use as much as you need without worrying about quotas.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Will I get access to new models?</h3>
                  <p className="text-gray-600">
                    Yes! Your lifetime access includes all future model additions and platform updates. 
                    When we add new AI models, they&apos;ll automatically be available to you.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Need help or have questions?</h3>
                  <p className="text-gray-600">
                    As a lifetime member, you have priority support. Contact <NAME_EMAIL> 
                    and we&apos;ll help you make the most of your unlimited access.
                  </p>
                </div>
              </>
            ) : (
              <>
                <div>
                  <h3 className="font-semibold mb-2">How does the 3-day free trial work?</h3>
                  <p className="text-gray-600">
                    You get full access to your chosen plan for 3 days without any charges. 
                    We&apos;ll charge your card after the trial ends unless you cancel.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">Can I change plans later?</h3>
                  <p className="text-gray-600">
                    Yes! You can upgrade, downgrade, or cancel your subscription at any time 
                    from your account settings. Changes take effect immediately.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-2">What models are included in each plan?</h3>
                  <div className="space-y-2">
                    <p className="text-gray-700">
                      <strong className="text-gray-900">Free/Freemium:</strong> {planStats?.tiers?.free || 0} models under {getCurrencySymbol(selectedCurrency)}0.50 cost (Gemini 2.5 Flash, Llama, etc)
                    </p>
                    <p className="text-gray-700">
                      <strong className="text-gray-900">Plus:</strong> {planStats?.tiers?.standard || 0} standard + {planStats?.tiers?.premium || 0} premium models (GPT-4o, Claude Sonnet, limited premium usage)
                    </p>
                    <p className="text-gray-700">
                      <strong className="text-gray-900">Advanced:</strong> Same models as Plus with higher limits
                    </p>
                    <p className="text-gray-700">
                      <strong className="text-gray-900">Max:</strong> All {totalModels} models including {planStats?.tiers?.frontier || 0} FRONTIER models (O3-Pro, Claude 4 Opus)
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Do you have any special offers?</h3>
                  <p className="text-gray-600 flex items-center gap-2">
                    <Gift className="w-4 h-4 text-blue-500" />
                    Yes! We occasionally provide special coupon codes for extended access. 
                    If you have a code, you can apply it in your subscription settings.
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}