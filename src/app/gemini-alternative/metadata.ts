import { Metadata } from 'next'
import { createComparisonPageMetadata } from '@/lib/metadata'

const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Gemini Alternative - JustSimpleChat",
  "description": "The best Gemini Pro alternative with access to Gemini 2.5 Flash, GPT-4, Claude and 200+ AI models. Pay less than Gemini Advanced and get more models.",
  "url": "https://www.justsimple.chat/gemini-alternative",
  "mainEntity": {
    "@type": "SoftwareApplication",
    "name": "JustSimpleChat",
    "applicationCategory": "ProductivityApplication",
    "offers": {
      "@type": "Offer",
      "price": "7.99",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1247"
    }
  }
}

export const metadata: Metadata = {
  ...createComparisonPageMetadata('Gemini', {
    models: 200,
    savings: '$12.01',
    features: ['Gemini 2.5 Flash', 'Gemini Pro', 'vision analysis', 'long context', 'multimodal']
  }),
  other: {
    'application/ld+json': JSON.stringify(structuredData)
  }
}