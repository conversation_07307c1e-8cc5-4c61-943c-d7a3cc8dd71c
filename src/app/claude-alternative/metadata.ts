import { Metadata } from 'next'
import { createComparisonPageMetadata } from '@/lib/metadata'

const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Claude Alternative - JustSimpleChat",
  "description": "The best Claude alternative with access to Claude 3.5, Claude 4, GPT-4, Gemini and 200+ AI models. Pay less than Claude Pro and get more models.",
  "url": "https://www.justsimple.chat/claude-alternative",
  "mainEntity": {
    "@type": "SoftwareApplication",
    "name": "JustSimpleChat",
    "applicationCategory": "ProductivityApplication",
    "offers": {
      "@type": "Offer",
      "price": "7.99",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1247"
    }
  }
}

export const metadata: Metadata = {
  ...createComparisonPageMetadata('<PERSON>', {
    models: 200,
    savings: '$12.01',
    features: ['Claude 3.5 access', 'Claude 4 access', 'GPT-4 access', 'long context', 'superior coding']
  }),
  other: {
    'application/ld+json': JSON.stringify(structuredData)
  }
}