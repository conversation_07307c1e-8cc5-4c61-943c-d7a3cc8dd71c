import { Metadata } from 'next'
import { createComparisonPageMetadata } from '@/lib/metadata'

const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "Perplexity Alternative - JustSimpleChat",
  "description": "The best Perplexity Pro alternative with web search plus access to GPT-4, Claude, Gemini and 200+ AI models. Get research capabilities and more for less.",
  "url": "https://www.justsimple.chat/perplexity-alternative",
  "mainEntity": {
    "@type": "SoftwareApplication",
    "name": "JustSimpleChat",
    "applicationCategory": "ProductivityApplication",
    "offers": {
      "@type": "Offer",
      "price": "7.99",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1247"
    }
  }
}

export const metadata: Metadata = {
  ...createComparisonPageMetadata('Perplexity', {
    models: 200,
    savings: '$12.01',
    features: ['web search', 'real-time data', 'research capabilities', 'source citations', 'multiple AI models']
  }),
  other: {
    'application/ld+json': JSON.stringify(structuredData)
  }
}