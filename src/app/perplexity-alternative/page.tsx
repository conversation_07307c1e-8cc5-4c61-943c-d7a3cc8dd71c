import Link from 'next/link';
import { CheckCircle, XCircle, ArrowRight, Sparkles, Search, Brain, Zap, FileText, Globe, Link as LinkIcon, Eye, BookOpen, Database } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TOTAL_MODELS_MARKETING } from '@/lib/model-stats';

// Metadata is exported from ./metadata.ts
export { metadata } from './metadata';

export default function PerplexityAlternativePage() {
  return (
    <>
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-black">
        {/* Hero Section */}
        <section className="pt-20 pb-16 px-4">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-4 py-2 rounded-full text-sm font-medium mb-6">
              <Search className="w-4 h-4" />
              The Superior Research Alternative
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Get Better Research + <span className="text-green-600">{TOTAL_MODELS_MARKETING} AI Models</span>
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Keep Perplexity&apos;s Pro Search and citation transparency, but also get Claude 4, GPT-4.5 Orion, Gemini 2.5 Pro, and {TOTAL_MODELS_MARKETING} other AI models 
              for <strong>the same $20/month as Perplexity Pro</strong> - with better UI and accuracy.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/">
                <Button size="lg" className="w-full sm:w-auto bg-green-600 hover:bg-green-700">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/">
                <Button size="lg" variant="outline" className="w-full sm:w-auto">
                  View Pricing
                </Button>
              </Link>
            </div>

            <p className="text-sm text-gray-500 dark:text-gray-400">
              Keep web search & citations • Add {TOTAL_MODELS_MARKETING} more models • Better UI & accuracy
            </p>
          </div>
        </section>

        {/* Comparison Table */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              JustSimpleChat vs Perplexity Pro
            </h2>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-lg">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left p-4">Feature</th>
                    <th className="text-center p-4">
                      <div className="font-bold text-green-600">JustSimpleChat</div>
                      <div className="text-sm text-gray-500">$20/month</div>
                    </th>
                    <th className="text-center p-4">
                      <div className="font-bold">Perplexity Pro</div>
                      <div className="text-sm text-gray-500">$20/month</div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Real-Time Web Search</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Citation Transparency</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700 bg-green-50 dark:bg-green-900/20">
                    <td className="p-4 font-medium">Access to Claude 4 & GPT-4</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Creative Content Generation</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700 bg-green-50 dark:bg-green-900/20">
                    <td className="p-4 font-medium">Total AI Models</td>
                    <td className="text-center p-4 font-bold text-green-600">{TOTAL_MODELS_MARKETING}</td>
                    <td className="text-center p-4">Limited LLMs</td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Advanced Customization</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Unlimited File Uploads</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Enhanced UI Experience</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="border-b dark:border-gray-700">
                    <td className="p-4">Better Accuracy (Multiple Models)</td>
                    <td className="text-center p-4"><CheckCircle className="w-5 h-5 text-green-500 mx-auto" /></td>
                    <td className="text-center p-4"><XCircle className="w-5 h-5 text-red-500 mx-auto" /></td>
                  </tr>
                  <tr className="bg-green-50 dark:bg-green-900/20">
                    <td className="p-4 font-bold">Value</td>
                    <td className="text-center p-4 font-bold text-green-600">Same price, {TOTAL_MODELS_MARKETING} models, better UI</td>
                    <td className="text-center p-4">Research-only, basic UI</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* Research-Specific Benefits */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Keep Research Excellence, Add Creative Power
            </h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <Search className="w-10 h-10 text-green-500 mb-2" />
                  <CardTitle>Same Research Power</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Keep Pro Search with citations and real-time web access for thorough research.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Brain className="w-10 h-10 text-blue-500 mb-2" />
                  <CardTitle>Better Accuracy</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Cross-verify findings with Claude 4, GPT-4.5 Orion, and Gemini 2.5 Pro to eliminate Perplexity&apos;s accuracy issues.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <FileText className="w-10 h-10 text-purple-500 mb-2" />
                  <CardTitle>Creative Content</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Generate creative content that Perplexity can&apos;t do - using research for blogs, articles, and reports.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <Eye className="w-10 h-10 text-orange-500 mb-2" />
                  <CardTitle>Better UI</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Escape Perplexity&apos;s &quot;barebones&quot; UI with our advanced, customizable interface.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Research Use Cases Enhanced */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Supercharged Research Capabilities
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Search className="w-6 h-6 text-green-500" />
                  For Academic Research (Enhanced)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Perplexity Pro:</strong> Real-time search with transparent citations</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Claude 4:</strong> Deep analysis and critical thinking on findings</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Cross-verify:</strong> Eliminate accuracy issues with multiple models</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <FileText className="w-6 h-6 text-blue-500" />
                  For Content Creation (Revolutionary)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Research first:</strong> Gather facts with Perplexity&apos;s web search</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Create second:</strong> Turn research into content with Claude/GPT-4</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Complete workflow:</strong> Research → Write → Edit in one platform</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Globe className="w-6 h-6 text-purple-500" />
                  For Business Intelligence (Professional)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Market research:</strong> Real-time competitor and industry analysis</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Strategic analysis:</strong> Claude 4 for deep business insights</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Report generation:</strong> Professional reports with citations</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <BookOpen className="w-6 h-6 text-orange-500" />
                  For Journalism (Complete Solution)
                </h3>
                <ul className="space-y-3">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Fact-checking:</strong> Multiple models verify information accuracy</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Story development:</strong> From research to polished articles</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <span><strong>Source citations:</strong> Transparent, verifiable references</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Current Perplexity Issues */}
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Current Perplexity Pro Issues (July 2025)
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>&quot;Barebones&quot; UI Experience</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: Modern, customizable interface</p>
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Summarization Accuracy Issues</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: Cross-verify with multiple AI models</p>
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Limited Creative Generation</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: Research + create in one platform</p>
                  </CardDescription>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <XCircle className="w-8 h-8 text-red-500 mb-2" />
                  <CardTitle>Limited Enterprise Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="space-y-2">
                    <p>&quot;Fewer enterprise integrations and limited customization and advanced API controls compared to Claude and Gemini.&quot; - Business user feedback</p>
                    <p className="text-green-600 font-semibold">✓ JustSimpleChat: Full API access and integrations</p>
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Migration Benefits */}
        <section className="py-16 px-4 bg-gray-50 dark:bg-gray-900/50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">
              Why Researchers Are Switching
            </h2>

            <div className="grid md:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-green-600">1</span>
                  </div>
                  <CardTitle>Keep Research Excellence</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Same Pro Search capabilities with citations and real-time web access you&apos;re used to.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-green-600">2</span>
                  </div>
                  <CardTitle>Add Creative Power</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Turn research into polished content with Claude 4, GPT-4.5 Orion, and {TOTAL_MODELS_MARKETING} other models.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-green-600">3</span>
                  </div>
                  <CardTitle>Better Accuracy</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Cross-verify findings with multiple models to eliminate summarization errors and improve accuracy.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 bg-gradient-to-r from-green-600 to-blue-600 text-white">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold mb-6">
              Ready to Supercharge Your Research?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Keep Perplexity&apos;s research excellence, but add {TOTAL_MODELS_MARKETING} AI models for content creation and better accuracy.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/">
                <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                  Start 3-Day Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/">
                <Button size="lg" variant="outline" className="w-full sm:w-auto bg-transparent text-white border-white hover:bg-white hover:text-green-600">
                  Compare Plans
                </Button>
              </Link>
            </div>
            <p className="mt-6 text-sm opacity-75">
              Keep your research workflow • Add content creation • Cancel anytime
            </p>
          </div>
        </section>
      </div>
    </>
  );
}