import type { <PERSON><PERSON><PERSON>, View<PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google"
import "./globals.css"
import "../styles/prism-theme.css"
import "../components/chat/thinking-styles.css"
import { Providers } from "./providers"
import { ConditionalFooter } from "@/components/layout/ConditionalFooter"
import { MainLayout } from "@/components/layout/MainLayout"
import { Toaster } from "sonner"
import { Inter } from 'next/font/google'
import { Header } from '@/components/layout/Header'
import { cn } from "@/lib/utils"
import { organizationSchema, applicationSchema } from "@/lib/metadata"
import Script from 'next/script'
import { ResourceHints } from '@/components/seo/ResourceHints'
import { BreadcrumbStructuredData } from '@/components/seo/BreadcrumbStructuredData'
import { generatePageStructuredData } from '@/lib/structured-data-enhanced'
// import { Analytics } from '@vercel/analytics/react'

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
})

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
})

const inter = Inter({ subsets: ['latin'] })

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://justsimple.chat'

export const metadata: Metadata = {
  metadataBase: new URL(BASE_URL),
  title: {
    default: "JustSimpleChat - Every AI Model, One Platform",
    template: "%s | JustSimpleChat"
  },
  description: "Access GPT-4, Claude, Gemini, and 200+ AI models through one intelligent platform. Smart routing picks the perfect AI for every task. Stop juggling multiple AI apps.",
  keywords: [
    "AI chat platform",
    "ChatGPT alternative",
    "Claude alternative", 
    "multiple AI models",
    "AI model comparison",
    "cheaper than ChatGPT",
    "GPT-4 access",
    "Claude access",
    "Gemini access",
    "AI chat aggregator",
    "unified AI platform"
  ],
  authors: [{ name: "JustSimpleChat Team" }],
  creator: "JustSimpleChat",
  publisher: "JustSimpleChat",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: BASE_URL,
    siteName: 'JustSimpleChat',
    title: 'JustSimpleChat - Every AI Model, One Platform',
    description: 'Access GPT-4, Claude, Gemini, and 200+ AI models through one intelligent platform. Smart routing picks the perfect AI for every task.',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'JustSimpleChat - Multiple AI Models in One Platform',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'JustSimpleChat - Every AI Model, One Platform',
    description: 'Access GPT-4, Claude, Gemini, and 200+ AI models through one intelligent platform. Smart routing picks the perfect AI for every task.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/icon.svg', type: 'image/svg+xml' },
    ],
    apple: [
      { url: '/apple-icon.png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
      },
    ],
  },
  manifest: '/manifest.json',
  alternates: {
    canonical: BASE_URL,
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
  },
  other: {
    'msapplication-TileColor': '#ffffff',
    'theme-color': '#ffffff',
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

// Google Ads conversion tracking function
const googleAdsScript = `
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'AW-17284585299');
`

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="h-full">
      <head>
        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="dns-prefetch" href="https://api.openai.com" />
        <link rel="dns-prefetch" href="https://api.anthropic.com" />
        
        {/* Google Ads Conversion Tracking */}
        <Script
          id="google-ads-gtag"
          src="https://www.googletagmanager.com/gtag/js?id=AW-17284585299"
          strategy="afterInteractive"
        />
        <Script
          id="google-ads-config"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: googleAdsScript
          }}
        />
        
        {/* Enhanced Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(generatePageStructuredData('home', {
              breadcrumbs: [
                { name: 'Home', url: '/' }
              ]
            }))
          }}
        />
      </head>
      <body
        className={cn(
          geistSans.variable,
          geistMono.variable,
          inter.className,
          "antialiased bg-neutral-950 h-full"
        )}
      >
        <Providers>
          <ResourceHints />
          <BreadcrumbStructuredData />
          <MainLayout>
            <Header />
            {children}
            <ConditionalFooter />
          </MainLayout>
          <Toaster 
            position="top-center"
            toastOptions={{
              style: {
                background: '#1f2937',
                color: '#f3f4f6',
                border: '1px solid #374151',
              },
            }}
          />
          {/* <Analytics /> */}
        </Providers>
      </body>
    </html>
  )
}