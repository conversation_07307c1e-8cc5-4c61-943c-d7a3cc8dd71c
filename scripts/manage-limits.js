#!/usr/bin/env node

/**
 * CLI tool to manage rate limits and message limits
 * Useful for testing and development
 */

const Redis = require('ioredis');
const { PrismaClient } = require('@prisma/client');
const { createHash } = require('crypto');

const redis = new Redis({
  host: 'localhost',
  port: 6379,
  maxRetriesPerRequest: 3,
});

const prisma = new PrismaClient();

const commands = {
  'clear-anonymous': clearAnonymousRateLimits,
  'clear-user': clearUserMessageLimits,
  'show-anonymous': showAnonymousRateLimits,
  'show-user': showUserMessageLimits,
  'clear-all': clearAllLimits,
  'add-bypass-ip': addBypassIP,
  'remove-bypass-ip': removeBypassIP,
  'list-bypass-ips': listBypassIPs,
};

async function clearAnonymousRateLimits() {
  console.log('🧹 Clearing anonymous rate limits...\n');
  
  try {
    const keys = await redis.keys('ratelimit:anon:*');
    
    if (keys.length === 0) {
      console.log('✅ No rate limit entries found.');
      return;
    }
    
    console.log(`Found ${keys.length} rate limit entries.`);
    const deleted = await redis.del(...keys);
    console.log(`✅ Successfully deleted ${deleted} rate limit entries.\n`);
    
  } catch (error) {
    console.error('❌ Error clearing rate limits:', error);
  }
}

async function clearUserMessageLimits(email) {
  if (!email) {
    console.error('❌ Please provide a user email: manage-limits clear-user <email>');
    return;
  }
  
  console.log(`🧹 Clearing message limits for user: ${email}\n`);
  
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        name: true,
        messagesUsedThisMonth: true,
        premiumMessagesUsedThisMonth: true,
      }
    });
    
    if (!user) {
      console.error('❌ User not found');
      return;
    }
    
    console.log(`Found user: ${user.name || user.id}`);
    console.log(`Current usage: ${user.messagesUsedThisMonth} standard, ${user.premiumMessagesUsedThisMonth || 0} premium`);
    
    await prisma.user.update({
      where: { id: user.id },
      data: {
        messagesUsedThisMonth: 0,
        premiumMessagesUsedThisMonth: 0,
        lastResetDate: new Date(),
      }
    });
    
    console.log('✅ Successfully reset message limits.\n');
    
  } catch (error) {
    console.error('❌ Error clearing user limits:', error);
  }
}

async function showAnonymousRateLimits() {
  console.log('📊 Anonymous Rate Limits:\n');
  
  try {
    const keys = await redis.keys('ratelimit:anon:*');
    
    if (keys.length === 0) {
      console.log('No rate limit entries found.');
      return;
    }
    
    for (const key of keys) {
      const identifier = key.replace('ratelimit:anon:', '');
      const count = await redis.zcard(key);
      const ttl = await redis.ttl(key);
      
      console.log(`Identifier: ${identifier}`);
      console.log(`Messages used: ${count}/5`);
      console.log(`TTL: ${ttl > 0 ? `${Math.floor(ttl / 3600)} hours ${Math.floor((ttl % 3600) / 60)} minutes` : 'No expiry'}`);
      console.log('---');
    }
    
  } catch (error) {
    console.error('❌ Error showing rate limits:', error);
  }
}

async function showUserMessageLimits(email) {
  if (!email) {
    console.error('❌ Please provide a user email: manage-limits show-user <email>');
    return;
  }
  
  console.log(`📊 Message Limits for ${email}:\n`);
  
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        name: true,
        plan: true,
        messagesUsedThisMonth: true,
        premiumMessagesUsedThisMonth: true,
        lastResetDate: true,
      }
    });
    
    if (!user) {
      console.error('❌ User not found');
      return;
    }
    
    const limits = {
      FREE: { standard: 0, premium: 0 },
      FREEMIUM: { standard: 10, premium: 0 },
      PLUS: { standard: 1500, premium: 100 },
      ADVANCED: { standard: 4500, premium: 200 },
      MAX: { standard: 7000, premium: 300 },
      ENTERPRISE: { standard: 1000000, premium: 1000000 },
    };
    
    const userLimits = limits[user.plan] || { standard: 0, premium: 0 };
    
    console.log(`User: ${user.name || email}`);
    console.log(`Plan: ${user.plan}`);
    console.log(`Standard Messages: ${user.messagesUsedThisMonth}/${userLimits.standard}`);
    console.log(`Premium Messages: ${user.premiumMessagesUsedThisMonth || 0}/${userLimits.premium}`);
    console.log(`Last Reset: ${user.lastResetDate.toLocaleDateString()}`);
    
    const nextReset = new Date(user.lastResetDate);
    nextReset.setMonth(nextReset.getMonth() + 1);
    nextReset.setDate(1);
    console.log(`Next Reset: ${nextReset.toLocaleDateString()}\n`);
    
  } catch (error) {
    console.error('❌ Error showing user limits:', error);
  }
}

async function clearAllLimits() {
  console.log('🧹 Clearing ALL limits...\n');
  await clearAnonymousRateLimits();
  console.log('\n✅ All anonymous rate limits cleared.\n');
}

async function addBypassIP(ip) {
  if (!ip) {
    console.error('❌ Please provide an IP address: manage-limits add-bypass-ip <ip>');
    return;
  }
  
  try {
    await redis.sadd('ratelimit:bypass:ips', ip);
    console.log(`✅ Added ${ip} to bypass list`);
  } catch (error) {
    console.error('❌ Error adding bypass IP:', error);
  }
}

async function removeBypassIP(ip) {
  if (!ip) {
    console.error('❌ Please provide an IP address: manage-limits remove-bypass-ip <ip>');
    return;
  }
  
  try {
    const removed = await redis.srem('ratelimit:bypass:ips', ip);
    if (removed) {
      console.log(`✅ Removed ${ip} from bypass list`);
    } else {
      console.log(`❌ IP ${ip} not found in bypass list`);
    }
  } catch (error) {
    console.error('❌ Error removing bypass IP:', error);
  }
}

async function listBypassIPs() {
  try {
    const ips = await redis.smembers('ratelimit:bypass:ips');
    if (ips.length === 0) {
      console.log('No bypass IPs configured');
    } else {
      console.log('Bypass IPs:');
      ips.forEach(ip => console.log(`  - ${ip}`));
    }
  } catch (error) {
    console.error('❌ Error listing bypass IPs:', error);
  }
}

async function showHelp() {
  console.log(`
JustSimpleChat Limits Management Tool

Usage: node manage-limits.js <command> [args]

Commands:
  clear-anonymous          Clear all anonymous user rate limits
  clear-user <email>       Clear message limits for a specific user
  show-anonymous           Show all current anonymous rate limits
  show-user <email>        Show message limits for a specific user
  clear-all                Clear all rate limits (anonymous only)
  add-bypass-ip <ip>       Add an IP to rate limit bypass list
  remove-bypass-ip <ip>    Remove an IP from bypass list
  list-bypass-ips          List all bypass IPs
  help                     Show this help message

Examples:
  node manage-limits.js clear-anonymous
  node manage-limits.js clear-user <EMAIL>
  node manage-limits.js show-anonymous
  node manage-limits.js add-bypass-ip *************
`);
}

// Main execution
async function main() {
  const [command, ...args] = process.argv.slice(2);
  
  if (!command || command === 'help') {
    await showHelp();
  } else if (commands[command]) {
    await commands[command](...args);
  } else {
    console.error(`❌ Unknown command: ${command}`);
    console.error('Run "node manage-limits.js help" for usage information');
  }
  
  await redis.disconnect();
  await prisma.$disconnect();
}

main().catch(console.error);