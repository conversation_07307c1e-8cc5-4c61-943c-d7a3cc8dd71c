#!/usr/bin/env node

/**
 * <PERSON>ript to clear rate limits for testing
 * This will remove all rate limit entries from Redis
 */

const Redis = require('ioredis');

async function clearRateLimits() {
  console.log('🧹 Clearing rate limits for testing...\n');
  
  // Connect to Redis
  const redis = new Redis({
    host: 'localhost',
    port: 6379,
    maxRetriesPerRequest: 3,
  });

  try {
    // Get all rate limit keys
    const keys = await redis.keys('ratelimit:anon:*');
    
    if (keys.length === 0) {
      console.log('✅ No rate limit entries found.');
      return;
    }
    
    console.log(`Found ${keys.length} rate limit entries:`);
    
    // Show current usage for each key
    for (const key of keys) {
      const count = await redis.zcard(key);
      const identifier = key.replace('ratelimit:anon:', '');
      console.log(`  - ${identifier}: ${count} messages`);
    }
    
    // Delete all rate limit keys
    console.log('\n🗑️  Deleting all rate limit entries...');
    const deleted = await redis.del(...keys);
    
    console.log(`✅ Successfully deleted ${deleted} rate limit entries.\n`);
    
  } catch (error) {
    console.error('❌ Error clearing rate limits:', error);
  } finally {
    redis.disconnect();
  }
}

// Check for specific IP to clear
const args = process.argv.slice(2);
if (args.length > 0 && args[0] === '--help') {
  console.log(`
Usage: node clear-rate-limits.js [options]

Options:
  --help     Show this help message
  
This script clears all anonymous user rate limits from Redis.
Rate limits are used to enforce the 5 free messages per 24 hours for non-authenticated users.
  `);
  process.exit(0);
}

clearRateLimits();