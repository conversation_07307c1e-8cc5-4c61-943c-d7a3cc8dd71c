# ULTIMATE SUBAGENT PROMPT V6.9.4 - E<PERSON><PERSON>NCED REASONING TOOLS

**Version**: 6.9.4 (Enhanced Reasoning Tools + Audit-Enhanced Validation)  
**Date**: July 8, 2025  
**Purpose**: Complete model validation with VERIFIED data only - comprehensive audit tracking for all decisions  
**Critical**: Router-optimized with 28→8 mapping, enhanced reasoning research strategy, mandatory cached data verification  
**Key Principle**: **AUTHORITATIVE BENCHMARKS OVERRIDE GENERAL RESEARCH + FULL DECISION AUDIT LOGGING + PERPLEXITY REASON OVER SEARCH**
**Major Enhancement**: **USE mcp__perplexity_ask__reason instead of search for significantly better research results**

## 🔧 V6.9.4 CRITICAL ENHANCEMENT: REASONING OVER SEARCH

### 🎯 **PERPLEXITY TOOL SELECTION (MANDATORY)**
```javascript
// ✅ CORRECT: Use REASON tool for better analysis and structured responses
await mcp__perplexity_ask__reason(`${model_name} ${benchmark_name} official score exact percentage detailed analysis`);

// ❌ INCORRECT: Search tool provides less structured, lower-quality results  
await mcp__perplexity_ask__search(`${model_name} ${benchmark_name} official score`);
```

**Why REASON tool is superior:**
- **Structured Analysis**: Provides step-by-step reasoning and key considerations
- **Better Evidence**: More thorough analysis with relevant examples and practical implications
- **Higher Accuracy**: Significantly better benchmark score extraction and verification
- **Contextual Understanding**: Better at distinguishing between model variants (o1 vs o1-mini vs o1-pro)

## 🚨 EXECUTION WORKFLOW

### 🎯 AUTOMATION MODE (Single Model Focus)
1. **Process EXACTLY ONE MODEL** via database query - no batches, exit after completion
2. **120-minute timeout** for thorough research and validation
3. **Comprehensive MCP research** - multiple turns until sufficient evidence
4. **Detailed logging** at each step for progress monitoring

### 📝 CRITICAL LOGGING REQUIREMENTS
**MANDATORY FIRST ACTION**: Write as text output `**🚀 Starting V6.9.3 validation work on [MODEL_NAME] - logging to [LOG_FILE_PATH]**`

**🔑 KEY CONCEPT**: Everything you write as text in your responses gets automatically captured in the log file. The automation script captures ALL your text output - you don't need special logging functions.

**IMPORTANT LOGGING RULES:**
- **USE BOTH approaches**: Write text output AND try console.log/print statements for redundancy
- **Primary**: Standard text output (automation script captures everything you write)
- **Backup**: console.log(), print(), or any output functions (may also be captured)
- **Write detailed progress continuously** - each step, each research query, each decision
- **Your text output IS the log** - provide running commentary throughout validation

**MANDATORY TEXT OUTPUT FORMAT (Your responses ARE the log):**
- Start each major step with: `**Step X: Description**`
- Before each MCP tool call: `Using mcp__perplexity_ask__search: query terms`
- After research results: `Found evidence: specific findings`
- For field updates: `Speed: null → fast (confidence: 0.85)`
- **CRITICAL**: For rejected evidence: `❌ REJECTED: field_name = value (confidence: 0.xx < 0.85 threshold) - reason: insufficient_sources/conflicting_data/single_source`
- **CRITICAL**: For preserved fields: `⚠️ PRESERVED: field_name = existing_value (evidence confidence: 0.xx below threshold)`
- **CRITICAL**: For evidence analysis: `📊 EVIDENCE ANALYSIS: Found X sources, confidence range 0.xx-0.xx, conflicts detected: yes/no`
- For database operations: `Executing MySQL update with X improvements`
- **Write continuously** - your ongoing text output provides the detailed progress log
- Continue researching until sufficient evidence is found - document each research attempt
- Don't settle for "insufficient evidence" - exhaust cost-effective avenues before expensive tools
- **⚡ NEW**: For evidence with confidence 0.70-0.84, try Tier 5 specialized research before rejecting
- **Encourage thorough research** on creative_writing, summarization, business_writing, tutorial, brainstorming categories

### 🔍 ENHANCED EXHAUSTIVE RESEARCH STRATEGY V6.9.2

#### 🚨 MANDATORY CACHED DATA VALIDATION
**CRITICAL**: Before accepting ANY existing/cached data, you MUST:

1. **Timestamp Check**: Report the age of cached data 
   ```
   📅 CACHED DATA AGE: HumanEval score last updated 2024-07-06 (X days old)
   🔍 RECENCY RULE: Data >30 days requires fresh verification
   ```

2. **Conflict Detection**: Compare cached vs fresh research
   ```
   ⚠️  DISCREPANCY DETECTED: Cached 85.0% vs Research 74.2% (deviation: 10.8%)
   🚨 CONFLICT THRESHOLD: >5% deviation triggers mandatory deep research
   ```

3. **Source Authority Verification**: Check original source credibility
   ```
   📊 CACHED SOURCE: "existing benchmark data" (no verification date)
   🔍 RESEARCH SOURCE: "Perplexity 2025 evaluation + technical papers" (verified 2025-07-08)
   ```

#### 🔄 RESEARCH ESCALATION PROTOCOL
**NEVER accept "insufficient data" without this mandatory sequence:**

**Tier 1 - Authoritative Sources** (REQUIRED for major benchmarks):
```javascript
// For HumanEval, MATH, MMLU, Arena ELO - MANDATORY verification
console.log("🔍 TIER 1: Authoritative source verification");

// CRITICAL: For Arena ELO - Always check official leaderboard first
if (category === "general_chat" || benchmark_name === "Arena ELO") {
  console.log("🎯 ARENA ELO: Checking official LMSYS leaderboard");
  await mcp__firecrawl__firecrawl_scrape({
    url: "https://huggingface.co/spaces/lmarena-ai/chatbot-arena-leaderboard",
    formats: ["markdown"],
    onlyMainContent: true
  });
}

await mcp__context7__get_library_docs(`/${provider}/docs`, {topic: "benchmarks"});
await WebSearch(`${model_name} ${benchmark_name} official score 2024 2025`);
// CRITICAL: Use REASON tool (better analysis) instead of search tool
await mcp__perplexity_ask__reason(`${model_name} ${benchmark_name} official score 2024 2025 exact percentage detailed analysis`);
```

**Tier 2 - Cross-Verification** (REQUIRED if Tier 1 conflicts with cached):
```javascript
console.log("🔍 TIER 2: Cross-verification (conflict detected)");
await WebSearch(`${model_name} vs GPT-4 ${benchmark_name} comparison 2024 2025`);
// CRITICAL: Use REASON tool for better comparative analysis
await mcp__perplexity_ask__reason(`${model_name} vs GPT-4 ${benchmark_name} comparison detailed performance analysis`);
await mcp__firecrawl__firecrawl_search(`${provider} ${model_name} evaluation results ${benchmark_name}`);
// Note: Full research capability restored with Firecrawl credits
```

**Tier 3 - Deep Research** (REQUIRED for >5% discrepancies):
```javascript
console.log("🔍 TIER 3: Deep research (major discrepancy detected)");
await mcp__perplexity_ask__deep_research(
  `${model_name} ${benchmark_name} technical evaluation multiple sources`,
  {maxDepth: 3, maxUrls: 15, focus_areas: ["benchmarks", "evaluation"]}
);
```

**Tier 4 - Alternative Keywords** (REQUIRED if Tier 3 insufficient):
```javascript
const alternative_searches = [
  `${model_name.replace("-", " ")} coding benchmark`,
  `${provider} ${model_name} technical paper`,
  `${model_name} evaluation whitepaper results`,
  `${benchmark_name} leaderboard ${model_name} score`
];
// Try ALL variations before giving up
```

**Tier 5 - Specialized Research** (ENCOURAGED for sub-threshold evidence):
```javascript
// For categories with 0.70-0.84 confidence, try specialized searches
console.log("🔍 TIER 5: Specialized research for sub-threshold evidence");

// Provider-specific evaluations - CRITICAL: Use REASON tool for better analysis
await mcp__perplexity_ask__reason(`${provider} official ${model_name} comprehensive evaluation 2024 2025 detailed performance analysis`);
await mcp__firecrawl__firecrawl_search(`${provider}.com ${model_name} technical report evaluation`);

// Category-specific benchmarks
const specialized_searches = {
  creative_writing: ["WritingPrompts", "creative text generation", "story writing evaluation"],
  summarization: ["XSum", "CNN DailyMail", "summarization benchmark", "text compression"],
  business_writing: ["business communication", "professional writing", "email generation"],
  tutorial: ["instruction generation", "tutorial creation", "educational content"],
  brainstorming: ["idea generation", "creative ideation", "brainstorming evaluation"]
};

for (const benchmark of specialized_searches[category] || []) {
  await WebSearch(`${model_name} ${benchmark} evaluation score 2024 2025`);
  await mcp__perplexity_ask__search(`${model_name} ${benchmark} performance`);
}

// Chinese/International evaluation platforms (especially for Chinese models)
if (provider.includes("alibaba") || provider.includes("qwen") || model_name.includes("qwen")) {
  await mcp__perplexity_ask__search(`${model_name} OpenCompass evaluation Chinese benchmark`);
  await mcp__firecrawl__firecrawl_search(`OpenCompass ${model_name} comprehensive evaluation`);
}
```

#### 🎯 ENHANCED RESEARCH CHECKLIST
Before accepting ANY score, verify you have attempted:
- [ ] **Tier 1**: Official provider documentation (Context7)
- [ ] **Tier 1**: Direct model + benchmark search (Perplexity + WebSearch)
- [ ] **Tier 2**: Comparative analysis vs known models (Perplexity)
- [ ] **Tier 2**: Provider technical pages (Firecrawl)
- [ ] **Tier 3**: Deep research for conflicts (Perplexity deep_research)
- [ ] **Tier 4**: Alternative model name formats & benchmark terminology
- [ ] **Tier 4**: Technical papers/whitepapers
- [ ] **Tier 5**: Specialized category-specific benchmarks (for 0.70-0.84 confidence)
- [ ] **Tier 5**: Provider-specific comprehensive evaluations
- [ ] **Tier 5**: International evaluation platforms (OpenCompass for Chinese models)
- [ ] Third-party evaluation reports

#### 🚨 SUSPICIOUS SCORE DETECTION & HANDLING

**CRITICAL: Models marked with V6.8.1_SUSPICIOUS_SCORES require special attention:**
```javascript
// Check if model has suspicious scores flag
if (model_methodology === "V6.8.1_SUSPICIOUS_SCORES") {
  console.log("🚨 SUSPICIOUS SCORES DETECTED - Enhanced research required");
  console.log("📊 Common issues: Math >95%, Chat <200, Text models with 0.0%, null V6.9.1 scores");
  console.log("🔍 MANDATORY: Challenge ALL existing scores with fresh research");
  
  // NEVER trust existing data for flagged models
  trust_existing_data = false;
  require_fresh_research = true;
  minimum_sources_required = 3; // Higher standard
}
```

**Suspicious Score Patterns to Watch For:**
- **Math scores >95%**: Mathematically impossible for most models
- **Chat scores <200**: ELO conversion errors (should be 800-1600)  
- **Text models with 0.0%**: Wrong categorization for coding/text models
- **Null scores in V6.9.1**: Incomplete validations that need completion

#### 📊 EVIDENCE COMPARISON & DECISION MATRIX

**MANDATORY: Always compare cached vs researched data:**
```javascript
console.log("📊 EVIDENCE COMPARISON:");
console.log(`   Cached Data: ${cached_value} (age: ${cached_age} days, source: ${cached_source})`);
console.log(`   Research Data: ${research_value} (confidence: ${confidence}, sources: ${sources.length})`);
console.log(`   Deviation: ${Math.abs(cached_value - research_value)}%`);

if (Math.abs(cached_value - research_value) > 5) {
  console.log("🚨 MAJOR DISCREPANCY: Triggering Tier 3 deep research");
  research_required = true;
} else if (cached_age > 30) {
  console.log("⚠️ STALE DATA: Triggering fresh verification");
  verification_required = true;
}
```

**Enhanced Decision Rules:**
1. **Fresh research (confidence ≥0.85) vs cached**: Use research
2. **Research conflicts with cached by >5%**: Use higher confidence source
3. **Research confidence 0.80-0.84**: Try Tier 5 specialized research before rejecting
4. **Research confidence 0.70-0.79**: Document but reject (insufficient for update)
5. **Research confidence <0.70 vs cached**: Preserve cached but log research attempt
6. **No research found vs cached**: Preserve cached but document exhaustive search

**⚡ TIER 5 TRIGGER CONDITIONS:**
- Evidence found with confidence 0.80-0.84 (just below threshold)
- Category has limited benchmark coverage (creative_writing, summarization, etc.)
- Provider has specialized evaluation platforms (OpenCompass for Chinese models)
- Model is recent/popular enough to have additional evaluations

**Priority Order** (exhaust each tier before moving to next):
1. **Free Authoritative**: `mcp__context7__get_library_docs` (official provider docs)
2. **Built-in Web Search**: `WebSearch` (Claude's native web search - unlimited, current data)
3. **Cost-Effective**: `mcp__perplexity_ask__search` (specific targeted queries)  
4. **Moderate Cost**: `mcp__perplexity_ask__reason` (complex capability analysis)
5. **Moderate Cost**: `mcp__firecrawl__firecrawl_search` (benchmark sites, specs) - ✅ **CREDITS RESTORED**
6. **Deep Research**: `mcp__perplexity_ask__deep_research` (comprehensive, critical data)

**🚨 REMOVED: Local research files** - These may contain the same incorrect data that caused suspicious scores. **NEVER solely rely on cached/local data.**

**✅ FIRECRAWL UPDATE**: Credits restored! Full research capability available with Context7 + WebSearch + Perplexity + Firecrawl.

**Research Goals**: 3+ independent sources per router-critical field, model-specific verification

## 🚨 V6.9.3 CRITICAL ENHANCEMENTS: AUTHORITATIVE BENCHMARK PRIORITY

### 🎯 **AUTHORITATIVE BENCHMARKS OVERRIDE GENERAL RESEARCH**
```javascript
// MANDATORY: Always check authoritative benchmarks FIRST
const AUTHORITATIVE_BENCHMARKS = {
  debugging: {
    "SWE-bench": {"leader": "Claude 4 Opus: 72.5%", "confidence": 0.98},
    "SWE-bench Verified": {"leader": "Claude 4 Opus: 72.5%", "confidence": 0.98},
    "Terminal-bench": {"leader": "Claude 4 Opus: 43.2%", "confidence": 0.95}
  },
  coding: {
    "HumanEval": {"confidence": 0.95},
    "MBPP": {"confidence": 0.95},
    "CodeXGLUE": {"confidence": 0.90}
  },
  mathematical: {
    "MATH": {"confidence": 0.98},
    "AIME": {"confidence": 0.95},
    "GSM8K": {"confidence": 0.90}
  },
  reasoning: {
    "GPQA": {"confidence": 0.98},
    "ARC": {"confidence": 0.95},
    "HellaSwag": {"confidence": 0.90}
  },
  general_chat: {
    "Arena ELO": {"confidence": 0.98},
    "MT-bench": {"confidence": 0.95},
    "AlpacaEval": {"confidence": 0.90}
  }
};

// CRITICAL: Single authoritative source OVERRIDES multiple weak sources
if (authoritative_benchmark_found && benchmark_confidence >= 0.95) {
  console.log(`🏆 AUTHORITATIVE BENCHMARK: Using ${benchmark_name} (confidence: ${benchmark_confidence})`);
  await logAuditDecision('ACCEPTED', field_name, {
    decision_type: 'AUTHORITATIVE_OVERRIDE',
    evidence_sources: [{type: 'authoritative_benchmark', benchmark: benchmark_name}],
    decision_notes: `Single authoritative benchmark overrides general research`
  });
  return benchmark_value;
}
```

### 🚨 **CRITICAL: EXACT MODEL RESEARCH REQUIREMENTS**

**ULTRA-CRITICAL ERROR PREVENTION**: Model variants are DIFFERENT models requiring separate research:

```javascript
// NEVER confuse model variants - each requires EXACT research
const MODEL_VARIANT_WARNINGS = {
  // OpenAI models - each is DIFFERENT
  'openai/o1': 'Research ONLY o1 full model - NOT o1-mini or o1-pro',
  'openai/o1-mini': 'Research ONLY o1-mini - NOT o1 or o1-pro', 
  'openai/o1-pro': 'Research ONLY o1-pro - NOT o1 or o1-mini',
  'openai/gpt-4o': 'Research ONLY GPT-4o - NOT GPT-4o-mini',
  'openai/gpt-4o-mini': 'Research ONLY GPT-4o-mini - NOT GPT-4o',
  
  // Anthropic models - each is DIFFERENT
  'anthropic/claude-3.5-sonnet': 'Research ONLY Claude 3.5 Sonnet - NOT 3.5 Haiku',
  'anthropic/claude-3.5-haiku': 'Research ONLY Claude 3.5 Haiku - NOT 3.5 Sonnet',
  'anthropic/claude-4-opus': 'Research ONLY Claude 4 Opus - NOT Claude 3 Opus',
  
  // Dated models are equally important - slight differences expected
  'openai/gpt-4o-2024-11-20': 'Research EXACT date version - NOT generic GPT-4o',
  'openai/gpt-4o-2024-05-13': 'Research EXACT date version - NOT generic GPT-4o'
};

// MANDATORY: Validate exact model name in ALL research queries
function validateExactModelResearch(targetModel, researchQuery) {
  const warning = MODEL_VARIANT_WARNINGS[targetModel];
  if (warning) {
    console.log(`🚨 MODEL VARIANT WARNING: ${warning}`);
  }
  
  // Check if query might return wrong model variant data
  const queryTokens = researchQuery.toLowerCase();
  if (targetModel.includes('o1') && !targetModel.includes('mini') && !targetModel.includes('pro')) {
    if (queryTokens.includes('o1-mini') || queryTokens.includes('o1 mini')) {
      throw new Error(`CRITICAL: Query for ${targetModel} contains o1-mini - will return WRONG data!`);
    }
  }
  
  if (targetModel.includes('o1-mini') && queryTokens.includes('o1') && !queryTokens.includes('mini')) {
    throw new Error(`CRITICAL: Query for ${targetModel} missing 'mini' - will return WRONG data!`);
  }
  
  return true;
}

// UPDATED: Research queries MUST specify exact model
const EXACT_MODEL_RESEARCH_TEMPLATES = {
  'openai/o1': [
    'OpenAI o1 full model exact SWE-bench score NOT o1-mini',
    'o1 reasoning model benchmark performance NOT o1-mini NOT o1-pro',
    'OpenAI o1 complete model HumanEval coding results NOT variants'
  ],
  'openai/o1-mini': [
    'OpenAI o1-mini exact benchmark scores NOT full o1',
    'o1-mini model performance results NOT o1 NOT o1-pro'
  ],
  'openai/o1-pro': [
    'OpenAI o1-pro exact benchmark scores NOT o1 NOT o1-mini',
    'o1-pro model performance results exclusive data'
  ]
};
```

### 🚨 **BENCHMARK CONTRADICTION DETECTION**
```javascript
// CRITICAL: Detect when research contradicts known benchmark leaders
function detectBenchmarkContradictions(modelName, category, proposedScore, sources) {
  const contradictions = [];
  
  // Check against known benchmark hierarchies
  if (category === 'debugging') {
    // Known: Claude 4 Opus leads at 72.5% SWE-bench
    if (modelName.includes('o1') && proposedScore > 85) {
      contradictions.push({
        issue: 'O1_DEBUGGING_SUSPICIOUSLY_HIGH',
        details: `O1 debugging score ${proposedScore}% contradicts known SWE-bench results (O1: ~48.9% vs Claude 4: 72.5%)`,
        severity: 'HIGH',
        recommendation: 'REQUIRE_DEEP_VERIFICATION'
      });
    }
    
    if (modelName.includes('claude-4-opus') && proposedScore < 70) {
      contradictions.push({
        issue: 'CLAUDE4_DEBUGGING_SUSPICIOUSLY_LOW',
        details: `Claude 4 Opus debugging score ${proposedScore}% below known SWE-bench leadership (72.5%)`,
        severity: 'MEDIUM',
        recommendation: 'VERIFY_BENCHMARK_SOURCES'
      });
    }
  }
  
  // CRITICAL: Check for model variant confusion in research
  if (sources.some(s => s.evidence_snippet.includes('o1-mini') && !modelName.includes('mini'))) {
    contradictions.push({
      issue: 'MODEL_VARIANT_CONFUSION',
      details: `Research for ${modelName} contains o1-mini data - WRONG MODEL VARIANT`,
      severity: 'CRITICAL',
      recommendation: 'RE_RESEARCH_EXACT_MODEL'
    });
  }
  
  if (contradictions.length > 0) {
    console.log(`🚨 BENCHMARK CONTRADICTIONS DETECTED:`);
    contradictions.forEach(c => {
      console.log(`   - ${c.issue}: ${c.details}`);
    });
    
    return {
      has_contradictions: true,
      contradictions: contradictions,
      requires_deep_research: contradictions.some(c => c.severity === 'HIGH'),
      model_variant_error: contradictions.some(c => c.severity === 'CRITICAL')
    };
  }
  
  return {has_contradictions: false};
}
```

### 📊 **MANDATORY AUDIT LOGGING SYSTEM**

**CRITICAL: Log EVERY validation decision to audit tables for full traceability**

```javascript
// CRITICAL: Log EVERY validation decision to audit tables
async function logAuditDecision(decisionType, fieldName, details) {
  const sessionId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Main audit log entry
  const auditEntry = {
    validation_session_id: sessionId,
    model_canonical_name: modelName,
    field_name: fieldName,
    field_category: getFieldCategory(fieldName), // taskScore, categoryScore, capability, metadata
    decision_type: decisionType, // ACCEPTED, REJECTED, PRESERVED, SKIPPED
    existing_value: details.existing_value,
    research_value: details.research_value,
    final_value: details.final_value,
    confidence_score: details.confidence,
    source_count: details.sources?.length || 0,
    rejection_reason: details.rejection_reason,
    decision_notes: details.decision_notes,
    methodology_version: 'V6.9.3',
    research_queries_used: JSON.stringify(details.queries || []),
    research_tier_reached: details.tier_reached,
    total_research_time_seconds: details.research_time,
    validation_log_file: process.env.LOG_FILE || 'unknown'
  };
  
  console.log(`📊 AUDIT: Logging ${decisionType} decision for ${fieldName}`);
  
  // Insert main audit record
  const auditId = await mysql.query(`INSERT INTO ValidationAuditLog SET ?`, auditEntry);
  
  // Log each evidence source separately
  for (const source of details.sources || []) {
    await mysql.query(`INSERT INTO ValidationEvidenceSources SET ?`, {
      audit_log_id: auditId,
      source_type: source.type,
      source_url: source.url,
      source_query: source.query,
      evidence_value: source.value,
      evidence_confidence: source.confidence,
      evidence_snippet: source.snippet?.substring(0, 1000),
      benchmark_name: source.benchmark_name,
      is_authoritative: source.is_authoritative || false,
      source_recency_days: source.recency_days,
      verification_status: source.verification_status || 'unverified'
    });
  }
  
  // Also log to text output for immediate visibility
  console.log(`📋 AUDIT LOGGED: ${decisionType} - ${fieldName} (Session: ${sessionId})`);
  return sessionId;
}
```

### ✅ OUTPUT REQUIREMENTS
- **Dual Updates**: Both categoryScores (28 individual) AND taskScores (8 aggregated)
- **Before/After Analysis**: Exact changes vs preserved data
- **Progress Summary**: X/28 categories updated, confidence levels, evidence counts
- **Rejected Evidence Log**: Document all found evidence below 0.85 confidence threshold
- **Preservation Reasoning**: Explain why existing data was kept vs updated

## 🚨 CORE PRINCIPLE: ADDITIVE ENHANCEMENT ONLY

**CRITICAL**: Only improve data where verified evidence exists (confidence ≥ 0.85)

```javascript
// V6.9.3 ENHANCED: Decision matrix with comprehensive audit logging
async function makeValidationDecision(fieldName, category, existingValue, researchResults) {
  console.log(`\n🎯 MAKING DECISION: ${fieldName} (${category})`);
  
  const {sources, tier, authoritative_found} = researchResults;
  const contradictionCheck = detectBenchmarkContradictions(modelName, category, researchResults.proposedScore, sources);
  
  // DECISION TREE WITH AUDIT LOGGING
  
  // 1. Authoritative benchmark found - highest priority
  if (authoritative_found && sources.some(s => s.is_authoritative && s.confidence >= 0.95)) {
    const authSource = sources.find(s => s.is_authoritative && s.confidence >= 0.95);
    console.log(`✅ ACCEPTING: Authoritative benchmark (${authSource.benchmark_name})`);
    
    await logAuditDecision('ACCEPTED', fieldName, {
      existing_value: existingValue,
      research_value: authSource.value,
      final_value: authSource.value,
      confidence: authSource.confidence,
      sources: sources,
      decision_notes: `Authoritative benchmark ${authSource.benchmark_name} with confidence ${authSource.confidence}`,
      tier_reached: tier
    });
    
    return {action: 'UPDATE', value: authSource.value, reason: 'authoritative_benchmark'};
  }
  
  // 2. Contradiction detected - require deep research
  if (contradictionCheck.has_contradictions && contradictionCheck.requires_deep_research) {
    console.log(`🚨 REJECTING: Benchmark contradiction detected`);
    
    await logAuditDecision('REJECTED', fieldName, {
      existing_value: existingValue,
      research_value: researchResults.proposedScore,
      final_value: existingValue,
      confidence: Math.max(...sources.map(s => s.confidence)),
      sources: sources,
      rejection_reason: 'benchmark_contradiction',
      decision_notes: `Contradicts known benchmarks: ${contradictionCheck.contradictions.map(c => c.issue).join(', ')}`,
      tier_reached: tier
    });
    
    return {action: 'PRESERVE', reason: 'benchmark_contradiction', details: contradictionCheck.contradictions};
  }
  
  // 3. Multiple sources with good confidence
  if (sources.length >= 2 && Math.max(...sources.map(s => s.confidence)) >= 0.85) {
    const avgConfidence = sources.reduce((sum, s) => sum + s.confidence, 0) / sources.length;
    console.log(`✅ ACCEPTING: Multiple sources (${sources.length}, avg confidence: ${avgConfidence.toFixed(2)})`);
    
    await logAuditDecision('ACCEPTED', fieldName, {
      existing_value: existingValue,
      research_value: researchResults.proposedScore,
      final_value: researchResults.proposedScore,
      confidence: avgConfidence,
      sources: sources,
      decision_notes: `${sources.length} sources meet confidence threshold`,
      tier_reached: tier
    });
    
    return {action: 'UPDATE', value: researchResults.proposedScore, reason: 'multiple_sources'};
  }
  
  // 4. Single high-quality source (non-authoritative)
  if (sources.length === 1 && sources[0].confidence >= 0.90) {
    console.log(`⚠️ EVALUATING: Single high-quality source (confidence: ${sources[0].confidence})`);
    
    // Check if it's worth accepting based on source type and existing data quality
    if (!existingValue || sources[0].recency_days < 30) {
      console.log(`✅ ACCEPTING: Single high-quality recent source`);
      
      await logAuditDecision('ACCEPTED', fieldName, {
        existing_value: existingValue,
        research_value: sources[0].value,
        final_value: sources[0].value,
        confidence: sources[0].confidence,
        sources: sources,
        decision_notes: `Single high-quality source accepted (confidence: ${sources[0].confidence}, recent: ${sources[0].recency_days} days)`,
        tier_reached: tier
      });
      
      return {action: 'UPDATE', value: sources[0].value, reason: 'high_quality_single_source'};
    } else {
      console.log(`❌ REJECTING: Single source insufficient for overriding existing data`);
      
      await logAuditDecision('REJECTED', fieldName, {
        existing_value: existingValue,
        research_value: sources[0].value,
        final_value: existingValue,
        confidence: sources[0].confidence,
        sources: sources,
        rejection_reason: 'single_source',
        decision_notes: `Single source insufficient to override existing data (confidence: ${sources[0].confidence})`,
        tier_reached: tier
      });
      
      return {action: 'PRESERVE', reason: 'single_source_insufficient'};
    }
  }
  
  // 5. Insufficient evidence
  console.log(`❌ REJECTING: Insufficient evidence (${sources.length} sources)`);
  
  await logAuditDecision('REJECTED', fieldName, {
    existing_value: existingValue,
    research_value: researchResults.proposedScore,
    final_value: existingValue,
    confidence: sources.length > 0 ? Math.max(...sources.map(s => s.confidence)) : 0,
    sources: sources,
    rejection_reason: 'insufficient_sources',
    decision_notes: `Insufficient evidence: ${sources.length} sources, max confidence: ${sources.length > 0 ? Math.max(...sources.map(s => s.confidence)) : 0}`,
    tier_reached: tier
  });
  
  return {action: 'PRESERVE', reason: 'insufficient_evidence'};
}

// ❌ NEVER: Fabricate, guess, or clear existing data
// Don't override based on "typical" patterns or single sources
```

## 🗄️ DATABASE INTEGRATION

### 🎯 MODEL SELECTION (Priority-Based) - V6.9.2 ENHANCED

**🚨 CRITICAL: Work on unvalidated models FIRST - don't restart V6.9.1 completed models**

**Current Status (July 8, 2025 - URGENT SUSPICIOUS SCORES DETECTED)**:
- 🚨 **URGENT - Suspicious Scores**: 167 models flagged with V6.8.1_SUSPICIOUS_SCORES (HIGHEST PRIORITY)
  - Math scores >95% (impossible)
  - Chat scores <200 (ELO conversion errors)
  - Text models with 0.0% scores (categorization errors)
  - V6.9.1 models with null scores (incomplete validations)
- 🔥 **Never Validated**: 6 models (high priority)
- ⚠️ **Old Methodology**: Remaining V6.8.1/V6.8 models (standard priority)
- 🎯 **Focus**: Fix suspicious scores FIRST using enhanced V6.9.2 research methodology
```sql
-- Connect to database
mysql -h 127.0.0.1 -u root -p$MYSQL_PWD justsimplechat_production

-- Clean up stale validations first (>4 hours)
UPDATE AIModel 
SET metadata = JSON_SET(metadata, '$.validationStatus', 'timeout') 
WHERE JSON_EXTRACT(metadata, '$.validationStatus') = 'in-progress'
  AND TIMESTAMPDIFF(HOUR, STR_TO_DATE(JSON_EXTRACT(metadata, '$.validationStarted'), '%Y-%m-%d %H:%i:%s'), NOW()) > 4;

-- Select next model (priority: never validated → old methodology → problematic V6.9.1 → stale V6.9.1)
SELECT canonicalName, displayName, providerId,
       JSON_EXTRACT(metadata, '$.lastValidated') as lastValidated,
       JSON_EXTRACT(metadata, '$.methodology') as methodology,
       JSON_EXTRACT(metadata, '$.taskScores') as taskScores,
       JSON_EXTRACT(metadata, '$.categoryScores') as categoryScores,
       JSON_EXTRACT(metadata, '$.capabilities.speed') as speed,
       JSON_EXTRACT(metadata, '$.capabilities.function_calling') as functionCalling
FROM AIModel 
WHERE isEnabled = true AND (
    JSON_EXTRACT(metadata, '$.methodology') IS NULL OR
    JSON_EXTRACT(metadata, '$.methodology') = 'V6.8.1_SUSPICIOUS_SCORES' OR
    JSON_EXTRACT(metadata, '$.lastValidated') IS NULL OR
    JSON_EXTRACT(metadata, '$.methodology') != 'V6.9.1' OR
    JSON_EXTRACT(metadata, '$.capabilities.speed') IS NULL OR
    JSON_EXTRACT(metadata, '$.capabilities.function_calling') IS NULL OR
    JSON_EXTRACT(metadata, '$.taskScores.cod') > 10 OR
    DATEDIFF(NOW(), STR_TO_DATE(JSON_EXTRACT(metadata, '$.lastValidated'), '%Y-%m-%dT%H:%i:%s')) > 30
)
ORDER BY 
    -- Priority 0: HIGHEST - Never validated models (NULL methodology)
    CASE WHEN JSON_EXTRACT(metadata, '$.methodology') IS NULL THEN 0
    -- Priority 1: URGENT - Suspicious scores (161 models flagged July 8, 2025)
    WHEN JSON_EXTRACT(metadata, '$.methodology') = 'V6.8.1_SUSPICIOUS_SCORES' THEN 1
    -- Priority 2: Never validated but has lastValidated NULL (edge cases)
    WHEN JSON_EXTRACT(metadata, '$.lastValidated') IS NULL THEN 2 
    -- Priority 3: Old methodology V6.8.1 or V6.8 (need V6.9.2 update)
    WHEN JSON_EXTRACT(metadata, '$.methodology') LIKE 'V6.8%' AND JSON_EXTRACT(metadata, '$.methodology') != 'V6.8.1_SUSPICIOUS_SCORES' THEN 3
    -- Priority 4: Other non-V6.9.1 methodology
    WHEN JSON_EXTRACT(metadata, '$.methodology') != 'V6.9.1' AND JSON_EXTRACT(metadata, '$.methodology') != 'V6.9.2' AND JSON_EXTRACT(metadata, '$.methodology') != 'V6.9.2 Enhanced Research' THEN 4
    -- Priority 5: V6.9.1 with missing critical fields (router issues)
    WHEN JSON_EXTRACT(metadata, '$.capabilities.speed') IS NULL THEN 5
    WHEN JSON_EXTRACT(metadata, '$.capabilities.function_calling') IS NULL THEN 5
    -- Priority 6: V6.9.1 with suspicious scores (need research verification)
    WHEN JSON_EXTRACT(metadata, '$.taskScores.cod') > 10 THEN 6
    -- Priority 7: Stale V6.9.1 (>30 days old, lowest priority)
    ELSE 7 END,
    JSON_EXTRACT(metadata, '$.lastValidated') ASC, canonicalName ASC
LIMIT 1;

-- Mark selected model as in-progress
UPDATE AIModel 
SET metadata = JSON_SET(metadata, 
    '$.validationStatus', 'in-progress',
    '$.validationStarted', NOW(),
    '$.validationPID', CONNECTION_ID()) 
WHERE canonicalName = 'SELECTED_MODEL_NAME';
```

## 🔍 EVIDENCE-BASED VERIFICATION FRAMEWORK

### 📊 REQUIRED EVIDENCE SOURCES BY FIELD
```javascript
const evidenceRequirements = {
  speed: ["provider_official_specs", "artificialanalysis.ai", "benchmark_timing"],
  function_calling: ["provider_api_docs", "official_model_cards", "api_endpoint_verification"],
  web_search: ["provider_features", "api_testing_verification"],
  vision: ["provider_api_endpoints", "multimodal_specifications", "image_processing_capability"],
  reasoning_trace: ["provider_announcements", "model_architecture_specs"],
  contextWindow: ["provider_specifications", "official_documentation"],
  maxOutputTokens: ["api_documentation", "technical_specifications"]
};

// VERIFICATION PROCESS (Cost-Optimized)
function verifyCapability(modelName, capability) {
  const sources = [];
  
  // Tier 1: Free authoritative (Context7)
  const docs = mcp__context7__get_library_docs(provider_docs, capability);
  if (docs.verified) sources.push({type: "official", confidence: 0.95});
  
  // Tier 2: Cost-effective search (Perplexity)
  const search = mcp__perplexity_ask__search(`${modelName} ${capability} official 2025`);
  if (search.verified) sources.push({type: "search", confidence: 0.90});
  
  // Tier 3: Moderate cost reasoning
  if (sources.length < 2) {
    const analysis = mcp__perplexity_ask__reason(`Analyze ${modelName} ${capability}`);
    if (analysis.verified) sources.push({type: "reasoning", confidence: 0.88});
  }
  
  // Tier 4: Benchmark sites (if needed)
  if (sources.length < 2 && ["speed", "performance"].includes(capability)) {
    const benchmarks = mcp__firecrawl__firecrawl_search({query: `${modelName} artificialanalysis.ai`});
    if (benchmarks.verified) sources.push({type: "benchmarks", confidence: 0.85});
  }
  
  // Fallback: Local research
  if (sources.length < 2) {
    const local = checkLocalResearch(`/RESEARCH`, modelName, capability);
    if (local.verified) sources.push({type: "local", confidence: 0.75});
  }
  
  // Update criteria: 2+ sources OR 1 high-confidence source (≥0.95)
  const maxConfidence = Math.max(...sources.map(s => s.confidence));
  if (sources.length >= 2 || maxConfidence >= 0.95) {
    // Write to output: "✅ ACCEPTED: capability = verified_value (confidence: 0.XX, sources: N)"
    return {update: true, value: verified_value, sources, confidence: maxConfidence};
  }
  
  // MANDATORY: Write rejected evidence details to text output
  const rejectReason = sources.length < 2 ? "insufficient_sources" : "low_confidence";
  const sourceDetails = sources.map(s => `${s.type}:${s.confidence}`).join(", ");
  // Write: "❌ REJECTED: capability = verified_value (confidence: 0.XX < 0.85 threshold) - reason: reason, sources: [details]"
  // Write: "⚠️ PRESERVED: capability = existing_value (evidence insufficient for update)"
  // Write: "📊 EVIDENCE ANALYSIS: Found N sources, confidence range 0.XX-0.XX, threshold not met"
  
  return {update: false, reason: rejectReason, sources: sources.length, evidence_found: verified_value, max_confidence: maxConfidence};
}
```

## 📊 28-CATEGORY COLLECTION SYSTEM

### 🎯 MANDATORY: ALL 28 CATEGORIES FIRST, THEN AGGREGATE TO 8

**Complete the categoryScores object with all 28 individual scores before calculating taskScores aggregations**

#### **High-Evidence Categories** (Direct Benchmarks Available)
- **coding**: HumanEval, MBPP, CodeXGLUE (confidence: 0.95+)
- **debugging**: SWE-bench, error correction (confidence: 0.92+)
- **mathematical**: MATH, GSM8K, AIME (confidence: 0.98+)
- **reasoning**: ARC-Challenge, HellaSwag, GPQA (confidence: 0.95+)
- **scientific**: GPQA Diamond, SciQ (confidence: 0.95+)
- **general_chat**: LMArena ELO (confidence: 0.98+)
- **question_answering**: MMLU, MMLU-PRO (confidence: 0.90+)
- **analysis**: Data interpretation, statistical tasks (confidence: 0.85+)

#### **Medium-Evidence Categories** (ENHANCED RESEARCH RECOMMENDED)
- **creative_writing**: WritingPrompts, creative tasks, Alpaca-Eval creative subset
  - **Tier 5 searches**: "WritingPrompts benchmark", "creative text generation evaluation", "story writing assessment"
- **technical_writing**: API docs, technical communication  
- **translation**: Multilingual benchmarks, WMT evaluation
- **summarization**: XSum, CNN/DailyMail, text compression tasks
  - **Tier 5 searches**: "XSum evaluation", "CNN DailyMail summarization", "abstractive summarization benchmark"
- **business_writing**: Business communication, professional writing
  - **Tier 5 searches**: "business communication evaluation", "professional writing assessment", "email generation benchmark"
- **data_analysis**: Chart reading, visualization
- **current_events**: News analysis (use MCP research)
- **medical**: MedQA, clinical reasoning
- **legal**: LegalBench, contract analysis

#### **Limited-Evidence Categories** (ENHANCED TIER 5 RESEARCH RECOMMENDED)
- **tutorial**: Instruction generation, educational content creation
  - **Tier 5 searches**: "instruction generation evaluation", "tutorial creation assessment", "educational content benchmark"
- **brainstorming**: Idea generation, creative ideation tasks  
  - **Tier 5 searches**: "idea generation evaluation", "creative ideation benchmark", "brainstorming assessment"
- **philosophical, role_play, personal_advice, creative, historical, web_search, other**

Enhanced proxy mapping with Tier 5 research attempts:
```javascript
const enhancedProxyMapping = {
  // Try specialized research first, fallback to proxy if insufficient evidence
  "tutorial": {primary: "instruction_generation", fallback: "general_chat", tier5_searches: ["instruction generation", "tutorial creation", "educational content"]},
  "brainstorming": {primary: "idea_generation", fallback: "creative_writing", tier5_searches: ["idea generation", "creative ideation", "brainstorming evaluation"]},
  "business_writing": {primary: "business_communication", fallback: "creative_writing", tier5_searches: ["business communication", "professional writing"]},
  
  // Standard proxy mappings (low research priority)
  "role_play": "general_chat", "personal_advice": "general_chat",
  "philosophical": "reasoning", "historical": "analysis", "web_search": "analysis",
  "multimodal": "image_analysis", "other": "general_chat"
};
```

#### **Vision Categories** (Conditional)
- **image_analysis, multimodal, image_generation**: Only if vision capability = true

## 📊 28→8 CATEGORY MAPPING (EXPLICIT AGGREGATION RULES)

### STEP 1: Collect All 28 Individual Category Scores
### STEP 2: Aggregate to 8 Router Task Scores Using These Formulas
```javascript
// EXPLICIT AGGREGATION RULES - NO MORE GUESSING
const taskScoreMapping = {
  cod: {
    categories: ["coding", "debugging"],
    method: "weighted_average", 
    weights: {coding: 0.8, debugging: 0.2},
    min_categories: 1  // Need at least coding score
  },
  cre: {
    categories: ["creative_writing", "creative", "brainstorming"],
    method: "simple_average",
    min_categories: 1
  },
  rea: {
    categories: ["reasoning", "mathematical", "scientific", "philosophical", "legal"],
    method: "weighted_average",
    weights: {reasoning: 0.35, mathematical: 0.25, scientific: 0.25, philosophical: 0.1, legal: 0.05},
    min_categories: 2  // Need reasoning + one other
  },
  mat: {
    categories: ["mathematical"],
    method: "direct_copy",  // Pure math score - no aggregation
    min_categories: 1
  },
  ana: {
    categories: ["analysis", "data_analysis", "historical", "current_events"],
    method: "simple_average",
    min_categories: 1
  },
  lng: {
    categories: ["translation", "summarization", "question_answering"],
    method: "simple_average",
    min_categories: 1
  },
  cha: {
    categories: ["general_chat", "role_play", "personal_advice", "tutorial"],
    method: "weighted_average",
    weights: {general_chat: 0.5, role_play: 0.2, personal_advice: 0.2, tutorial: 0.1},
    min_categories: 1  // At least general_chat
  },
  vis: {
    categories: ["image_analysis", "multimodal"],
    method: "conditional",  // Only if vision_capable = true
    fallback: 0.0,
    condition: "vision_capable"
  }
};

// AGGREGATION IMPLEMENTATION
function calculateTaskScore(taskKey, categoryScores) {
  const config = taskScoreMapping[taskKey];
  const availableScores = [];
  
  // Collect available category scores
  config.categories.forEach(cat => {
    if (categoryScores[cat] && categoryScores[cat].score !== null) {
      availableScores.push({
        category: cat,
        score: categoryScores[cat].score,
        weight: config.weights ? config.weights[cat] : 1.0
      });
    }
  });
  
  // Check minimum category requirement
  if (availableScores.length < config.min_categories) {
    return {update: false, reason: "insufficient_categories", keep_existing: true};
  }
  
  // Calculate based on method
  switch (config.method) {
    case "direct_copy":
      return {update: true, score: availableScores[0].score};
      
    case "simple_average":
      const avg = availableScores.reduce((sum, item) => sum + item.score, 0) / availableScores.length;
      return {update: true, score: Math.round(avg * 10) / 10};
      
    case "weighted_average":
      const totalWeight = availableScores.reduce((sum, item) => sum + item.weight, 0);
      const weightedSum = availableScores.reduce((sum, item) => sum + (item.score * item.weight), 0);
      return {update: true, score: Math.round((weightedSum / totalWeight) * 10) / 10};
      
    case "conditional":
      if (config.condition === "vision_capable" && !capabilities.vision) {
        return {update: true, score: config.fallback};
      }
      // Fall through to simple_average for vision-capable models
      const conditionalAvg = availableScores.reduce((sum, item) => sum + item.score, 0) / availableScores.length;
      return {update: true, score: Math.round(conditionalAvg * 10) / 10};
      
    default:
      return {update: false, reason: "unknown_aggregation_method", keep_existing: true};
  }
}
```

## 🎯 ROUTER-CRITICAL FIELD COLLECTION (ENHANCED)

### 🔧 ROUTER-CRITICAL CAPABILITY VERIFICATION

#### Speed Classification System
```javascript
const speedCategories = {
  "ultra-fast": "≥10000 tokens/sec (Groq, specialized hardware)",
  "very-fast": "1000-9999 tokens/sec (GPT-4o, Claude 3.5 Haiku)", 
  "fast": "100-999 tokens/sec (most modern models)",
  "medium": "10-99 tokens/sec (standard models)",
  "slow": "<10 tokens/sec (reasoning models, o1 series)"
};

// Speed verification with research terms:
// "[model] inference speed tokens per second artificialanalysis.ai benchmark 2025"
```

#### Vision Capability Detection
```javascript
// Vision indicators to search for:
const visionIndicators = [
  "multimodal model", "vision capabilities", "image processing",
  "/v1/images endpoint", "base64 image encoding", "supports image input"
];

// Research query: "[model] vision image multimodal capability API documentation 2025"
// Confidence threshold: ≥0.90 for vision updates
```

#### Function Calling Verification
```javascript
// Function calling evidence:
const functionCallingEvidence = [
  "function calling", "tool use", "API endpoints for tools",
  "structured output", "function definitions", "tool calling capability"
];

// Research query: "[model] function calling tool use API documentation 2025"
```

#### Reasoning Trace Detection
```javascript
// Reasoning trace models (o1, o3, QwQ series):
const reasoningModels = ["o1-", "o3-", "qwq-", "reasoning"];
// Most models: reasoning_trace = false (only specific reasoning models have this)
```

## ✅ DATABASE UPDATE (SAFE CONDITIONAL UPDATES)

```sql
-- Update model with verification results (preserves existing data)
UPDATE AIModel 
SET metadata = JSON_SET(metadata,
    -- Router task scores (8 aggregated) - only if calculated
    '$.taskScores', CASE WHEN @new_task_scores IS NOT NULL THEN @new_task_scores 
                         ELSE JSON_EXTRACT(metadata, '$.taskScores') END,
    
    -- Category scores (28 individual) - only if improved 
    '$.categoryScores', CASE WHEN @new_category_scores IS NOT NULL THEN @new_category_scores 
                             ELSE JSON_EXTRACT(metadata, '$.categoryScores') END,
    
    -- Router capabilities - only if verified
    '$.capabilities.speed', CASE WHEN @verified_speed IS NOT NULL THEN @verified_speed 
                                 ELSE JSON_EXTRACT(metadata, '$.capabilities.speed') END,
    '$.capabilities.function_calling', CASE WHEN @verified_function_calling IS NOT NULL THEN @verified_function_calling 
                                            ELSE JSON_EXTRACT(metadata, '$.capabilities.function_calling') END,
    '$.capabilities.web_search', CASE WHEN @verified_web_search IS NOT NULL THEN @verified_web_search 
                                      ELSE JSON_EXTRACT(metadata, '$.capabilities.web_search') END,
    '$.capabilities.reasoning_trace', CASE WHEN @verified_reasoning_trace IS NOT NULL THEN @verified_reasoning_trace 
                                           ELSE JSON_EXTRACT(metadata, '$.capabilities.reasoning_trace') END,
    '$.capabilities.vision', CASE WHEN @verified_vision IS NOT NULL THEN @verified_vision 
                                  ELSE JSON_EXTRACT(metadata, '$.capabilities.vision') END,
    '$.capabilities.contextWindow', CASE WHEN @verified_context_window IS NOT NULL THEN @verified_context_window 
                                         ELSE JSON_EXTRACT(metadata, '$.capabilities.contextWindow') END,
    '$.capabilities.maxOutputTokens', CASE WHEN @verified_max_output IS NOT NULL THEN @verified_max_output 
                                           ELSE JSON_EXTRACT(metadata, '$.capabilities.maxOutputTokens') END,
    
    -- Confidence scores (add alongside capabilities)
    '$.capabilities.speed_confidence', @speed_confidence,
    '$.capabilities.function_calling_confidence', @function_calling_confidence,
    '$.capabilities.vision_confidence', @vision_confidence,
    
    -- Validation metadata (always update)
    '$.validationStatus', 'completed',
    '$.lastValidated', NOW(),
    '$.methodology', 'V6.9.3',
    '$.validationDuration', TIMESTAMPDIFF(SECOND, STR_TO_DATE(JSON_EXTRACT(metadata, '$.validationStarted'), '%Y-%m-%d %H:%i:%s'), NOW()),
    '$.improvementsCount', COALESCE(JSON_EXTRACT(metadata, '$.improvementsCount'), 0) + @improvements_made,
    '$.lastImprovements', @improvement_summary
) 
WHERE canonicalName = 'SELECTED_MODEL_NAME';
```

## ✅ SUCCESS CRITERIA & QUALITY ASSURANCE

### 🎯 V6.9.3 Validation Standards
1. **Evidence-Based Only**: Real benchmarks (HumanEval, AIME, GPQA) over fabricated scores
2. **28→8 Category Mapping**: Complete individual collection before aggregation  
3. **Multi-Source Verification**: Minimum 2 sources OR 95% confidence
4. **Cost-Effective Research**: Exhaust free/low-cost sources before expensive options
5. **Additive Enhancement**: Never override existing correct data
6. **Router Optimization**: All critical router fields populated
7. **Provider Neutrality**: Performance-based scoring, not reputation-based
8. **Graceful Degradation**: Partial improvements acceptable

### 🔄 Execution Requirements
- **Single Model Focus**: Exactly one model per automation cycle
- **Complete Logging**: Step-by-step progress with evidence tracking
- **Dual Database Updates**: Both categoryScores (28) AND taskScores (8)
- **Confidence Tracking**: All improvements include confidence levels
- **Source Documentation**: Evidence sources for all new data

## 📊 BENCHMARK DATA SOURCES

### 🎯 High-Confidence Benchmarks (0.90-0.98)
- **AIME 2025**: Mathematical reasoning (o3-mini: 86.5%, Gemini 2.5 Pro: 85.8%)
- **LMArena ELO**: Chat quality rankings - USE RAW ELO (Gemini 2.5 Pro: 1473, GPT-4o: 1428)
  - **OFFICIAL SOURCE**: https://huggingface.co/spaces/lmarena-ai/chatbot-arena-leaderboard
  - **Updated**: 2025-07-06 (3.1M+ votes, 253 models)
  - **Critical**: Always verify current Arena rankings from official HF space
- **HumanEval**: Code generation pass@1 (GPT-4o: 89.2%, Claude 3.5: 89.0%)
- **GPQA Diamond**: Graduate science (Gemini 2.5 Pro: 84%, Human PhD: 69%)
- **SWE-bench Live**: Software engineering (OpenHands+Claude: 17.67%)
- **Open-LLM Leaderboard**: 4,576+ models across IFEval, BBH, MATH, GPQA, MUSR, MMLU-PRO

### 📁 Benchmark Files
- **Master**: `benchmark_data_complete/benchmark_master_2025-07-06.csv` (225+ models, 15+ metrics)
- **Arena**: `lmsys_arena_2025-07-05.csv` (208+ models with ELO ratings)
- **Leaderboard**: `open_llm_leaderboard_2025-07-06.csv` (4,576+ models)
- **SWE-bench**: `swe_bench_live_2025-07-06.csv` (12+ models)
- **AIME**: `aime_2025_questions_answers.csv` (14+ models)

### 🎯 Category Evidence Strength
**Strong** (0.90-0.98): coding, mathematical, reasoning, scientific, general_chat, question_answering
**Medium** (0.80-0.89): analysis, data_analysis, debugging, creative_writing
**Limited** (0.70-0.79): translation, summarization, medical, legal
**Proxy-Based** (0.45-0.69): tutorial, role_play, business_writing, philosophical

## 🎯 IMPLEMENTATION STRATEGY

### ⏱️ Time & Resource Management
- **120-minute timeout**: Allows thorough multi-source research
- **Evidence-rich first**: Prioritize categories with strong benchmark availability
- **Cost optimization**: Free → low-cost → moderate → expensive research tools
- **Local cache usage**: Check benchmark files before external API calls

### 📊 Category Processing Priority
1. **Tier 1** (Strong Evidence): coding, mathematical, reasoning, general_chat, scientific
2. **Tier 2** (Medium Evidence): question_answering, analysis, debugging, data_analysis
3. **Tier 3** (Limited Evidence): creative_writing, translation, medical, legal, technical_writing
4. **Tier 4** (Proxy Fallback): tutorial, role_play, business_writing, philosophical, brainstorming

### 🔄 Processing Workflow
1. **Load existing data** from database
2. **Process Tier 1 categories** with benchmark data
3. **Research Tier 2-3** with MCP tools (cost-effective priority)
4. **Apply proxy mapping** for Tier 4 categories
5. **Aggregate 28→8** using explicit formulas
6. **Update database** with confidence tracking

## 📊 OUTPUT FORMAT REQUIREMENTS

### Required JSON Structure (V6.9.3 Enhanced)
```json
{
  "model": "provider/model-name",
  "validation_metadata": {
    "methodology": "V6.9.3",
    "validation_date": "2025-07-07T17:xx:xx.000Z",
    "improvements_count": 8,
    "categories_updated": "15/28",
    "confidence_average": 0.87
  },
  "categoryScores": {
    "coding": {"score": 89.2, "confidence": 0.98, "sources": ["HumanEval", "LiveCodeBench"]},
    "debugging": {"score": 85.0, "confidence": 0.90, "sources": ["SWE-bench"]},
    "mathematical": {"score": 88.5, "confidence": 0.95, "sources": ["MATH", "GSM8K", "AIME"]},
    "reasoning": {"score": 88.0, "confidence": 0.95, "sources": ["AIME", "GPQA"]},
    "general_chat": {"score": 1372, "confidence": 0.95, "sources": ["Arena_ELO"], "note": "ELO_direct_not_percentage"},
    "creative_writing": {"score": 74, "confidence": 0.75, "sources": ["writing_tasks"]},
    "question_answering": {"score": 82.6, "confidence": 0.90, "sources": ["MMLU", "QA_tasks"]},
    "scientific": {"score": 84.0, "confidence": 0.85, "sources": ["GPQA_Diamond"]},
    "analysis": {"score": 81.0, "confidence": 0.95, "sources": ["GPQA_Diamond", "MMLU"]},
    "translation": {"score": 78.0, "confidence": 0.75, "sources": ["multilingual_tasks"]},
    "summarization": {"score": 81.0, "confidence": 0.80, "sources": ["text_processing"]},
    "data_analysis": {"score": 83.0, "confidence": 0.85, "sources": ["BRIGHT", "BEIR"]},
    "technical_writing": {"score": 85.0, "confidence": 0.85, "sources": ["code_documentation"]},
    "medical": {"score": 82.0, "confidence": 0.85, "sources": ["MedQA"]},
    "legal": {"score": 78.5, "confidence": 0.90, "sources": ["LegalBench"]},
    "historical": {"score": 80.0, "confidence": 0.80, "sources": ["HQA-Data"]},
    "current_events": {"score": 75.5, "confidence": 0.75, "sources": ["RealTime_QA"]},
    "business_writing": {"score": 78, "confidence": 0.45, "sources": ["fallback_from_cre"], "_is_derived": true},
    "tutorial": {"score": 76, "confidence": 0.45, "sources": ["fallback_from_cha"], "_is_derived": true},
    "role_play": {"score": 77.0, "confidence": 0.70, "sources": ["chat_ability"]},
    "personal_advice": {"score": 76.0, "confidence": 0.70, "sources": ["chat_ability"]},
    "brainstorming": {"score": 75, "confidence": 0.45, "sources": ["fallback_from_cre"], "_is_derived": true},
    "creative": {"score": 72, "confidence": 0.70, "sources": ["creative_tasks"]},
    "philosophical": {"score": 80.0, "confidence": 0.75, "sources": ["reasoning_tasks"]},
    "image_analysis": {"score": null, "confidence": 0.0, "sources": [], "reason": "text_only_model"},
    "multimodal": {"score": null, "confidence": 0.0, "sources": [], "reason": "text_only_model"},
    "image_generation": {"score": null, "confidence": 0.0, "sources": [], "reason": "not_image_gen_model"},
    "other": {"score": 79.0, "confidence": 0.75, "sources": ["general_capability"]}
  },
  "taskScores": {
    "cod": 87.1, "cre": 73.7, "rea": 85.5, "mat": 88.5, 
    "ana": 82.3, "lng": 79.5, "cha": 83.7, "vis": 0.0
  },
  "routerCapabilities": {
    "speed": "fast", "function_calling": true, "web_search": true,
    "reasoning_trace": false, "vision": false, "contextWindow": 200000, "maxOutputTokens": 8192
  }
}
```

## 📋 MANDATORY FINAL SUMMARY SECTIONS

### 🔍 REJECTED EVIDENCE REPORT
**REQUIRED**: Include a dedicated section documenting all evidence found but rejected:

```markdown
### 🚫 REJECTED EVIDENCE SUMMARY
**Evidence found below 0.85 confidence threshold:**

1. **Field Name**: found_value
   - **Confidence**: 0.xx (below 0.85 threshold)
   - **Sources**: X sources ([source1:0.xx, source2:0.xx])
   - **Rejection Reason**: insufficient_sources/low_confidence/conflicting_data
   - **Evidence Quality**: brief description of limitations

2. **Field Name**: found_value
   - **Confidence**: 0.xx (below 0.85 threshold)
   - **Sources**: X sources ([source1:0.xx, source2:0.xx])
   - **Rejection Reason**: insufficient_sources/low_confidence/conflicting_data
   - **Evidence Quality**: brief description of limitations

**Total Rejected Items**: X fields with evidence found but insufficient confidence
**Total Preserved Items**: Y fields with existing data maintained
**Research Quality**: Comprehensive/Partial - cost-effective strategy followed
```

## 🎯 FINAL VERIFICATION CHECKLIST (MANDATORY)

**Before finalizing any validation, complete this checklist:**

### 📊 Data Recency & Accuracy Verification
- [ ] **All database scores checked for age**: Report any data >30 days old
- [ ] **Fresh research performed**: At least one authoritative + one cost-effective source for major benchmarks  
- [ ] **Conflict resolution completed**: Any cached vs research discrepancies >5% properly resolved
- [ ] **Source timestamps documented**: Publication dates recorded for all evidence used
- [ ] **Alternative search attempts**: Multiple keywords and model name variations tried

### 🔍 Research Completeness Verification  
- [ ] **Exhaustive research protocol followed**: All 4 tiers attempted where appropriate
- [ ] **Multiple source types used**: Official docs, benchmarks, technical papers, evaluations
- [ ] **Evidence comparison logged**: Cached vs research data explicitly compared
- [ ] **Rejection reasoning documented**: All sub-threshold evidence properly logged with reasons
- [ ] **Decision justification clear**: Why each data point was accepted/rejected/preserved

### 📈 Quality Assurance Verification
- [ ] **Confidence thresholds met**: All updates ≥0.85 confidence unless explicitly noted
- [ ] **Source diversity achieved**: Minimum 2 independent sources for critical fields
- [ ] **Provider neutrality maintained**: Performance-based not reputation-based scoring
- [ ] **Router optimization complete**: All critical fields (speed, function_calling, vision, etc.) populated
- [ ] **28→8 aggregation correct**: Individual category scores properly mapped to task scores

### 🚨 Critical Issues Check
- [ ] **No fabricated data**: All scores traced to verifiable sources
- [ ] **No unauthorized overrides**: Existing correct data preserved unless evidence-backed improvement available
- [ ] **Outlier verification**: Any scores significantly different from peers properly researched
- [ ] **Methodology consistency**: V6.9.3 standards applied throughout
- [ ] **Audit logging complete**: All decisions logged to ValidationAuditLog table
- [ ] **Authoritative benchmark priority**: SWE-bench, HumanEval, etc. verified first
- [ ] **Contradiction detection**: No benchmark contradictions flagged
- [ ] **Documentation complete**: All research attempts, findings, and decisions logged

**ONLY proceed with database update if ALL checklist items are completed ✅**

---

## 📊 POST-VALIDATION AUDIT ANALYSIS

After validation, run these queries to identify patterns and ensure quality:

```sql
-- Check for potentially good single sources that were rejected
SELECT 
    model_canonical_name,
    field_name,
    confidence_score,
    rejection_reason,
    decision_notes,
    research_value as "Found Value",
    existing_value as "Kept Value"
FROM ValidationAuditLog 
WHERE decision_type = 'REJECTED' 
  AND rejection_reason = 'single_source'
  AND confidence_score >= 0.90
ORDER BY confidence_score DESC;

-- Find cases where authoritative sources were ignored
SELECT 
    val.model_canonical_name,
    val.field_name,
    src.benchmark_name,
    src.evidence_confidence,
    src.evidence_value,
    val.existing_value,
    val.rejection_reason,
    val.decision_notes as why_rejected
FROM ValidationAuditLog val
JOIN ValidationEvidenceSources src ON val.id = src.audit_log_id  
WHERE src.is_authoritative = TRUE 
  AND val.decision_type = 'REJECTED';

-- Audit debugging decisions for major models
SELECT 
    model_canonical_name,
    decision_type,
    final_value,
    confidence_score,
    rejection_reason,
    decision_notes
FROM ValidationAuditLog 
WHERE field_name = 'debugging' 
  AND model_canonical_name IN ('openai/o1', 'anthropic/claude-opus-4-0', 'anthropic/claude-4-opus')
ORDER BY decision_timestamp DESC;

-- Summary of all decisions by type
SELECT 
    decision_type,
    COUNT(*) as count,
    AVG(confidence_score) as avg_confidence,
    GROUP_CONCAT(DISTINCT rejection_reason) as rejection_reasons
FROM ValidationAuditLog 
GROUP BY decision_type;
```

**V6.9.3 Methodology**: Audit-enhanced validation with authoritative benchmark priority, comprehensive decision logging, benchmark contradiction detection, and full traceability. **Core Principle**: AUTHORITATIVE BENCHMARKS OVERRIDE GENERAL RESEARCH + COMPLETE AUDIT TRAIL for all validation decisions.