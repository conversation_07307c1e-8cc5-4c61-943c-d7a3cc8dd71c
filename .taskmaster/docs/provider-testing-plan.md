# Comprehensive Provider Testing Plan

## Overview
This document outlines a systematic testing plan for all AI providers and models in JustSimpleChat to ensure they are properly integrated and functioning correctly.

## Testing Infrastructure

### 1. Create Test Script Template
```bash
#!/bin/bash
# test-provider.sh - Generic provider testing script

PROVIDER=$1
MODEL=$2
API_ENDPOINT="http://localhost:3006/api/chat"

echo "🧪 Testing Provider: $PROVIDER, Model: $MODEL"
echo "================================================"

# Test 1: Simple Message
echo -e "\n📝 Test 1: Simple greeting"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d "{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}],\"manualModel\":\"$MODEL\"}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -o "${PROVIDER}-${MODEL}-test1.json" \
  -s

# Test 2: Code Generation
echo -e "\n📝 Test 2: Code generation"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d "{\"messages\":[{\"role\":\"user\",\"content\":\"Write a Python function to calculate factorial\"}],\"manualModel\":\"$MODEL\"}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -o "${PROVIDER}-${MODEL}-test2.json" \
  -s

# Test 3: Math/Reasoning
echo -e "\n📝 Test 3: Math problem"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d "{\"messages\":[{\"role\":\"user\",\"content\":\"What is 15 * 8 + 23?\"}],\"manualModel\":\"$MODEL\"}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -o "${PROVIDER}-${MODEL}-test3.json" \
  -s

# Test 4: Multi-turn Conversation
echo -e "\n📝 Test 4: Multi-turn conversation"
curl -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d "{\"messages\":[{\"role\":\"user\",\"content\":\"My name is Alice\"},{\"role\":\"assistant\",\"content\":\"Nice to meet you, Alice!\"},{\"role\":\"user\",\"content\":\"What's my name?\"}],\"manualModel\":\"$MODEL\"}" \
  -w "\nHTTP Status: %{http_code}\n" \
  -o "${PROVIDER}-${MODEL}-test4.json" \
  -s

echo -e "\n✅ Tests completed. Check output files for responses."
```

### 2. Provider Debug Mode
Enable in `.env.production`:
```
PROVIDER_DEBUG=true
```

## Provider-Specific Testing

### 1. OpenAI
**Models to Test:**
- gpt-4-turbo
- gpt-4o
- gpt-4o-mini
- gpt-3.5-turbo
- o1-preview
- o1-mini

**Special Tests:**
- Function calling
- JSON mode response format
- Vision (gpt-4o with image URLs)
- Reasoning trace (o1 models)

**Known Issues to Verify:**
- o1 models don't support system messages
- o1 models have different token limits
- Check reasoning content extraction for o1

### 2. Anthropic
**Models to Test:**
- claude-3-5-sonnet-20241022
- claude-3-5-haiku-20241022
- claude-3-opus-20240229
- claude-3-sonnet-20240229
- claude-3-haiku-20240307

**Special Tests:**
- System prompts handling
- Long context (200k tokens)
- Vision capabilities
- XML tag handling in responses

**Known Issues to Verify:**
- Rate limiting on Opus
- Haiku speed vs quality tradeoff

### 3. Google (Gemini)
**Models to Test:**
- gemini-2.0-flash-exp
- gemini-2.0-flash
- gemini-1.5-pro
- gemini-1.5-flash
- gemini-2.0-flash-thinking-exp

**Special Tests:**
- Multimodal (text + image)
- Thinking mode (thinking-exp models)
- Long context (1M+ tokens)
- Safety settings

**Known Issues to Verify:**
- Thinking models reasoning extraction
- Flash vs Pro performance differences
- Rate limits on experimental models

### 4. Meta (Llama via Groq/Together)
**Models to Test:**
- llama-3.3-70b
- llama-3.2-90b-vision
- llama-3.2-11b-vision
- llama-3.2-3b
- llama-3.2-1b
- llama-3.1-405b
- llama-3.1-70b
- llama-3.1-8b

**Special Tests:**
- Vision models with images
- Tool use capabilities
- Context window limits
- Speed tests (smaller models)

**Known Issues to Verify:**
- Provider availability (Groq vs Together)
- Vision model image handling
- 405B model availability

### 5. xAI (Grok)
**Models to Test:**
- grok-3
- grok-3-fast
- grok-3-mini ✅ (with reasoning)
- grok-3-mini-fast ✅ (with reasoning)
- grok-2-1212 ✅
- grok-2-vision-1212

**Special Tests:**
- Web search capabilities
- Reasoning content (mini models)
- Vision capabilities
- X/Twitter data access

**Known Issues to Verify:**
- ✅ Fixed: Model name transformation (grok-2 → grok-2-1212)
- ✅ Fixed: Reasoning content extraction
- Test vision model with images
- Test web search with different sources

### 6. DeepSeek
**Models to Test:**
- deepseek-chat
- deepseek-reasoner
- deepseek-coder

**Special Tests:**
- Code generation quality
- Reasoning capabilities
- Math problems
- Chinese language support

**Known Issues to Verify:**
- API endpoint stability
- Response format consistency
- Reasoning model output format

### 7. Mistral
**Models to Test:**
- mistral-large-2411
- mistral-large-latest
- mistral-small-latest
- codestral-2501
- codestral-latest
- mistral-medium

**Special Tests:**
- Multilingual capabilities
- Code generation (Codestral)
- European language focus
- Function calling

**Known Issues to Verify:**
- Model availability
- Codestral specialization
- Rate limiting

### 8. Perplexity
**Models to Test:**
- sonar
- sonar-pro
- sonar-reasoning

**Special Tests:**
- Web search integration
- Citation handling
- Real-time information
- Reasoning capabilities

**Known Issues to Verify:**
- Citation format in responses
- Search result quality
- API compatibility

### 9. Cohere
**Models to Test:**
- command-r-plus-08-2024
- command-r-plus
- command-r-08-2024
- command-r
- command-light

**Special Tests:**
- RAG capabilities
- Citation generation
- Multilingual support
- Command specialization

**Known Issues to Verify:**
- Response format differences
- Citation handling
- Tool use support

### 10. Qwen (Alibaba)
**Models to Test:**
- qwen-max
- qwen-plus
- qwen-turbo
- qwen-vl-max-latest
- qwen-vl-plus-latest

**Special Tests:**
- Chinese language
- Vision capabilities (VL models)
- Long context
- Math reasoning

**Known Issues to Verify:**
- API endpoint stability
- Language detection
- Vision model format

### 11. OpenRouter
**Models to Test:**
- neversleep/llama-3.1-lumimaid-70b
- anthracite-org/magnum-v4-72b
- meta-llama/llama-3.2-90b-vision-instruct
- nvidia/llama-3.1-nemotron-70b-instruct

**Special Tests:**
- Model routing
- Specialized model capabilities
- Rate limiting per model
- Cost tracking

**Known Issues to Verify:**
- Model availability
- Routing decisions
- Cost calculations

## Test Cases for All Providers

### Basic Functionality
1. **Hello World**
   - Input: "Hello"
   - Expected: Greeting response
   - Verify: Response received, no errors

2. **Simple Math**
   - Input: "What is 25 + 17?"
   - Expected: "42"
   - Verify: Correct calculation

3. **Code Generation**
   - Input: "Write a Python hello world"
   - Expected: Valid Python code
   - Verify: Code syntax, explanation

4. **Multi-turn Context**
   - Input: Series of related messages
   - Expected: Context maintained
   - Verify: Remembers previous messages

### Advanced Features
1. **Long Input**
   - Input: 1000+ word prompt
   - Expected: Coherent response
   - Verify: No truncation, handles well

2. **Special Characters**
   - Input: Unicode, emojis, symbols
   - Expected: Proper handling
   - Verify: No encoding errors

3. **JSON Response**
   - Input: Request JSON format
   - Expected: Valid JSON
   - Verify: Parseable JSON

4. **Language Detection**
   - Input: Non-English text
   - Expected: Appropriate language response
   - Verify: Language handling

### Error Handling
1. **Invalid Model**
   - Input: Non-existent model name
   - Expected: Error message
   - Verify: Graceful failure

2. **Rate Limiting**
   - Input: Rapid requests
   - Expected: Rate limit error
   - Verify: Proper error handling

3. **Token Limits**
   - Input: Exceed context window
   - Expected: Truncation or error
   - Verify: Graceful handling

4. **Network Timeout**
   - Input: Slow response scenario
   - Expected: Timeout handling
   - Verify: No hanging requests

## Testing Checklist

### Pre-Testing
- [ ] Verify API keys are set in `.env.production`
- [ ] Enable `PROVIDER_DEBUG=true`
- [ ] Create test output directory
- [ ] Prepare test data files
- [ ] Check rate limits for each provider

### During Testing
- [ ] Run basic test suite for each model
- [ ] Document response times
- [ ] Check token usage and costs
- [ ] Verify streaming functionality
- [ ] Test error scenarios
- [ ] Monitor PM2 logs for errors

### Post-Testing
- [ ] Compile response time metrics
- [ ] Document any failures
- [ ] Update model availability status
- [ ] Calculate cost per request
- [ ] Create provider comparison matrix
- [ ] Update documentation

## Automated Testing Script

### Full Provider Test Suite
```bash
#!/bin/bash
# test-all-providers.sh

PROVIDERS=(
  "openai:gpt-4o"
  "anthropic:claude-3-5-sonnet-20241022"
  "google:gemini-2.0-flash"
  "xai:grok-3"
  "deepseek:deepseek-chat"
  "mistral:mistral-large-latest"
  "perplexity:sonar-pro"
  "cohere:command-r-plus"
  "qwen:qwen-max"
)

echo "🚀 Starting comprehensive provider testing"
echo "========================================"

for provider_model in "${PROVIDERS[@]}"; do
  IFS=':' read -r provider model <<< "$provider_model"
  echo -e "\n🧪 Testing $provider with model $model"
  ./test-provider.sh "$provider" "$model"
  sleep 2  # Rate limit protection
done

echo -e "\n✅ All tests completed!"
```

## Expected Outcomes

### Success Criteria
1. All models respond within 30 seconds
2. No 5xx errors from any provider
3. Streaming works for all models
4. Token counting is accurate
5. Cost calculation is correct
6. Error handling is graceful

### Performance Benchmarks
- **Fast**: < 1s first token
- **Medium**: 1-3s first token
- **Slow**: > 3s first token

### Quality Metrics
- Code generation accuracy
- Math problem solving
- Context retention
- Language understanding
- Instruction following

## Reporting Template

### Provider Test Report
```markdown
## Provider: [Name]
Date: [Test Date]
Tester: [Name]

### Models Tested
- [x] Model 1 - ✅ Passed / ❌ Failed
- [x] Model 2 - ✅ Passed / ❌ Failed

### Test Results
| Test Case | Result | Response Time | Notes |
|-----------|--------|---------------|-------|
| Hello World | ✅ | 0.8s | Good |
| Code Gen | ✅ | 1.2s | Excellent |
| Math | ❌ | - | Timeout |

### Issues Found
1. Issue description
2. Issue description

### Recommendations
- Action item 1
- Action item 2
```

## Next Steps

1. **Create Testing Infrastructure**
   - Set up automated test scripts
   - Create test data repository
   - Build response validation tools

2. **Execute Testing Plan**
   - Test each provider systematically
   - Document all findings
   - Create issue tickets for failures

3. **Continuous Monitoring**
   - Set up provider health checks
   - Monitor response times
   - Track availability metrics
   - Cost optimization analysis

4. **Documentation Updates**
   - Update provider capabilities
   - Document known limitations
   - Create troubleshooting guides
   - Maintain compatibility matrix