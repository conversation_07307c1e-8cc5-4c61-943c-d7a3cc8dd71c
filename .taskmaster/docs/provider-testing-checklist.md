# Provider Testing Checklist

## Pre-Testing Setup
- [ ] All API keys set in `.env.production`
- [ ] `PROVIDER_DEBUG=true` enabled
- [ ] PM2 instance running (`pm2 start simplechat`)
- [ ] Test scripts executable (`chmod +x test-*.sh`)
- [ ] Test results directory created

## Provider Testing Status

### ✅ xAI (Grok)
- [x] API Key configured
- [x] Basic connectivity test
- [x] Model availability verified
  - [x] grok-3
  - [x] grok-3-fast
  - [x] grok-3-mini (with reasoning)
  - [x] grok-3-mini-fast (with reasoning)
  - [x] grok-2-1212
  - [x] grok-2-vision-1212
- [x] Streaming functionality
- [x] Reasoning content extraction (grok-3-mini models)
- [x] Error handling
- **Known Issues:**
  - Model names must be versioned (grok-2 → grok-2-1212)
  - Reasoning content sent in separate chunks during streaming

### ⏳ OpenAI
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] gpt-4o
  - [ ] gpt-4o-mini
  - [ ] gpt-4-turbo
  - [ ] gpt-3.5-turbo
  - [ ] o1-preview
  - [ ] o1-mini
- [ ] Streaming functionality
- [ ] JSON mode
- [ ] Function calling
- [ ] Vision (gpt-4o)
- [ ] Reasoning trace (o1 models)

### ⏳ Anthropic
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] claude-3-5-sonnet-20241022
  - [ ] claude-3-5-haiku-20241022
  - [ ] claude-3-opus-20240229
  - [ ] claude-3-sonnet-20240229
  - [ ] claude-3-haiku-20240307
- [ ] Streaming functionality
- [ ] System prompts
- [ ] Long context (200k)
- [ ] Vision capabilities

### ⏳ Google (Gemini)
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] gemini-2.0-flash-exp
  - [ ] gemini-2.0-flash
  - [ ] gemini-1.5-pro
  - [ ] gemini-1.5-flash
  - [ ] gemini-2.0-flash-thinking-exp
- [ ] Streaming functionality
- [ ] Thinking mode extraction
- [ ] Vision capabilities
- [ ] Safety settings

### ⏳ DeepSeek
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] deepseek-chat
  - [ ] deepseek-reasoner
  - [ ] deepseek-coder
- [ ] Streaming functionality
- [ ] Code generation quality
- [ ] Reasoning capabilities

### ⏳ Mistral
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] mistral-large-2411
  - [ ] mistral-large-latest
  - [ ] mistral-small-latest
  - [ ] codestral-2501
  - [ ] codestral-latest
- [ ] Streaming functionality
- [ ] Multilingual support
- [ ] Code specialization (Codestral)

### ⏳ Perplexity
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] sonar
  - [ ] sonar-pro
  - [ ] sonar-reasoning
- [ ] Streaming functionality
- [ ] Web search integration
- [ ] Citation handling

### ⏳ Cohere
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] command-r-plus-08-2024
  - [ ] command-r-plus
  - [ ] command-r-08-2024
  - [ ] command-r
- [ ] Streaming functionality
- [ ] RAG capabilities
- [ ] Citation generation

### ⏳ Qwen (Alibaba)
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model availability
  - [ ] qwen-max
  - [ ] qwen-plus
  - [ ] qwen-turbo
  - [ ] qwen-vl-max-latest
  - [ ] qwen-vl-plus-latest
- [ ] Streaming functionality
- [ ] Chinese language support
- [ ] Vision capabilities (VL models)

### ⏳ Meta (Llama via providers)
- [ ] Provider routing verified
- [ ] Model availability
  - [ ] llama-3.3-70b
  - [ ] llama-3.2-90b-vision
  - [ ] llama-3.2-11b-vision
  - [ ] llama-3.1-405b
  - [ ] llama-3.1-70b
  - [ ] llama-3.1-8b
- [ ] Streaming functionality
- [ ] Vision models
- [ ] Tool use capabilities

### ⏳ OpenRouter
- [ ] API Key configured
- [ ] Basic connectivity test
- [ ] Model routing
- [ ] Specialized models
- [ ] Cost tracking

## Test Scripts

### Individual Provider Test
```bash
./test-provider.sh <provider> <model>
# Example: ./test-provider.sh xai grok-3-mini
```

### Full Provider Suite
```bash
./test-all-providers.sh
```

### Check Results
```bash
# View test results
ls -la test-results/

# Check for errors
grep -r "error" test-results/

# Check streaming
grep -r "type.*content" test-results/
```

## Common Issues & Solutions

### API Key Issues
- **Error**: "Invalid API key"
- **Solution**: Check `.env.production` for correct key format

### Rate Limiting
- **Error**: "429 Too Many Requests"
- **Solution**: Add delays between tests, check provider limits

### Model Not Found
- **Error**: "Unknown model"
- **Solution**: Verify model name in registry, check API docs

### Streaming Failures
- **Error**: "Failed to parse SSE chunk"
- **Solution**: Check provider streaming format, update parser

## Post-Testing Actions

1. **Update Model Registry**
   - Remove deprecated models
   - Add new models found during testing
   - Update costs and capabilities

2. **Fix Provider Issues**
   - Update error handling
   - Fix streaming parsers
   - Add missing features

3. **Update Documentation**
   - Provider capabilities
   - Known limitations
   - Troubleshooting guides

4. **Performance Optimization**
   - Identify slow providers
   - Optimize retry logic
   - Update fallback order

## Metrics to Track

- **Response Time**: First token latency
- **Throughput**: Tokens per second
- **Reliability**: Success rate
- **Cost**: Per 1k tokens
- **Quality**: Output coherence
- **Features**: Special capabilities