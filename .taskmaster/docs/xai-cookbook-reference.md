# xAI Cookbook Reference

## Repository
https://github.com/xai-org/xai-cookbook

## Overview
The xAI Cookbook is a collection of pragmatic, real-world examples for using xAI's Grok APIs. It provides Python notebooks demonstrating various use cases from basic to advanced.

## Available Examples

### 1. Function Calling 101
- **Description**: An introduction to function calling for beginners
- **Path**: `/examples/function_calling_101/guide.ipynb`
- **Tags**: function-calling
- **Author**: <PERSON><PERSON>

### 2. Hyper Personalized Marketing
- **Description**: Use Grok to generate hyper personalized marketing materials complete with custom images and copy
- **Path**: `/examples/hyper_personalized_marketing/guide.ipynb`
- **Tags**: marketing, multi-modal, image-generation
- **Author**: Omar <PERSON>ab

### 3. Building a Unified Chat Experience
- **Description**: Learn how to build a comprehensive chat experience with Grok inclusive of multi-turn conversations, streaming, function calling, and structured outputs
- **Path**: `/examples/multi_turn_conversation/guide.ipynb`
- **Tags**: multi-turn-conversation, chatbot, streaming, function-calling, structured-outputs, image-understanding
- **Author**: Omar <PERSON>

### 4. Multimodal Examples
#### Object Detection
- **Description**: Combine Grok's natural language processing with its image understanding to perform flexible object detection
- **Path**: `/examples/multimodal/object_detection/guide.ipynb`
- **Tags**: vision, object-detection, multi-modal
- **Author**: Omar Diab

#### Structured Data Extraction from Fashion Images
- **Description**: Leverage Grok's image understanding to extract structured/json data from fashion images
- **Path**: `/examples/multimodal/structured_data_extraction/guide.ipynb`
- **Tags**: vision, structured-outputs, multi-modal, e-commerce
- **Author**: Omar Diab

### 5. Real Time Sentiment Analysis with X
- **Description**: Combine X's real-time data with Grok's reasoning to perform real-time sentiment analysis
- **Path**: `/examples/sentiment_analysis_on_x/guide.ipynb`
- **Tags**: sentiment-analysis, real-time, reasoning
- **Author**: Omar Diab

## Key Insights for Our Implementation

### API Structure
- xAI uses OpenAI-compatible API format
- Base URL: `https://api.x.ai/v1`
- Authentication: Bearer token with API key

### Available Models (from our testing)
- `grok-3` - Latest flagship model
- `grok-3-fast` - Faster variant
- `grok-3-mini` - Lightweight with reasoning
- `grok-3-mini-fast` - Fastest variant with reasoning
- `grok-2-1212` - Previous generation
- `grok-2-vision-1212` - Multimodal support
- `grok-2-image-1212` - Image generation

### Reasoning Support (Thinking/Output Separation)
Only available on mini variants:
- `grok-3-mini`
- `grok-3-mini-fast`

**Key Features:**
- **Reasoning Trace Access**: The model's thinking process is available via the `reasoning_content` field
- **Reasoning Effort Control**: Use `reasoning_effort` parameter: "low" | "high"
- **Separated Output**: Final answer is in `message.content`, thinking is in `reasoning_content`

**Example Usage:**
```python
response = client.chat.completions.create(
    model="grok-3-mini",
    reasoning_effort="high",
    messages=[{"role": "user", "content": "Complex problem here"}],
)

# Access reasoning trace
reasoning = getattr(response.choices[0].message, "reasoning_content", None)
if reasoning:
    print("Thinking:", reasoning)

# Access final answer
print("Answer:", response.choices[0].message.content)
```

### Vision Support
Use `grok-2-vision-1212` for image analysis

### Image Generation
Use `grok-2-image-1212` for generating images from text prompts

### Web Search
Grok models support live web search via `search_parameters`:
```json
{
  "search_parameters": {
    "mode": "auto", // "off", "auto", "on"
    "return_citations": true,
    "sources": [{"type": "web"}]
  }
}
```

## Implementation Notes

### Python SDK
```python
from openai import OpenAI

client = OpenAI(
    api_key="your-xai-api-key",
    base_url="https://api.x.ai/v1",
)

response = client.chat.completions.create(
    model="grok-3",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello!"}
    ],
)
```

### TypeScript/JavaScript
```typescript
const response = await fetch('https://api.x.ai/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
  },
  body: JSON.stringify({
    model: 'grok-3',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: 'Hello!' }
    ],
  }),
});
```

## Security Notes
- Never hardcode API keys
- Use environment variables
- The cookbook includes Gitleaks pre-commit hooks

## Contributing
- Follow the CONTRIBUTING.md guidelines
- Test notebooks end-to-end
- Use Git LFS for large files