# ULTIMATE SUBAGENT PROMPT V6.9 - ROUTER-OPTIMIZED VALIDATION

**Version**: 6.9.0 (Router-Critical Implementation)  
**Date**: July 7, 2025  
**Purpose**: Complete model validation with ALL router-critical fields and enhanced data collection  
**Critical**: Fixes V6.8 missing router fields and data quality issues  
**Update**: Comprehensive field collection based on router analysis - includes speed, function calling, web search, reasoning trace, and quality fixes

## 🚨 CRITICAL V6.9 METHODOLOGY REQUIREMENTS

### PROVIDER BIAS ELIMINATION
- **NEVER** assign scores based on provider reputation
- **ALWAYS** use real benchmark data and API testing
- **NO** systematic clustering by provider (V6.7 critical flaw)
- **EVIDENCE-BASED** scoring only - no fabricated scores

### ROUTER-CRITICAL FIELD COLLECTION (NEW V6.9)
- **MANDATORY**: Collect ALL router-critical fields for optimal routing performance
- **SPEED CATEGORIZATION**: Essential for 10% of router score + latency penalties
- **FUNCTION CALLING**: Critical for tool use detection (20% contextual bonus)
- **WEB SEARCH**: Required for current events filtering
- **REASONING TRACE**: Essential for o1/o3/QwQ model identification
- **DATA QUALITY**: Fix impossible scores >10, normalize all scales to 0-10
- **SPECIFIC ATTRIBUTES**: Programming languages, frameworks for contextual scoring

### 🗄️ DATABASE INTEGRATION & MODEL SELECTION (NEW V6.9)

#### 🎯 MODEL SELECTION QUERY
**CRITICAL**: Use this exact MySQL query to select the next model needing validation:

```sql
-- Connect to database
mysql -h 127.0.0.1 -u root -pDmggg13031988*** justsimplechat_production

-- Select next model for validation (priority order)
SELECT 
    canonicalName,
    displayName,
    providerId,
    JSON_EXTRACT(metadata, '$.lastValidated') as lastValidated,
    JSON_EXTRACT(metadata, '$.methodology') as methodology,
    JSON_EXTRACT(metadata, '$.taskScores') as currentTaskScores,
    JSON_EXTRACT(metadata, '$.capabilities.speed') as currentSpeed,
    JSON_EXTRACT(metadata, '$.capabilities.function_calling') as currentFunctionCalling
FROM AIModel 
WHERE isEnabled = true
  AND (
    -- Priority 1: Never validated
    JSON_EXTRACT(metadata, '$.lastValidated') IS NULL OR
    -- Priority 2: Old methodology (V6.8 or older)
    JSON_EXTRACT(metadata, '$.methodology') != 'V6.9' OR
    -- Priority 3: Missing critical router fields
    JSON_EXTRACT(metadata, '$.capabilities.speed') IS NULL OR
    JSON_EXTRACT(metadata, '$.capabilities.function_calling') IS NULL OR
    -- Priority 4: Data quality issues (impossible scores)
    JSON_EXTRACT(metadata, '$.taskScores.cod') > 10 OR
    JSON_EXTRACT(metadata, '$.taskScores.rea') > 10 OR
    JSON_EXTRACT(metadata, '$.taskScores.mat') > 10 OR
    -- Priority 5: Older than 30 days
    (JSON_EXTRACT(metadata, '$.lastValidated') IS NOT NULL AND 
     DATEDIFF(NOW(), STR_TO_DATE(JSON_EXTRACT(metadata, '$.lastValidated'), '%Y-%m-%dT%H:%i:%s')) > 30)
  )
ORDER BY 
    -- Prioritize models with no validation first
    CASE WHEN JSON_EXTRACT(metadata, '$.lastValidated') IS NULL THEN 1 ELSE 2 END,
    -- Then by oldest validation date
    JSON_EXTRACT(metadata, '$.lastValidated') ASC,
    -- Then alphabetically for consistency
    canonicalName ASC
LIMIT 1;
```

#### 🔒 DUPLICATE PREVENTION - MARK AS IN-PROGRESS
**IMMEDIATELY** after selecting a model, mark it as being processed:

```sql
-- Mark model as in-progress to prevent duplicate processing
UPDATE AIModel 
SET metadata = JSON_SET(metadata, 
    '$.validationStatus', 'in-progress',
    '$.validationStarted', NOW(),
    '$.validationPID', CONNECTION_ID()
) 
WHERE canonicalName = 'SELECTED_MODEL_NAME';
```

#### ✅ COMPLETION - MARK AS VALIDATED
**AFTER** completing validation, update with results:

```sql
-- Update model with complete validation results
UPDATE AIModel 
SET metadata = JSON_SET(metadata, 
    -- Core router scores (8 keys)
    '$.taskScores', JSON_OBJECT(
        'cod', ?, 'cre', ?, 'rea', ?, 'mat', ?, 
        'ana', ?, 'lng', ?, 'cha', ?, 'vis', ?
    ),
    
    -- Enhanced capabilities (NEW V6.9)
    '$.capabilities.speed', ?,                      -- ultra-fast|very-fast|fast|medium|slow
    '$.capabilities.speed_confidence', ?,           -- research confidence
    '$.capabilities.function_calling', ?,           -- true/false
    '$.capabilities.function_calling_confidence', ?, -- research confidence
    '$.capabilities.web_search', ?,                 -- true/false  
    '$.capabilities.web_search_confidence', ?,      -- research confidence
    '$.capabilities.reasoning_trace', ?,            -- true/false (o1/o3 style)
    '$.capabilities.reasoning_confidence', ?,       -- research confidence
    '$.capabilities.contextWindow', ?,              -- standardized context window
    '$.capabilities.maxOutputTokens', ?,            -- maximum output tokens
    '$.capabilities.context_confidence', ?,         -- specification confidence
    '$.capabilities.vision', ?,                     -- enhanced detection
    '$.capabilities.vision_confidence', ?,          -- research confidence
    
    -- Specific attributes (NEW V6.9)
    '$.specific_attributes.languages', ?,           -- programming languages array
    '$.specific_attributes.frameworks', ?,          -- frameworks array
    '$.specific_attributes.language_confidence', ?, -- research confidence
    
    -- Category scores (all 28 categories)
    '$.categoryScores', ?,                          -- complete JSON object
    
    -- Validation metadata
    '$.validationStatus', 'completed',
    '$.lastValidated', NOW(),
    '$.methodology', 'V6.9',
    '$.validationDuration', TIMESTAMPDIFF(SECOND, 
        STR_TO_DATE(JSON_EXTRACT(metadata, '$.validationStarted'), '%Y-%m-%d %H:%i:%s'), 
        NOW()
    ),
    '$.validationSource', 'automated_subagent_v6.9'
) 
WHERE canonicalName = 'SELECTED_MODEL_NAME';
```

#### 🚨 ERROR HANDLING
**IF validation fails or times out**:

```sql
-- Mark as failed for manual review
UPDATE AIModel 
SET metadata = JSON_SET(metadata, 
    '$.validationStatus', 'failed',
    '$.validationError', 'TIMEOUT_OR_ERROR_REASON',
    '$.validationFailed', NOW()
) 
WHERE canonicalName = 'SELECTED_MODEL_NAME';
```

### 🚨 CRITICAL DATA INTEGRITY RULES (V6.9)
- **NO FALSE DATA**: NEVER provide unverified information - use `null` instead
- **VERIFIED SOURCES ONLY**: All capabilities must be verified through:
  1. Official provider documentation (API docs, model cards)
  2. MCP research with real-time data (Perplexity 2025, Context7 latest docs)
  3. Actual benchmark results from recognized sources
- **REAL-TIME REQUIREMENTS**: All data must be current (2025) - use MCP tools for latest info
- **CONFIDENCE TRACKING**: Lower confidence (0.3-0.5) for unverified or uncertain data
- **EVIDENCE MANDATORY**: Every capability and score must have documented source
- **ERROR HANDLING**: If unsure, explicitly state uncertainty rather than guess
- **DATABASE INTEGRATION**: ALWAYS select model via SQL query and mark progress to prevent duplicates

### REAL BENCHMARK INTEGRATION - COMPREHENSIVE DATASET (V6.8.1)

#### 🎯 PRIMARY DATASETS (High Confidence: 0.90-0.98)
- **AIME 2025**: 30 mathematical reasoning problems (questions + answers in CSV)
  - Source: `aime_2025_questions_answers.csv` + Vals AI results
  - Top: o3-mini (86.5%), Gemini 2.5 Pro Exp (85.8%), o3 (85.3%)
  - **CRITICAL**: Specify variant - Standard (75.5%) vs Extended-Compute (90.0%) for Claude Opus 4
  - **Default**: Use standard variant unless explicitly extended-compute is available
- **LMArena ELO**: 208 models with chat quality rankings (USE DIRECTLY - NO PERCENTAGE CONVERSION)
  - Source: `lmsys_arena_2025-07-05.csv` 
  - Leader: Gemini 2.5 Pro (1473), ChatGPT-4o latest (1428)
  - **CRITICAL**: ELO ratings are complete metrics - store raw ELO, not converted percentages
- **Open-LLM Leaderboard**: 4,576 models with comprehensive metrics
  - Source: `open_llm_leaderboard_2025-07-06.csv`
  - Metrics: IFEval, BBH, MATH Lvl 5, GPQA, MUSR, MMLU-PRO
- **GPQA Diamond**: Graduate-level science questions
  - Source: Research compilation + Vals AI
  - Top: Gemini 2.5 Pro (84%), iAsk Pro (78.3%), Human PhD (69%)
- **HumanEval**: Code generation pass@1 accuracy
  - Source: Research compilation
  - Top: GPT-4o (89.2%), Claude 3.5 Sonnet (89.0%), o1 (88.0%)
- **SWE-bench Live**: Software engineering benchmarks
  - Source: `swe_bench_live_2025-07-06.csv`
  - Top: OpenHands + Claude 3.7 (17.67% resolved)

#### 📊 MASTER BENCHMARK FILE
- **Location**: `benchmark_data_complete/benchmark_master_2025-07-06.csv`
- **Coverage**: 225 unique models with 15 metrics
- **Quality**: Real benchmark scores with confidence tracking
- **Precision**: Decimal accuracy preserved (1-3 decimal places)

#### 🔍 BENCHMARK AVAILABILITY MATRIX (Updated July 2025)

**Categories with Strong Evidence (Direct Benchmarks)**:
```json
{
  "coding": {"primary": "HumanEval/SWE-bench", "confidence": 0.95, "models": 225},
  "debugging": {"primary": "SWE-bench Live", "confidence": 0.92, "models": 12},
  "mathematical": {"primary": "AIME 2025/MATH/GSM8K", "confidence": 0.98, "models": 14},
  "reasoning": {"primary": "AIME/GPQA/BBH", "confidence": 0.95, "models": 225},
  "scientific": {"primary": "GPQA Diamond", "confidence": 0.95, "models": 7},
  "question_answering": {"primary": "MMLU/MMLU-PRO", "confidence": 0.90, "models": 225},
  "analysis": {"primary": "Open-LLM metrics", "confidence": 0.85, "models": 4576},
  "general_chat": {"primary": "LMArena ELO", "confidence": 0.98, "models": 208},
  "data_analysis": {"primary": "BBH/MUSR", "confidence": 0.80, "models": 225}
}
```

### 28-CATEGORY HYBRID ASSESSMENT (Updated)
- **Evidence-based categories**: ~20 categories with direct benchmarks (↑ from 12)
- **Derived categories**: ~8 categories use fallback mapping (↓ from 16)
- **Null handling**: Return `null` for insufficient evidence
- **Transparency**: Always mark derived vs. direct benchmark scores
- **Confidence scoring**: Lower confidence for derived scores (0.45-0.60)

## 📋 VALIDATION METHODOLOGY

### STEP 1: BENCHMARK DATA LOOKUP
**FIRST: Check our comprehensive benchmark database before external research**:

#### Primary Data Sources (High Priority)
```python
# Load master benchmark file
master_df = pd.read_csv("benchmark_data_complete/benchmark_master_2025-07-06.csv")

# Search for model (fuzzy matching)
model_matches = master_df[master_df['model'].str.contains(MODEL_NAME, case=False, na=False)]

# Available metrics with confidence levels:
# - arena_elo (208 models, confidence: 0.98)
# - aime_aime_2025_accuracy (13 models, confidence: 0.98) 
# - gpqa_gpqa_diamond_accuracy (7 models, confidence: 0.95)
# - humaneval_humaneval_pass1 (8 models, confidence: 0.95)
# - math_math_accuracy, math_gsm8k_accuracy (8 models, confidence: 0.90)
# - mmlu_mmlu_accuracy (8 models, confidence: 0.90)
```

#### Specialized Dataset Lookups
```python
# AIME 2025 questions for testing
aime_questions = pd.read_csv("benchmark_data_complete/aime_2025_questions_answers.csv")

# Open-LLM Leaderboard (comprehensive)  
openllm_df = pd.read_csv("benchmark_data_complete/open_llm_leaderboard_2025-07-06.csv")

# SWE-bench Live results
swe_df = pd.read_csv("benchmark_data_complete/swe_bench_live_2025-07-06.csv")
```

#### ONLY IF NOT FOUND: MCP Research Backup
**Use MCP tools only if model not found in our datasets**:

```
mcp__perplexity-ask__search("MODEL_NAME benchmark scores 2025 AIME HumanEval GPQA")
mcp__firecrawl__firecrawl_search({
  "query": "MODEL_NAME benchmark leaderboard results 2025",
  "limit": 2,  // CRITICAL: Limit to 2 results to prevent context overload
  "scrapeOptions": {"formats": ["markdown"], "onlyMainContent": true}
})
```

### STEP 2: 28-CATEGORY ASSESSMENT WITH BENCHMARK REALITY

🚨 **CRITICAL REQUIREMENT: ALL 28 CATEGORIES MUST BE MAPPED**
- **NEVER leave categories unmapped** - use fallback methodology for missing benchmarks
- **ALWAYS provide scores for all 28 categories** - null values only for missing capabilities (vision, etc.)
- **MUST complete full category mapping** before proceeding to router scores

#### BENCHMARK AVAILABILITY MATRIX (July 2025)

**Categories with Strong Evidence (Direct Benchmarks)**:
```json
{
  "coding": {"primary": "HumanEval/MBPP/CoIR", "state": "public", "confidence": 0.95},
  "debugging": {"primary": "SWE-bench", "state": "public", "confidence": 0.90},
  "mathematical": {"primary": "MATH/GSM8K/AIME", "state": "public", "confidence": 0.95},
  "reasoning": {"primary": "ARC/HellaSwag/GPQA/BRIGHT", "state": "public", "confidence": 0.92},
  "scientific": {"primary": "GPQA-Diamond/SciQ", "state": "public", "confidence": 0.85},
  "translation": {"primary": "WMT-22/FLORES-200", "state": "public", "confidence": 0.90},
  "summarization": {"primary": "XSum/CNN-DailyMail", "state": "public", "confidence": 0.85},
  "question_answering": {"primary": "MMLU", "state": "public", "confidence": 0.90},
  "image_analysis": {"primary": "VQAv2/WebQA", "state": "public", "confidence": 0.87},
  "data_analysis": {"primary": "BRIGHT/CoIR/BEIR", "state": "public", "confidence": 0.85},
  "medical": {"primary": "MedQA leaderboard/MMLU-medicine", "state": "public", "confidence": 0.85},
  "legal": {"primary": "LegalBench (162 tasks)", "state": "public", "confidence": 0.90},
  "analysis": {"primary": "BRIGHT/BEIR/IFIR", "state": "public", "confidence": 0.85},
  "web_search": {"primary": "WebQA (Hit@1)/IFIR/BEIR", "state": "public", "confidence": 0.80},
  "current_events": {"primary": "RealTime QA (timestamped)/NewsQA", "state": "public", "confidence": 0.75},
  "historical": {"primary": "HQA-Data", "state": "public", "confidence": 0.80},
  "personal_advice": {"primary": "EmpatheticDialogues/Empathy-Stories", "state": "public", "confidence": 0.75},
  "technical_writing": {"primary": "JSONSchemaBench", "state": "public", "confidence": 0.80},
  "business_writing": {"primary": "DocILE (business-doc extraction)/BestWriting", "state": "limited", "confidence": 0.70},
  "image_generation": {"primary": "DrawBench/LongBench-T2I/GenAI-Bench", "state": "public", "confidence": 0.85}
}
```

**Categories Requiring Fallback (Limited or No Benchmarks)**:
```json
{
  "creative_writing": {"fallback": "general_chat", "state": "limited", "confidence": 0.60},
  "philosophical": {"fallback": "reasoning", "state": "none", "confidence": 0.45},
  "tutorial": {"fallback": "general_chat", "state": "none", "confidence": 0.50},
  "role_play": {"fallback": "general_chat", "state": "none", "confidence": 0.45},
  "brainstorming": {"fallback": "creative_writing", "state": "prototype", "confidence": 0.55},
  "creative": {"fallback": "creative_writing", "state": "limited", "confidence": 0.60},
  "multimodal": {"fallback": "image_analysis", "state": "limited", "confidence": 0.70},
  "academic_writing": {"fallback": "analysis", "state": "none", "confidence": 0.45},
  "other": {"fallback": "general_chat", "state": "none", "confidence": 0.30}
}
```

🚨 **MANDATORY FALLBACK METHODOLOGY**:
For categories without direct benchmarks, use the following mapping logic:
1. **creative_writing/brainstorming/creative** → Use general_chat as proxy
2. **philosophical/historical/academic_writing** → Use reasoning as proxy  
3. **tutorial/role_play/personal_advice** → Use general_chat as proxy
4. **multimodal** → Use image_analysis as proxy (if vision capable)
5. **other** → Use general_chat as baseline proxy

#### 🔧 CODING CATEGORIES
- **coding**: HumanEval, MBPP, CodeXGLUE scores (HIGH CONFIDENCE)
- **debugging**: SWE-bench, error correction tasks (HIGH CONFIDENCE)

#### ✍️ WRITING CATEGORIES  
- **creative_writing**: WritingPrompts, creative evaluation
- **technical_writing**: API docs, technical communication
- **business_writing**: Professional communication tasks

#### 🧠 REASONING CATEGORIES
- **reasoning**: ARC-Challenge, HellaSwag, logical reasoning
- **mathematical**: MATH dataset, GSM8K, AIME scores
- **scientific**: SciQ, PubMedQA, GPQA Diamond
- **philosophical**: Ethical reasoning, argument analysis
- **legal**: LegalBench, contract analysis
- **medical**: MedQA, clinical reasoning

#### 📊 ANALYSIS CATEGORIES
- **analysis**: Data interpretation, statistical reasoning
- **data_analysis**: Chart reading, data visualization
- **historical**: HistoryQA, temporal reasoning
- **current_events**: News analysis (use MCP tools)
- **web_search**: Information synthesis

#### 🗣️ COMMUNICATION CATEGORIES
- **general_chat**: Conversational ability
- **question_answering**: MMLU, factual QA
- **tutorial**: Educational instruction
- **role_play**: Character consistency
- **personal_advice**: Empathy, practical guidance

#### 🌐 LANGUAGE CATEGORIES
- **translation**: WMT datasets, FLORES-200
- **summarization**: CNN/DailyMail, XSum

#### 🎨 CREATIVE CATEGORIES
- **brainstorming**: Idea generation, creativity
- **creative**: Original content, artistic expression

#### 👁️ VISION CATEGORIES
- **image_analysis**: VQAv2, COCO-QA (if vision capable)
- **multimodal**: Cross-modal tasks

#### 🔍 OTHER
- **other**: Specialized domain tasks

### STEP 3: HYBRID EVIDENCE-BASED SCORING

🚨 **CRITICAL ARENA ELO HANDLING**:
- **Arena ELO scores MUST be used directly** - NO arbitrary percentage conversion
- **Example**: Claude 3 Opus ELO 1265 → `"general_chat": {"score": 1265}`
- **NOT**: ~~Claude 3 Opus ELO 1265 → `"general_chat": {"score": 85}` (WRONG!)~~
- **ELO Range**: Typical range 800-1500, higher = better conversational quality
- **Storage**: Store raw ELO value, add note field for clarity: `"note": "ELO_direct_not_percentage"`

**IF percentage needed for router compatibility, use official ELO mathematics**:
- **Logistic Win-Probability Formula**: `P(A beats average) = 1 / (1 + 10^((1000 - ELO) / 400))`
- **Example**: ELO 1265 → `P = 1 / (1 + 10^((1000-1265)/400)) = 0.821 = 82.1%`
- **Reference**: 1000 ELO as "average LLM" baseline (standard in research)
- **Note**: This gives theory-backed win probability vs average model

🚨 **MANDATORY 28-CATEGORY VALIDATION CHECKLIST**:
Before proceeding to router scores, verify ALL categories are mapped:

**Coding (2)**: ✅ coding, ✅ debugging
**Writing (3)**: ✅ creative_writing, ✅ technical_writing, ✅ business_writing  
**Reasoning (6)**: ✅ reasoning, ✅ mathematical, ✅ scientific, ✅ philosophical, ✅ legal, ✅ medical
**Analysis (4)**: ✅ analysis, ✅ data_analysis, ✅ historical, ✅ current_events, ✅ web_search
**Communication (5)**: ✅ general_chat, ✅ question_answering, ✅ tutorial, ✅ role_play, ✅ personal_advice
**Language (2)**: ✅ translation, ✅ summarization
**Creative (2)**: ✅ brainstorming, ✅ creative
**Vision (3)**: ✅ image_analysis, ✅ multimodal, ✅ image_generation
**Other (1)**: ✅ other

**TOTAL: 28 categories - ALL MUST BE SCORED**

#### Scoring Framework (0-100 scale)
- **90-100**: Exceptional performance, verified by multiple benchmarks
- **80-89**: Strong performance, reliable benchmark data
- **70-79**: Good performance, some benchmark evidence
- **60-69**: Adequate performance, limited evidence
- **50-59**: Weak performance, poor benchmark results
- **Below 50**: Poor performance, multiple benchmark failures
- **null**: Insufficient evidence or capability not available

#### Decimal Place Conventions - STATISTICAL BEST PRACTICES
- **Pass/fail benchmarks** (HumanEval, GPQA): Use **1 decimal place** (e.g., 87.5%)
  - Sample sizes ≤1,000: ±0.5pp is statistically significant
  - Avoid false precision with XX.XX format
- **Continuous metrics** (BLEU, ROUGE-L): Use **2-3 decimal places** (e.g., 0.823)
  - Following WMT reports and standard NLP conventions
- **Large datasets** (>10,000 samples): Can justify 2 decimal places
- **Derived/fallback scores**: Round to nearest integer or 0.5 increment

#### Data Source Priority - MAXIMUM PRECISION
1. **Download raw data first**: CSV/Parquet files with n_correct/n_total
   - Open-LLM Leaderboard: "Download full CSV" button
   - GPQA-Diamond: Per-question JSON in GitHub repo
   - HumanEval: Papers-with-Code API with both fields
   - SWE-bench: Evaluation guide raw data
2. **Calculate exact ratios**: pass@1 = correct/total (keep 4 decimals internally)
3. **Round for display**: Apply conventions above for final scores
4. **Avoid web scraping**: HTML tables often pre-rounded and mis-sorted

#### Evidence Requirements - PRACTICAL APPROACH

**For Categories with Direct Benchmarks** (~12 categories):
1. **Primary benchmark score** (required)
2. **API test validation** (if model accessible)
3. **Research corroboration** (papers, leaderboards)

**For Categories with Fallback Mapping** (~16 categories):
1. **Copy parent task-key score** (e.g., business_writing ← cre)
2. **Mark as derived**: `"_is_derived": true`
3. **Lower confidence**: 0.45-0.60 range
4. **Document fallback**: `"sources": ["fallback_from_cre"]`

#### Confidence Calculation - REALISTIC LEVELS
- **0.90-0.95**: Direct benchmark + API validation + research (coding, math, reasoning)
- **0.75-0.89**: Single benchmark + research (translation, QA, analysis)
- **0.60-0.74**: Limited benchmark or strong proxy (creative_writing, general_chat)
- **0.45-0.59**: Derived from parent category (business_writing, philosophical)
- **0.00**: No evidence available (current_events without web, other)

### STEP 3.5: ROUTER-CRITICAL FIELD COLLECTION (NEW V6.9)

🚨 **MANDATORY ROUTER FIELDS**: The following fields are ESSENTIAL for router performance and MUST be collected for every model:

#### SPEED CATEGORIZATION (CRITICAL - Router Impact: 10% of final score)
**Current Gap**: Only 58/197 models have speed data
**Router Usage**: Affects latency penalties and speed-sensitive routing

**Collection Method**:
```javascript
// 🚨 MANDATORY VERIFICATION - NO GUESSING
// Use MCP tools for REAL-TIME speed research:
mcp__perplexity-ask__search("MODEL_NAME inference speed tokens per second benchmark latency 2025")

// ONLY assign if VERIFIED from sources:
"speed": "ultra-fast|very-fast|fast|medium|slow"
"speed_confidence": 0.XX  // Based on source quality
"speed_sources": ["artificial_analysis_2025", "provider_specs"]

// VERIFIED Research sources (priority order):
1. ✅ Provider official specifications (tokens/second, latency specs)
2. ✅ Artificial Analysis speed benchmarks (artificialanalysis.ai)
3. ✅ Academic papers with timing data (Papers with Code)
4. ✅ LiteLLM documented speed classifications
5. ⚠️ Community benchmarks (GitHub, Reddit) - lower confidence

// ❌ DO NOT USE: Speculation, outdated data, unverified claims
// ❌ IF UNCERTAIN: Set to null with reason: "insufficient_speed_data"
```

**Speed Assignment Guidelines**:
- **ultra-fast**: >10,000 tokens/sec (Groq, specialized hardware)
- **very-fast**: 1,000-10,000 tokens/sec (GPT-4o, Claude 3.5 Haiku)
- **fast**: 100-1,000 tokens/sec (Most modern models)
- **medium**: 10-100 tokens/sec (Standard models)
- **slow**: <10 tokens/sec (Large reasoning models, o1 series)

#### FUNCTION CALLING DETECTION (CRITICAL - Router Impact: 20% contextual bonus)
**Current Gap**: Only 2/197 models have function calling data
**Router Usage**: Essential for tool use detection and routing

**Collection Method**:
```javascript
// 🚨 MANDATORY VERIFICATION - CONFIRMED CAPABILITY ONLY
// Research with CURRENT documentation:
mcp__perplexity-ask__search("MODEL_NAME function calling tools API support 2025 official documentation")
mcp__context7__get-library-docs(context7CompatibleLibraryID="/openai/platform", topic="function calling")

// ONLY set to true if VERIFIED from official sources:
"function_calling": true/false,
"supports_tools": true/false,
"function_calling_confidence": 0.XX,
"function_calling_sources": ["openai_api_docs_2025", "provider_official"]

// ❌ NEVER ASSUME: Just because a model is "modern" doesn't mean it has function calling
// ❌ IF UNVERIFIED: Set to null with reason: "capability_unverified"
```

**VERIFIED Function Calling Research Sources**:
1. ✅ **Provider API documentation** (function/tool calling sections)
2. ✅ **Official model cards** and capability specifications  
3. ✅ **Provider blog posts** about tool use features
4. ✅ **API endpoint testing** (if accessible)
5. ⚠️ Community testing - use only as supporting evidence

**Critical Verification**: Many models DON'T have function calling - verify each individually

#### WEB SEARCH CAPABILITY (CRITICAL - Router Impact: Filters models for current events)
**Current Gap**: Only 2/197 models have web search data
**Router Usage**: Filters available models for current events queries

**Collection Method**:
```javascript
// 🚨 MANDATORY VERIFICATION - CONFIRMED WEB ACCESS ONLY
// Research with REAL-TIME data:
mcp__perplexity-ask__search("MODEL_NAME web search browsing internet access capability 2025 official")

// ONLY set to true if EXPLICITLY CONFIRMED:
"web_search": true/false,
"supports_web_search": true/false,
"web_search_confidence": 0.XX,
"web_search_sources": ["perplexity_official", "provider_docs"]

// ❌ DO NOT ASSUME: Many models don't have web access - verify individually
// ❌ IF UNCONFIRMED: Set to false/null with reason: "web_access_unverified"
```

**VERIFIED Web Search Models** (Only confirmed ones):
- ✅ **Perplexity sonar models**: Explicitly designed for web search
- ⚠️ **GPT models**: Research each variant - NOT ALL have browsing
- ⚠️ **Claude models**: Research each variant - LIMITED web access
- ⚠️ **Search-augmented**: Verify case-by-case

**Critical**: Most models DON'T have web search - only flag confirmed ones

#### REASONING TRACE DETECTION (CRITICAL - Router Impact: o1/o3/QwQ identification)
**Current Gap**: 0/197 models have reasoning trace data
**Router Usage**: Critical for identifying reasoning models that show thinking process

**Collection Method**:
```javascript
// Research reasoning trace capabilities:
mcp__perplexity-ask__search("MODEL_NAME reasoning trace thinking process o1 style CoT")

// Identify models that show reasoning:
"reasoning_trace": true/false  // Shows step-by-step thinking
```

**Reasoning Trace Models**:
- **o1 series**: o1-preview, o1-mini (explicit reasoning)
- **o3 series**: o3-mini, o3 (reasoning capabilities)
- **QwQ models**: Alibaba's reasoning models
- **Reasoning-specific variants**: Any model that exposes thinking process

#### CONTEXT WINDOW STANDARDIZATION (CRITICAL - Router Impact: Large context bonus)
**Current Gap**: 73/197 models missing context window data
**Router Usage**: 20% bonus for large context, filtering for long conversations

**Collection Method**:
```javascript
// Research context window specs:
mcp__perplexity-ask__search("MODEL_NAME context window maximum tokens 2025")
mcp__context7__get-library-docs(context7CompatibleLibraryID="/openai/platform", topic="context length")

// Standardize format:
"contextWindow": 128000,      // Maximum input tokens
"maxOutputTokens": 4096       // Maximum output tokens
```

**Context Window Research**:
1. Provider official specifications
2. Model cards and documentation
3. API endpoint limits
4. Community testing results

#### DATA QUALITY FIXES (CRITICAL - Fix existing bad data)
**Current Gap**: Multiple models have impossible scores >10
**Router Usage**: Accurate scoring is essential for model selection

**Quality Checks Required**:
```javascript
// 1. IMPOSSIBLE SCORE DETECTION
// Flag any score >10 when max should be 10
if (score > 10) {
  flag_for_recalculation = true;
  confidence = 0.3;  // Lower confidence for impossible scores
}

// 2. SCALE NORMALIZATION 
// Convert ALL scores to 0-10 scale
if (benchmark_scale === "0-100") {
  normalized_score = original_score / 10;
} else if (benchmark_scale === "0-1") {
  normalized_score = original_score * 10;
}

// 3. REALISTIC SCORE DISTRIBUTION
// Ensure scores follow realistic patterns:
// - Few perfect 10s (only for exceptional performance)
// - Normal distribution around 6-8 for most models
// - Avoid clustering at round numbers (85, 90, etc.)
```

#### SPECIFIC ATTRIBUTES COLLECTION (ENHANCEMENT - Router Impact: 15-20% contextual scoring)
**Current Gap**: Only 15/3,874 mappings have specific attributes
**Router Usage**: Language/framework matching for coding tasks

**Programming Language Support** (for coding models):
```javascript
// Research language capabilities:
mcp__perplexity-ask__search("MODEL_NAME programming languages supported coding")

// Collect specific languages:
"languages": ["python", "javascript", "typescript", "java", "cpp", "rust", "go", "swift"]
```

**Framework Expertise** (for web development models):
```javascript
// Research framework knowledge:
"frameworks": ["react", "node", "django", "spring", "tensorflow", "pytorch"]
```

**Domain Specialization**:
```javascript
// Math models:
"math_types": ["calculus", "algebra", "statistics", "discrete", "applied"]

// Analysis models:
"data_types": ["tabular", "time_series", "text", "image"]
```

### STEP 4: ENHANCED COST AND CAPABILITY DETECTION

#### Cost Data (July 2025 pricing)
```json
{
  "inputCost": 0.00, // USD per 1M tokens
  "outputCost": 0.00, // USD per 1M tokens
  "currency": "USD",
  "per_tokens": 1000000,
  "costCategory": "FREE|BUDGET|STANDARD|PREMIUM",
  "planAvailability": ["free", "plus", "max"]
}
```

#### Enhanced Capability Detection (V6.9)
🚨 **MANDATORY**: Test actual capabilities using MCP research, don't assume:

```json
{
  // VISION CAPABILITIES (Enhanced Detection)
  "vision": true,                    // Image processing support
  "vision_confidence": 0.95,         // Confidence in vision capability
  
  // FUNCTION CALLING (NEW - Critical for Router)
  "function_calling": true,          // Can call functions/tools
  "supports_tools": true,            // General tool use capability
  "function_calling_confidence": 0.90, // Research confidence
  
  // WEB SEARCH (NEW - Critical for Router)  
  "web_search": false,               // Has web access capability
  "supports_web_search": false,      // Alternative field name
  "web_search_confidence": 0.85,     // Research confidence
  
  // REASONING TRACE (NEW - Critical for o1/o3 identification)
  "reasoning_trace": false,          // Shows step-by-step thinking (o1-style)
  "reasoning_confidence": 0.90,      // Research confidence
  
  // CONTEXT WINDOW (Enhanced - Critical for Router)
  "contextWindow": 128000,           // Maximum input tokens (standardized field)
  "maxOutputTokens": 4096,           // Maximum output tokens
  "large_context": true,             // Context > 100K tokens
  "context_confidence": 0.95,        // Specification confidence
  
  // SPEED CATEGORY (NEW - Critical for Router)
  "speed": "fast",                   // ultra-fast|very-fast|fast|medium|slow
  "speed_confidence": 0.80,          // Research confidence
  
  // EXISTING CAPABILITIES (Enhanced)
  "reasoning": true,                 // Based on reasoning benchmarks
  "streaming": true,                 // Test streaming response
  "multimodal": true                 // Combined text/vision capabilities
}
```

**ENHANCED VISION CAPABILITY RESEARCH**:
Research EACH model individually - many models incorrectly flagged:
```javascript
// Expected vision-capable models (~50+ models should have vision):
- All GPT-4V variants (gpt-4-vision-preview, gpt-4o, gpt-4o-mini)
- All Claude 3+ series (claude-3-opus, claude-3-sonnet, claude-3-haiku, claude-3.5-sonnet, claude-3.5-haiku)
- All Claude 4 series (claude-4-opus when available)
- All Gemini Pro/Flash variants (gemini-1.5-pro, gemini-1.5-flash, gemini-2.0-flash)
- Qwen-VL series (qwen-vl, qwen2-vl)
- Many other multimodal models

// Research command:
mcp__perplexity-ask__search("MODEL_NAME vision image processing multimodal capabilities 2025 API")
```

**FUNCTION CALLING RESEARCH** (Expected: ~80+ models):
```javascript
// Most modern models should support function calling:
- All recent GPT models (GPT-4, GPT-4o series)
- All Claude 3+ models (with tool use)
- Most Gemini models (with function calling)
- Many Groq, xAI, Mistral models
- Check provider documentation for "function calling", "tool use", "API calls"

// Research command:
mcp__perplexity-ask__search("MODEL_NAME function calling tool use API support documentation")
```

**WEB SEARCH IDENTIFICATION** (Expected: ~15+ models):
```javascript
// Models with confirmed web access:
- All Perplexity models (sonar series)
- Some GPT variants with browsing
- Search-augmented models
- Models specifically designed for real-time information

// Research command: 
mcp__perplexity-ask__search("MODEL_NAME web search internet browsing real-time data access")
```

**REASONING TRACE MODELS** (Expected: ~10+ models):
```javascript
// Models that show reasoning process:
- o1 series (o1-preview, o1-mini)
- o3 series (o3-mini, o3)  
- QwQ models (Alibaba reasoning models)
- Any model that exposes "thinking" or step-by-step reasoning

// Research command:
mcp__perplexity-ask__search("MODEL_NAME reasoning trace thinking process chain of thought o1 style")
```

### STEP 5: ROUTER COMPATIBILITY

#### 28 Router Categories → 8 Task Keys Mapping
```javascript
// Aggregate 28 categories into 8 router-compatible scores
const taskScores = {
  cod: calculateCodingScore([coding, debugging, technical_writing]),
  cre: calculateCreativeScore([creative_writing, creative, brainstorming]),
  rea: calculateReasoningScore([reasoning, mathematical, scientific, philosophical, legal]),
  mat: calculateMathScore([mathematical]), // Pure math performance
  ana: calculateAnalysisScore([analysis, data_analysis, historical, current_events]),
  lng: calculateLanguageScore([translation, summarization, general_chat, question_answering]),
  cha: calculateChatScore([general_chat, role_play, personal_advice, tutorial]),
  vis: calculateVisionScore([image_analysis, multimodal]) // 0.0 if no vision
};

// **CRITICAL**: For router aggregation, convert ELO using official mathematics only for task_scores
// Logistic Win-Probability Formula: P = 1 / (1 + 10^((1000 - ELO) / 400)) * 100
// Example: ELO 1265 → P = 1 / (1 + 10^((1000-1265)/400)) * 100 = 82.1%
// Store raw ELO in category_scores, logistic probability in task_scores
```

## 📊 OUTPUT FORMAT

### Required JSON Structure (V6.9 Enhanced)
```json
{
  "model": "provider/model-name",
  "validation_timestamp": "2025-07-07T12:00:00Z",
  "methodology": "V6.9",
  "confidence_level": 0.92,
  
  
  "benchmark_scores": {
    "AIME_2025": 87.5,      // 1 decimal (pass/fail, <1k items)
    "HumanEval": 89.2,      // 1 decimal (164 problems)
    "GPQA_Diamond": 81.0,   // 1 decimal (448 questions)
    "MATH": 88.5,           // 1 decimal (5k problems)
    "GSM8K": 89.3,          // 1 decimal (8.5k problems)
    "MMLU": 82.64,          // 2 decimals (>10k questions)
    "LiveCodeBench": 73.3,  // 1 decimal (moderate size)
    "BLEU": 0.823,          // 3 decimals (continuous metric)
    "ROUGE-L": 0.756        // 3 decimals (continuous metric)
  },
  
  "cost_data": {
    "inputCost": 0.14,
    "outputCost": 0.28,
    "currency": "USD",
    "per_tokens": 1000000,
    "costCategory": "STANDARD",
    "planAvailability": ["free", "plus", "max"]
  },
  
  "capabilities": {
    // ENHANCED CAPABILITIES (V6.9) - ALL FIELDS MANDATORY
    "vision": true,                    // Image processing support
    "vision_confidence": 0.95,         // Research confidence (0-1)
    
    "function_calling": true,          // Can call functions/tools (NEW)
    "supports_tools": true,            // General tool use capability (NEW)
    "function_calling_confidence": 0.90, // Research confidence (NEW)
    
    "web_search": false,               // Has web access capability (NEW)
    "supports_web_search": false,      // Alternative field name (NEW)  
    "web_search_confidence": 0.85,     // Research confidence (NEW)
    
    "reasoning_trace": false,          // Shows step-by-step thinking (NEW)
    "reasoning_confidence": 0.90,      // Research confidence (NEW)
    
    "contextWindow": 128000,           // Maximum input tokens (STANDARDIZED)
    "maxOutputTokens": 4096,           // Maximum output tokens (ENHANCED)
    "large_context": true,             // Context > 100K tokens (NEW)
    "context_confidence": 0.95,        // Specification confidence (NEW)
    
    "speed": "fast",                   // ultra-fast|very-fast|fast|medium|slow (NEW)
    "speed_confidence": 0.80,          // Research confidence (NEW)
    
    "reasoning": true,                 // Based on reasoning benchmarks
    "streaming": true,                 // Test streaming response
    "multimodal": true                 // Combined text/vision capabilities
  },
  
  "specific_attributes": {
    // PROGRAMMING CAPABILITIES (NEW V6.9)
    "languages": ["python", "javascript", "typescript", "java", "cpp", "rust"],
    "frameworks": ["react", "node", "django", "spring", "tensorflow"],
    "language_confidence": 0.75,       // Research confidence for languages
    
    // DOMAIN SPECIALIZATION (NEW V6.9)
    "math_types": ["calculus", "algebra", "statistics"],  // For math models
    "data_types": ["tabular", "time_series", "text"],     // For analysis models
    "domain_confidence": 0.70          // Research confidence for domain
  },
  
  "category_scores": {
    "analysis": {"score": 81.0, "confidence": 0.95, "sources": ["GPQA_Diamond", "MMLU"], "_is_derived": false, "_evidence_count": 2},
    "brainstorming": {"score": 75, "confidence": 0.45, "sources": ["fallback_from_cre"], "_is_derived": true, "_evidence_count": 0},
    "business_writing": {"score": 78, "confidence": 0.45, "sources": ["fallback_from_cre"], "_is_derived": true, "_evidence_count": 0},
    "coding": {"score": 89.2, "confidence": 0.98, "sources": ["HumanEval", "LiveCodeBench"]},
    "creative": {"score": 72, "confidence": 0.70, "sources": ["creative_tasks"]},
    "creative_writing": {"score": 74, "confidence": 0.75, "sources": ["writing_tasks"]},
    "current_events": {"score": 75.5, "confidence": 0.75, "sources": ["RealTime_QA", "NewsQA"], "_is_derived": false, "_evidence_count": 2},
    "data_analysis": {"score": 83.0, "confidence": 0.85, "sources": ["BRIGHT", "BEIR"]},
    "debugging": {"score": 85.0, "confidence": 0.90, "sources": ["SWE-bench"]},
    "general_chat": {"score": 1372, "confidence": 0.95, "sources": ["Arena_ELO"], "note": "ELO_direct_not_percentage", "_is_derived": false, "_evidence_count": 1},
    "historical": {"score": 80.0, "confidence": 0.80, "sources": ["HQA-Data"]},
    "image_analysis": {"score": null, "confidence": 0.0, "sources": [], "reason": "no_vision"},
    "legal": {"score": 78.5, "confidence": 0.90, "sources": ["LegalBench"]},
    "mathematical": {"score": 88.5, "confidence": 0.95, "sources": ["MATH", "GSM8K", "AIME"]},
    "medical": {"score": 82.0, "confidence": 0.85, "sources": ["MedQA"]},
    "multimodal": {"score": null, "confidence": 0.0, "sources": [], "reason": "text_only"},
    "other": {"score": 79.0, "confidence": 0.75, "sources": ["general_capability"]},
    "personal_advice": {"score": 76.0, "confidence": 0.70, "sources": ["chat_ability"]},
    "philosophical": {"score": 80.0, "confidence": 0.75, "sources": ["reasoning_tasks"]},
    "question_answering": {"score": 82.6, "confidence": 0.90, "sources": ["MMLU", "QA_tasks"]},
    "reasoning": {"score": 88.0, "confidence": 0.95, "sources": ["AIME", "GPQA", "logical_reasoning"]},
    "role_play": {"score": 77.0, "confidence": 0.70, "sources": ["chat_ability"]},
    "scientific": {"score": 84.0, "confidence": 0.85, "sources": ["GPQA_Diamond", "MMLU_science"]},
    "summarization": {"score": 81.0, "confidence": 0.80, "sources": ["text_processing"]},
    "technical_writing": {"score": 85.0, "confidence": 0.85, "sources": ["code_documentation"]},
    "translation": {"score": 78.0, "confidence": 0.75, "sources": ["multilingual_tasks"]},
    "tutorial": {"score": 83.0, "confidence": 0.80, "sources": ["instructional_ability"]},
    "web_search": {"score": 78.0, "confidence": 0.80, "sources": ["WebQA_Hit@1", "IFIR", "BEIR"], "_is_derived": false, "_evidence_count": 3}
  },
  
  "task_scores": {
    "cod": 87.1,
    "cre": 73.5,
    "rea": 88.0,
    "mat": 88.5,
    "ana": 82.0,
    "lng": 78.0,
    "cha": 83.4,  // Logistic probability from ELO 1372: 1/(1+10^((1000-1372)/400))*100 = 83.4%
    "vis": 85.0   // Updated for vision-capable models
  },
  
  "validation_notes": {
    "api_issue": "LiteLLM proxy unavailable - used direct API validation",
    "strengths": "Exceptional math and reasoning performance, strong coding abilities",
    "limitations": "No vision capabilities, no web search, limited creative writing",
    "provider_agnostic": "Scores based on actual benchmarks, not provider reputation",
    "evidence_quality": "High - multiple verified benchmark sources"
  }
}
```

## 🚨 CRITICAL SUCCESS CRITERIA

### V6.9 Validation Requirements (ENHANCED)
1. **✅ Provider Bias Eliminated**: Scores based on performance, not reputation
2. **✅ Real Benchmarks Used**: Actual HumanEval, AIME, GPQA scores
3. **✅ 28 Distinct Categories**: True category assessment vs 8-key mapping
4. **✅ Evidence Documentation**: Every score linked to sources
5. **✅ Honest Capability Detection**: Returns null for missing capabilities
6. **✅ Confidence Metrics**: Transparent confidence based on evidence

### NEW V6.9 ROUTER-CRITICAL REQUIREMENTS
7. **✅ Speed Categorization**: MANDATORY - All models must have speed classification
8. **✅ Function Calling Detection**: MANDATORY - Research actual tool use capabilities
9. **✅ Web Search Identification**: MANDATORY - Identify models with web access
10. **✅ Reasoning Trace Detection**: MANDATORY - Flag o1/o3/QwQ style models
11. **✅ Context Window Standardization**: MANDATORY - Consistent contextWindow format
12. **✅ Data Quality Fixes**: MANDATORY - Fix impossible scores >10, normalize scales
13. **✅ Specific Attributes**: RECOMMENDED - Languages, frameworks for contextual scoring
14. **✅ Enhanced Vision Detection**: CRITICAL - Fix incorrect vision flags (46 false negatives)

### Failure Conditions (V6.7/V6.8 Flaws to Avoid)
- ❌ **Provider clustering**: Same scores for models from same provider
- ❌ **Round number bias**: Scores clustered at 85, 90, etc.
- ❌ **Impossible perfection**: Multiple 90+ scores without justification
- ❌ **Missing evidence**: Scores without benchmark or API backing
- ❌ **False capabilities**: Claiming capabilities without testing
- ❌ **Missing router fields**: Speed, function calling, web search MUST be included
- ❌ **Impossible scores**: Any score >10 when max should be 10
- ❌ **Scale inconsistency**: Mixed 0-10, 0-100, 0-1 scales without normalization

## 💡 PRACTICAL IMPLEMENTATION GUIDELINES

### Time and Resource Constraints
- **60-minute timeout**: Generous timeout to prevent validation failures
- **Token efficiency**: Prioritize evidence-rich categories first
- **Parallel processing**: Use concurrent MCP calls for different evidence types
- **Cache benchmarks**: Use local benchmark files to reduce API calls

### 🤖 AUTOMATION EXECUTION (Crontab Integration)
**For automated execution via crontab every 5 minutes:**

🔒 **SINGLE MODEL CONSTRAINT**: Process EXACTLY ONE MODEL per execution - no batches, no loops, no multiple tasks.

1. **Database Model Selection** (REPLACES Task Master):
   ```sql
   -- Step 1: Connect and select next model
   mysql -h 127.0.0.1 -u root -pDmggg13031988*** justsimplechat_production -e "
   SELECT canonicalName FROM AIModel WHERE isEnabled = true AND (
     JSON_EXTRACT(metadata, '$.lastValidated') IS NULL OR
     JSON_EXTRACT(metadata, '$.methodology') != 'V6.9' OR
     JSON_EXTRACT(metadata, '$.capabilities.speed') IS NULL
   ) ORDER BY 
     CASE WHEN JSON_EXTRACT(metadata, '$.lastValidated') IS NULL THEN 1 ELSE 2 END,
     JSON_EXTRACT(metadata, '$.lastValidated') ASC
   LIMIT 1;"
   
   -- Step 2: Mark as in-progress immediately
   mysql -h 127.0.0.1 -u root -pDmggg13031988*** justsimplechat_production -e "
   UPDATE AIModel SET metadata = JSON_SET(metadata, 
     '$.validationStatus', 'in-progress',
     '$.validationStarted', NOW()
   ) WHERE canonicalName = 'SELECTED_MODEL';"
   ```

2. **Execution Constraints**:
   - **EXACTLY ONE MODEL** per execution cycle (critical)
   - **Maximum 60 minutes** per validation (generous timeout to prevent failures)
   - **ALL 28 categories** must be completed for that ONE model
   - **Lock file protection** prevents concurrent executions
   - **Failsafe protection** prevents >20 concurrent instances
   - **Stop immediately** after completing one model validation

3. **Automation Success Criteria (V6.9 Enhanced)**:
   - ✅ EXACTLY ONE task retrieved and set to in-progress
   - ✅ EXACTLY ONE model validated (not multiple models)
   - ✅ ALL 28 categories mapped with scores for that ONE model
   - ✅ 8 router scores generated for Thompson Sampling for that ONE model
   - ✅ **ALL ROUTER-CRITICAL FIELDS** collected for that ONE model:
     - ✅ Speed categorization (ultra-fast/very-fast/fast/medium/slow)
     - ✅ Function calling detection (true/false with confidence)
     - ✅ Web search capability (true/false with confidence)
     - ✅ Reasoning trace detection (true/false with confidence)
     - ✅ Context window standardization (contextWindow + maxOutputTokens)
     - ✅ Enhanced vision capabilities (corrected false negatives)
     - ✅ Data quality fixes (no scores >10, normalized scales)
   - ✅ **SPECIFIC ATTRIBUTES** collected where applicable:
     - ✅ Programming languages for coding models
     - ✅ Frameworks for development models
     - ✅ Domain specializations where relevant
   - ✅ That ONE task marked as done upon completion
   - ✅ JSON summary output for ONE model only
   - ✅ Execution stops immediately after completing ONE model

4. **Error Recovery**:
   - If validation incomplete: Leave task as in-progress
   - If timeout exceeded: Log partial progress, resume next cycle
   - If no tasks available: Exit gracefully (automation continues)

### Evidence Priority Order
1. **Tier 1 (High Evidence)**: coding, mathematical, reasoning, translation
2. **Tier 2 (Medium Evidence)**: scientific, QA, summarization, data_analysis
3. **Tier 3 (Limited Evidence)**: creative_writing, general_chat, medical, legal
4. **Tier 4 (Fallback Only)**: business_writing, philosophical, role_play, etc.

### Hallucination Prevention
- **Never fabricate benchmark scores**
- **Use null for missing evidence** rather than guessing
- **Document all derived scores** with `_is_derived` flag
- **Include evidence count** for transparency

## 🎯 ROUTER COMPATIBILITY - IMPORTANT CLARIFICATION

### Production Integration Guidelines
- **8 task-keys (cod, cre, rea, mat, ana, lng, cha, vis)**: ONLY these feed into `selectModelWithThompsonSampling()`
- **28 category scores**: For analytics, UI dashboards, and detailed reporting ONLY
- **NO CHANGES** to existing router.ts logic - maintains backward compatibility
- **Thompson Sampling**: Continues using only the 8 core scores as before

### Data Flow Architecture
```javascript
// Router continues to use ONLY 8 scores
const routerInput = {
  cod: taskScores.cod,  // Aggregated from coding categories
  cre: taskScores.cre,  // Aggregated from creative categories
  rea: taskScores.rea,  // Aggregated from reasoning categories
  mat: taskScores.mat,  // Direct from mathematical
  ana: taskScores.ana,  // Aggregated from analysis categories
  lng: taskScores.lng,  // Aggregated from language categories
  cha: taskScores.cha,  // Aggregated from chat categories
  vis: taskScores.vis   // Aggregated from vision categories
};

// 28 categories stored separately for analytics/UI
const categoryScores = { /* all 28 detailed scores */ };
```

### Database Update Requirements (V6.9)
```sql
UPDATE AIModel 
SET metadata = JSON_SET(metadata, 
  -- Core router scores (8 keys)
  '$.taskScores', JSON_OBJECT('cod', ?, 'cre', ?, 'rea', ?, 'mat', ?, 'ana', ?, 'lng', ?, 'cha', ?, 'vis', ?),
  
  -- Enhanced capabilities (NEW V6.9)
  '$.capabilities.speed', ?,                      -- ultra-fast|very-fast|fast|medium|slow
  '$.capabilities.function_calling', ?,           -- true/false
  '$.capabilities.web_search', ?,                 -- true/false  
  '$.capabilities.reasoning_trace', ?,            -- true/false (o1/o3 style)
  '$.capabilities.contextWindow', ?,              -- standardized context window
  '$.capabilities.maxOutputTokens', ?,            -- maximum output tokens
  
  -- Specific attributes (NEW V6.9)
  '$.specific_attributes.languages', ?,           -- programming languages array
  '$.specific_attributes.frameworks', ?,          -- frameworks array
  
  -- Validation metadata
  '$.validationData', ?,
  '$.lastValidated', NOW(),
  '$.methodology', 'V6.9'
) 
WHERE canonicalName = ?;
```

## 📊 V6.9 ROUTER-OPTIMIZED APPROACH SUMMARY

### V6.9 Key Improvements (Router-Critical)
1. **Router-Critical Field Collection**: Speed, function calling, web search, reasoning trace
2. **Data Quality Fixes**: Impossible score detection, scale normalization
3. **Enhanced Vision Detection**: Fix 46+ false negatives 
4. **Context Window Standardization**: Consistent format across all models
5. **Specific Attributes**: Programming languages, frameworks for contextual scoring
6. **MCP Research Integration**: Perplexity and Context7 for comprehensive data collection

### Expected Outcomes (V6.9 Enhanced)
- **Router Performance**: Dramatically improved with complete field coverage
- **Speed Categorization**: All 197 models with speed classification
- **Function Calling**: ~80+ models correctly identified as tool-capable
- **Web Search**: ~15+ models identified with web access
- **Vision Capabilities**: ~50+ models correctly flagged (vs. current 21)
- **Context Windows**: Standardized format for all models
- **Data Quality**: All scores normalized to 0-10 scale, no impossible values

### V6.9 Data Collection Priorities
1. **TIER 1 (Critical)**: Speed, function calling, web search, reasoning trace
2. **TIER 2 (Quality)**: Fix impossible scores, normalize scales, context windows
3. **TIER 3 (Enhancement)**: Programming languages, frameworks, domain expertise
4. **TIER 4 (Optimization)**: Advanced benchmarks, specialized capabilities

### Router Impact Analysis
- **Speed Field**: Affects 10% of router score + latency penalties
- **Function Calling**: Provides 20% contextual bonus for tool use
- **Web Search**: Essential filtering for current events queries
- **Vision**: 30% bonus for image processing tasks
- **Context Window**: 20% bonus for large context tasks
- **Specific Attributes**: 15-20% contextual scoring improvement

### Automation Readiness (V6.9)
- **MCP Tools**: Perplexity, Context7, Firecrawl configured and ready
- **Research Commands**: Optimized prompts for each field type
- **Quality Checks**: Built-in impossible score detection
- **Confidence Tracking**: Research confidence for all new fields
- **Error Recovery**: Graceful handling of missing data

---

**This V6.9 router-optimized methodology provides COMPLETE model validation with ALL router-critical fields, dramatically improving routing performance while maintaining evidence-based accuracy. It transforms the router from operating with incomplete data (gaps in 70%+ of models) to having comprehensive coverage for intelligent routing decisions.**