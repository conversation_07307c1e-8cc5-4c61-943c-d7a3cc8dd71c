# Web Search Capabilities Documentation

## Overview
Several AI models in JustSimpleChat support native web search capabilities, allowing them to access real-time information without requiring separate web search API calls.

## Models with Native Web Search

### xAI (Grok)
- **Models**: All Grok models except `grok-2-mini`
- **Default**: Web search is enabled by default
- **Configuration**: Via `search_parameters` in API request

### Perplexity
- **Models**: All Perplexity models
- **Default**: Web search is built into their core functionality
- **Configuration**: Automatic based on query

## xAI Web Search Implementation

### Basic Configuration
```typescript
const requestBody = {
  model: 'grok-3',
  messages: [...],
  search_parameters: {
    mode: 'auto',  // 'off' | 'auto' | 'on'
    return_citations: true,
    sources: [
      { type: 'web' },
      { type: 'x' },
      { type: 'news' },
      { type: 'rss' }
    ]
  }
};
```

### Search Modes
- **`off`**: Disables search completely
- **`auto`**: Model decides whether to search (DEFAULT)
- **`on`**: Forces search for every query

### Available Data Sources

#### 1. Web Search
```typescript
{
  type: 'web',
  country?: 'US',  // ISO alpha-2 code
  excluded_websites?: ['example.com'],  // Max 5
  allowed_websites?: ['specific.com'],  // Max 5, can't use with excluded
  safe_search?: true  // Default: true
}
```

#### 2. X (Twitter) Search
```typescript
{
  type: 'x',
  x_handles?: ['elonmusk', 'xai']  // Specific accounts only
}
```

#### 3. News Search
```typescript
{
  type: 'news',
  country?: 'US',
  excluded_websites?: ['cnn.com'],
  safe_search?: true
}
```

#### 4. RSS Feeds
```typescript
{
  type: 'rss',
  links: ['https://example.com/feed.xml']  // One RSS link
}
```

### Advanced Options

#### Date Range Filtering
```typescript
search_parameters: {
  mode: 'auto',
  from_date: '2024-01-01',  // ISO8601 format
  to_date: '2024-12-31'
}
```

#### Limit Search Results
```typescript
search_parameters: {
  mode: 'auto',
  max_search_results: 10  // Default: 20
}
```

### Citations in Response
When `return_citations: true`, the response includes source URLs:
- **Non-streaming**: Citations in the response object
- **Streaming**: Citations in the final chunk only

## Implementation in JustSimpleChat

### Current Implementation
The xAI provider (`/src/lib/providers/xai/index.ts`) includes web search support:

```typescript
private getXAISpecificParams(model: string, options: any): any {
  const params: any = {};

  // Enable real-time data access for supported models
  if (model !== 'grok-2-mini' && options.useRealtimeData !== false) {
    params.enable_realtime_data = true;
    params.search_web = options.searchWeb ?? true;
    params.include_sources = options.includeSources ?? false;
  }

  return params;
}
```

### Proposed Enhancement for Web Search Button

To implement a dedicated "Web Search" button in the prompt box:

1. **Update ChatOptions interface** (`/src/lib/providers/base.ts`):
```typescript
export interface ChatOptions {
  // ... existing options
  searchParameters?: {
    mode: 'off' | 'auto' | 'on';
    returnCitations?: boolean;
    sources?: SearchSource[];
    fromDate?: string;
    toDate?: string;
    maxSearchResults?: number;
  };
}
```

2. **Update xAI Provider**:
```typescript
private getXAISpecificParams(model: string, options: ChatOptions): any {
  const params: any = {};

  // Handle new search parameters format
  if (options.searchParameters) {
    params.search_parameters = {
      mode: options.searchParameters.mode,
      return_citations: options.searchParameters.returnCitations ?? true,
      sources: options.searchParameters.sources ?? [
        { type: 'web' },
        { type: 'x' }
      ],
      from_date: options.searchParameters.fromDate,
      to_date: options.searchParameters.toDate,
      max_search_results: options.searchParameters.maxSearchResults
    };
  }

  return params;
}
```

3. **Add Web Search Toggle to Chat Input**:
- Add a search icon button next to the send button
- When enabled, set `searchParameters.mode = 'on'`
- Show indicator when web search is active
- Display citations in the response

## Benefits

1. **No Additional API Calls**: Web search is handled natively by the model
2. **Real-time Information**: Access to current data without date training cutoffs
3. **Multiple Sources**: Can search web, news, social media, and RSS feeds
4. **Cost Effective**: Included in the model API cost
5. **Contextual Search**: Model decides what to search based on the query

## Security Considerations

1. **Safe Search**: Enabled by default
2. **Website Filtering**: Can exclude/allow specific domains
3. **Source Attribution**: Citations provided for transparency
4. **User Control**: Can be disabled per request

## Future Enhancements

1. **Smart Search Detection**: Automatically enable for queries that need current information
2. **Source Preferences**: User settings for preferred news sources, excluded domains
3. **Search History**: Track what searches were performed
4. **Citation Display**: Rich preview of sources in the UI
5. **Cross-Model Support**: Extend to other providers that support web search