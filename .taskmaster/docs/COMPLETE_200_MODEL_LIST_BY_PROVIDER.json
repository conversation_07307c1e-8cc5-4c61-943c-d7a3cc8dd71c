{"metadata": {"totalModels": 200, "lastUpdated": "2025-07-05T17:15:00Z", "source": "justsimplechat_production.AIModel", "purpose": "Complete model list for router.ts validation subagents"}, "priorityGroups": {"FRONTIER": {"priority": 1, "count": 12, "description": "Highest cost, most advanced models"}, "REASONING": {"priority": 2, "count": 4, "description": "Specialized reasoning models"}, "STANDARD": {"priority": 3, "count": 184, "description": "Production ready models"}}, "providers": {"Alibaba Cloud": {"count": 25, "models": [{"canonicalName": "alibaba/qwen-max", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-max-0125", "displayName": "<PERSON><PERSON> (Jan 2025)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-max-2025-01-25", "displayName": "<PERSON><PERSON> (Jan 2025)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-max-latest", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-plus", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-plus-2025-01-25", "displayName": "Qwen Plus 2025-01-25", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-plus-2025-04-28", "displayName": "Qwen Plus 2025-04-28", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-plus-latest", "displayName": "<PERSON><PERSON> Latest", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-turbo", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-turbo-2024-11-01", "displayName": "Qwen Turbo 2024-11-01", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-turbo-2025-04-28", "displayName": "Qwen Turbo 2025-04-28", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-turbo-latest", "displayName": "<PERSON>wen <PERSON> Latest", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-vl-max", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-vl-max-2025-04-08", "displayName": "Qwen VL Max 2025-04-08", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-vl-max-latest", "displayName": "<PERSON>wen VL <PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-vl-plus", "displayName": "Qwen VL Plus", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-vl-plus-2025-01-25", "displayName": "Qwen VL Plus 2025-01-25", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen-vl-plus-latest", "displayName": "Qwen VL Plus Latest", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "aliba<PERSON>/qwen2-7b-instruct", "displayName": "Qwen2 7B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen2.5-vl-32b-instruct", "displayName": "Qwen2.5 VL 32B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen2.5-vl-3b-instruct", "displayName": "Qwen2.5 VL 3B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen2.5-vl-72b-instruct", "displayName": "Qwen2.5 VL 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen2.5-vl-7b-instruct", "displayName": "Qwen2.5 VL 7B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen3-14b", "displayName": "Qwen3 14B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen3-32b", "displayName": "Qwen3 32B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "alibaba/qwen3-8b", "displayName": "Qwen3 8B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/qwen/qwen-2.5-72b-instruct", "displayName": "<PERSON><PERSON> 2.5 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/Qwen2-72B-Instruct", "displayName": "<PERSON><PERSON> 2 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/Qwen2-VL-72B-Instruct", "displayName": "Qwen 2 VL 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/Qwen2.5-72B-Instruct-Turbo", "displayName": "Qwen 2.5 72B Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/Qwen2.5-7B-Instruct-Turbo", "displayName": "Qwen 2.5 7B Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/Qwen2.5-VL-72B-Instruct", "displayName": "Qwen 2.5 VL 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/Qwen3-235B-A22B-fp8-tput", "displayName": "Qwen 3 235B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/Qwen/QwQ-32B", "displayName": "QwQ 32B", "costCategory": "STANDARD", "priority": 3}]}, "Anthropic": {"count": 7, "models": [{"canonicalName": "anthropic/claude-3-opus-latest", "displayName": "Claude 3 Opus (Original)", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "anthropic/claude-opus-4-0", "displayName": "Claude 4 Opus", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "anthropic/claude-3-7-sonnet-latest", "displayName": "Claude 3.7 Sonnet", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "anthropic/claude-3-haiku-20240307", "displayName": "Claude 3 Haiku", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "anthropic/claude-3.5-haiku", "displayName": "Claude 3.5 Haiku", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "anthropic/claude-3.5-sonnet", "displayName": "Claude 3.5 Sonnet", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "anthropic/claude-sonnet-4-0", "displayName": "Claude 4 Sonnet", "costCategory": "PREMIUM", "priority": 3}]}, "OpenAI": {"count": 42, "models": [{"canonicalName": "openai/gpt-4-turbo", "displayName": "GPT-4", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/gpt-4-turbo-2024-04-09", "displayName": "GPT-4 Turbo (April 2024)", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/gpt-4-turbo-preview", "displayName": "GPT-4 Turbo (2024-04-09)", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/gpt-4.5-preview-2025-02-27", "displayName": "GPT-4.5 Preview (2025-02-27)", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/o1", "displayName": "O1", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/o1-2024-12-17", "displayName": "O1 (2024-12-17)", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/o1-preview", "displayName": "O1 Preview", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/o1-pro", "displayName": "O1 Pro", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/o3-pro", "displayName": "O3-Pro", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "openai/o3-2025-04-16", "displayName": "O3 (2025-04-16)", "costCategory": "REASONING", "priority": 2}, {"canonicalName": "openai/o3-mini", "displayName": "OpenAI O3 Mini", "costCategory": "REASONING", "priority": 2}, {"canonicalName": "openai/o4-mini", "displayName": "OpenAI O4 Mini", "costCategory": "REASONING", "priority": 2}, {"canonicalName": "openai/o4-mini-2025-04-16", "displayName": "O4 Mini (2025-04-16)", "costCategory": "REASONING", "priority": 2}, {"canonicalName": "openai/chatgpt-4o-latest", "displayName": "ChatGPT 4o Latest", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/codex-mini-latest", "displayName": "Codex Mini", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "openai/dall-e-2", "displayName": "DALL-E 2", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/dall-e-3", "displayName": "DALL-E 3", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-3.5-turbo", "displayName": "GPT-3.5 Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-3.5-turbo-0125", "displayName": "GPT-3.5 Turbo 0125", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-3.5-turbo-16k", "displayName": "GPT-3.5 Turbo 16K", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-3.5-turbo-instruct", "displayName": "GPT-3.5 Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4", "displayName": "GPT-4", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4-0125-preview", "displayName": "GPT-4 0125 Preview", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4.1", "displayName": "GPT-4.1", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4.1-2025-04-14", "displayName": "GPT-4.1 (2025-04-14)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4.1-mini", "displayName": "GPT-4.1 Mini", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4.1-mini-2025-04-14", "displayName": "GPT-4.1 Mini (2025-04-14)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4.1-nano", "displayName": "GPT-4.1 Nano", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4.1-nano-2025-04-14", "displayName": "GPT-4.1 Nano (2025-04-14)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4o", "displayName": "GPT-4o", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "openai/gpt-4o-2024-05-13", "displayName": "GPT-4o (May 2024)", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "openai/gpt-4o-2024-08-06", "displayName": "GPT-4o (August 2024)", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "openai/gpt-4o-2024-11-20", "displayName": "GPT-4o (November 2024)", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "openai/gpt-4o-mini", "displayName": "GPT-4o Mini", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4o-mini-2024-07-18", "displayName": "GPT-4o Mini July 2024", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4o-mini-search-preview", "displayName": "GPT-4o Mini Search", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-4o-search-preview", "displayName": "GPT-4o Search Preview", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/gpt-image-1", "displayName": "GPT Image 1", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/o1-mini", "displayName": "O1 Mini", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "openai/o3", "displayName": "O3", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/o3-deep-research", "displayName": "O3 Deep Research", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openai/o4-mini-deep-research", "displayName": "O4 Mini Deep Research", "costCategory": "STANDARD", "priority": 3}]}, "Google": {"count": 14, "models": [{"canonicalName": "gemini/gemini-2.5-pro", "displayName": "Gemini 2.5 Pro", "costCategory": "FRONTIER", "priority": 1}, {"canonicalName": "gemini/gemini-1.5-flash", "displayName": "Gemini 1.5 Flash", "costCategory": "BUDGET", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-flash-002", "displayName": "Gemini 1.5 Flash", "costCategory": "BUDGET", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-flash-8b", "displayName": "Gemini 1.5 Flash 8B", "costCategory": "BUDGET", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-flash-8b-latest", "displayName": "Gemini 1.5 Flash 8B Latest", "costCategory": "BUDGET", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-flash-latest", "displayName": "Gemini 1.5 Flash (Latest)", "costCategory": "BUDGET", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-pro", "displayName": "Gemini 1.5 Pro", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-pro-002", "displayName": "Gemini 1.5 Pro v002", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "gemini/gemini-1.5-pro-latest", "displayName": "Gemini 1.5 Pro (Latest)", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "gemini/gemini-2.0-flash", "displayName": "Gemini 2.0 Flash", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "gemini/gemini-2.0-flash-exp", "displayName": "Gemini 2.0 Flash Experimental", "costCategory": "FREE", "priority": 3}, {"canonicalName": "gemini/gemini-2.0-flash-lite", "displayName": "Gemini 2.0 Flash Lite", "costCategory": "BUDGET", "priority": 3}, {"canonicalName": "gemini/gemini-2.0-flash-thinking-exp", "displayName": "Gemini 2.0 Flash Thinking Exp", "costCategory": "FREE", "priority": 3}, {"canonicalName": "gemini/gemini-2.5-flash", "displayName": "Gemini 2.5 Flash", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "gemini/gemini-2.5-flash-lite-preview-06-17", "displayName": "Gemini 2.5 Flash Lite", "costCategory": "FREE", "priority": 3}]}, "xAI": {"count": 9, "models": [{"canonicalName": "openrouter/x-ai/grok-3-mini", "displayName": "Grok 3 Mini", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "xai/grok-2-1212", "displayName": "Grok 2 (1212)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "xai/grok-2-image-1212", "displayName": "Grok 2 Image", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "xai/grok-2-vision-1212", "displayName": "Grok 2 Vision", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "xai/grok-3", "displayName": "Grok 3 Fast", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "xai/grok-3-fast", "displayName": "Grok 3", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "xai/grok-3-mini", "displayName": "Grok 3 Mini", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "xai/grok-3-mini-fast", "displayName": "Grok 3 Mini", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "xai/grok-beta", "displayName": "Grok Beta", "costCategory": "STANDARD", "priority": 3}]}, "Cohere": {"count": 11, "models": [{"canonicalName": "cohere/c4ai-aya-expanse-32b", "displayName": "Aya Expanse 32B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/c4ai-aya-vision-32b", "displayName": "Aya Vision 32B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command", "displayName": "Command", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-a-03-2025", "displayName": "Command A (March 2025)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-r", "displayName": "Command R", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-r-08-2024", "displayName": "Command R (August 2024)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-r-plus", "displayName": "Command R+", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-r-plus-08-2024", "displayName": "Command R Plus 08 2024", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-r7b-12-2024", "displayName": "Command R 7B (December 2024)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "cohere/command-r7b-arabic-02-2025", "displayName": "Command R 7B Arabic", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/cohere/command-r-08-2024", "displayName": "Command R 08-2024", "costCategory": "STANDARD", "priority": 3}]}, "DeepSeek": {"count": 4, "models": [{"canonicalName": "deepseek/deepseek-chat", "displayName": "DeepSeek Chat (V3)", "costCategory": "FREE", "priority": 3}, {"canonicalName": "deepseek/deepseek-reasoner", "displayName": "DeepSeek Reasoner (R1)", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/deepseek/deepseek-r1-0528", "displayName": "DeepSeek R1 0528", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/deepseek/deepseek-r1-distill-qwen-7b", "displayName": "DeepSeek R1 Distill 7B", "costCategory": "STANDARD", "priority": 3}]}, "Mistral AI": {"count": 12, "models": [{"canonicalName": "mistral/codestral-latest", "displayName": "Codestral", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "mistral/devstral-small-2505", "displayName": "Devstral Small", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "mistral/devstral-small-latest", "displayName": "Devstral Small Latest", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "mistral/magistral-medium-2506", "displayName": "Magistral Medium 2506", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "mistral/magistral-small-2506", "displayName": "Magistral Small 2506", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "mistral/mistral-large-2411", "displayName": "Mistral Large 2411", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "mistral/mistral-medium-2312", "displayName": "Mistral Medium", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "mistral/pixtral-large-2411", "displayName": "Pixtral Large", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/mistralai/ministral-3b", "displayName": "Ministral 3B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/mistralai/ministral-8b", "displayName": "Ministral 8B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/mistralai/mistral-nemo", "displayName": "Mi<PERSON><PERSON> Nemo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/mistralai/mistral-small-3.2-24b-instruct", "displayName": "Mistral Small 3.2 24B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/mistral/Mixtral-8x7B-Instruct-v0.1", "displayName": "Mixtral 8x7B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/mistral/Mixtral-8x7B-v0.1", "displayName": "Mixtral 8x7B", "costCategory": "STANDARD", "priority": 3}]}, "Meta": {"count": 22, "models": [{"canonicalName": "openrouter/meta-llama/llama-3.2-3b-instruct", "displayName": "Llama 3.2 3B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/meta-llama/llama-3.3-70b-instruct", "displayName": "Llama 3.3 70B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/meta-llama/llama-4-maverick", "displayName": "Llama 4 Maverick", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-2-70b-hf", "displayName": "Llama 2 70B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-3-70b-chat-hf", "displayName": "Llama 3 70B Chat", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-3-8b-chat-hf", "displayName": "Llama 3 8B Chat", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo", "displayName": "Llama 3.2 11B Vision", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-3.2-3B-Instruct-Turbo", "displayName": "Llama 3.2 3B Instruct Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo", "displayName": "Llama 3.2 90B Vision", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-3.3-70B-Instruct-Turbo", "displayName": "Llama 3.3 70B Instruct Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8", "displayName": "Llama 4 Maverick 17B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-4-Scout-17B-16E-Instruct", "displayName": "Llama 4 Scout 17B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-Guard-3-11B-Vision-Turbo", "displayName": "Llama Guard 3 11B Vision", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-Guard-4-12B", "displayName": "Llama Guard 4 12B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Llama-Vision-Free", "displayName": "Llama Vision", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Meta-Llama-3-70B-Instruct-Turbo", "displayName": "Llama 3 70B Instruct Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Meta-Llama-3-8B-Instruct-Lite", "displayName": "Llama 3 8B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo", "displayName": "Llama 3.1 405B Instruct Turbo", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo", "displayName": "Llama 3.1 70B Instruct", "costCategory": "STANDARD", "priority": 3}]}, "Groq": {"count": 4, "models": [{"canonicalName": "groq/gemma2-9b-it", "displayName": "Gemma2 9b It", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "groq/llama-3.3-70b-versatile", "displayName": "Llama 3.3 70b Versatile R1", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "groq/llama3-70b-8192", "displayName": "Llama3 70b 8192", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "groq/llama3-8b-8192", "displayName": "Llama 3 8B", "costCategory": "STANDARD", "priority": 3}]}, "Perplexity AI": {"count": 5, "models": [{"canonicalName": "perplexity/sonar", "displayName": "Sonar", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "perplexity/sonar-deep-research", "displayName": "Sonar Deep Research", "costCategory": "PREMIUM", "priority": 3}, {"canonicalName": "perplexity/sonar-pro", "displayName": "Sonar Pro", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "perplexity/sonar-reasoning", "displayName": "Sonar Reasoning", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/perplexity-ai/r1-1776", "displayName": "R1 1776", "costCategory": "STANDARD", "priority": 3}]}, "Others": {"count": 41, "models": [{"canonicalName": "together_ai/arcee-ai/AFM-4.5B-Preview", "displayName": "AFM 4.5B Preview", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/arcee-blitz", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/arcee-spotlight", "displayName": "<PERSON><PERSON>light", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/caller", "displayName": "<PERSON><PERSON>", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/coder-large", "displayName": "Arcee Coder Large", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/maestro-reasoning", "displayName": "<PERSON><PERSON> Maestro Reasoning", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/virtuoso-large", "displayName": "Virtuoso Large", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/arcee-ai/virtuoso-medium-v2", "displayName": "Virtuoso Medium v2", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/eva-unit-01/eva-llama-3.33-70b", "displayName": "EVA Llama 3.33 70B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/eva-unit-01/eva-qwen-2.5-32b", "displayName": "EVA Qwen 2.5 32B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/inflection/inflection-3-pi", "displayName": "Pi", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/inflection/inflection-3-productivity", "displayName": "Inflection 3 Productivity", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/lgai/exaone-3-5-32b-instruct", "displayName": "EXAONE 3.5 32B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/lgai/exaone-deep-32b", "displayName": "EXAONE Deep 32B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/neversleep/llama-3-lumimaid-8b", "displayName": "Lumimaid 8B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/neversleep/llama-3.1-lumimaid-70b", "displayName": "Lumimaid 70B v0.2", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/nvidia/llama-3.1-nemotron-70b-instruct", "displayName": "Nemotron 70B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/nvidia/llama-3.1-nemotron-ultra-253b-v1", "displayName": "Nemotron Ultra 253B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/nvidia/llama-3.3-nemotron-super-49b-v1", "displayName": "Nemotron Super 49B", "costCategory": "FREE", "priority": 3}, {"canonicalName": "together_ai/nvidia/Llama-3.1-Nemotron-70B-Instruct-HF", "displayName": "Nemotron 70B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/01-ai/yi-large", "displayName": "Yi Large", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/ai21/jamba-1.6-large", "displayName": "Jamba 1.6 Large", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/ai21/jamba-1.6-mini", "displayName": "Jamba 1.6 Mini", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/alpindale/goliath-120b", "displayName": "Goliath 120B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/anthracite-org/magnum-v4-72b", "displayName": "Magnum v4 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/microsoft/phi-4-reasoning-plus", "displayName": "Phi-4 Reasoning Plus", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/minimax/minimax-m1", "displayName": "Minimax M1", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/moonshotai/kimi-dev-72b:free", "displayName": "<PERSON><PERSON> 72B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/pygmalionai/mythalion-13b", "displayName": "Mythalion 13B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/rekaai/reka-flash-3:free", "displayName": "Reka Flash 3", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/sao10k/fimbulvetr-11b-v2", "displayName": "Fimbulvetr 11B v2", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "openrouter/undi95/toppy-m-7b", "displayName": "Toppy M 7B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/marin-community/marin-8b-instruct", "displayName": "Marin 8B", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/salesforce/Llama-Rank-v1", "displayName": "Llama Rank v1", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/scb10x/scb10x-llama3-1-typhoon2-70b-instruct", "displayName": "Typhoon 2 70B Instruct", "costCategory": "STANDARD", "priority": 3}, {"canonicalName": "together_ai/scb10x/scb10x-typhoon-2-1-gemma3-12b", "displayName": "Typhoon 2.1 Gemma3 12B", "costCategory": "STANDARD", "priority": 3}]}}}