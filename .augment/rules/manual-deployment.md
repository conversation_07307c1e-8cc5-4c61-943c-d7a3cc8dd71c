---
type: "agent_requested"
description: "Example description"
---
# Deployment Procedures

## Critical Deployment Rules
Reference the comprehensive deployment workflow in CLAUDE.md files for detailed procedures.

### Pre-Deployment Checklist (ALWAYS execute)
1. **TypeScript validation**: `npm run typecheck` or `npx tsc --noEmit`
2. **Linting**: `npm run lint`
3. **Build verification**: `npm run build`
4. **Local testing**: `npm run dev` and test changed features
5. **Console error check**: Verify no errors in browser DevTools
6. **Security check**: Ensure no sensitive data is logged or exposed

**Only proceed with deployment if <PERSON><PERSON> checks pass!**

### v3.2 Deployment Workflow
**NEVER EDIT ON MAIN BRANCH** - Always work on develop or feature branches

```bash
cd /home/<USER>/deployments/dev/simplechat-ai

# ALWAYS check branch first
git branch --show-current

# Development work
git checkout develop
git pull origin develop
# Make changes, then:
git add . && git commit -m "feat: description" && git push

# Staging deployment (test first!)
export BASH_DEFAULT_TIMEOUT_MS=1200000  # CRITICAL: Set timeout
./deploy-staging-v3.sh

# Production deployment (after staging success)
git checkout main
git merge develop
git push origin main
export BASH_DEFAULT_TIMEOUT_MS=1200000
./deploy-production-v3.sh
```

### Environment Details
- **Development**: Branch `develop`, Port 3005, Database `justsimplechat_development`
- **Production**: Branch `main`, Port 3006, Database `justsimplechat_production`
- **PM2 Logs**: Always use `--nostream` flag

### Deployment Script Features
- Zero-downtime deployment using PM2 cluster mode
- Automatic rollback on failure
- Build validation (lint, typecheck, tests)
- Comprehensive health checks
- Backup creation before production deployments

### Common Issues & Solutions
1. **Timeout errors**: Always set `export BASH_DEFAULT_TIMEOUT_MS=1200000`
2. **Branch errors**: Check with `git branch --show-current`
3. **Uncommitted changes**: Commit first with `git add . && git commit -m "fix: ..."`
4. **TypeScript errors**: Fix with `npm run typecheck`
5. **ESLint errors**: Fix with `npm run lint`

## Reference Documentation
For detailed procedures, see:
- `~/deployments/dev/simplechat-ai/CLAUDE.md` - Implementation details
- `~/.claude/CLAUDE.md` - Personal workflow preferences
- `~/deployments/CLAUDE.md` - Application stack overview
- `/DOCS/README.md` - Comprehensive technical documentation
