---
type: "agent_requested"
description: "Example description"
---
# Core JustSimpleChat Patterns

## React/Next.js Patterns
- Use Next.js App Router with Server Components by default
- Implement proper loading states with Suspense boundaries
- Use `use server` for server actions, `use client` only when necessary
- Follow the component structure: Server Component → Client Component → UI Components
- Prefer React 19 Actions over useEffect for data mutations
- Use useOptimistic for optimistic updates in forms and interactions
- Implement proper error boundaries for component error handling

## Database Patterns
- Use database-first architecture with `provider/model` format (e.g., `openai/gpt-4o`)
- All database operations should use proper error handling and transactions
- Model names must follow consistent prefixes: `openai/`, `anthropic/`, `gemini/`, `xai/`, `together_ai/`, `openrouter/`, `alibaba/`
- Database canonicalName maps directly to provider SDKs - no transformation needed
- Use MySQL with `127.0.0.1` as host for Docker container connections

## API Route Patterns
- Always validate request bodies with Zod schemas
- Return consistent error responses with proper HTTP status codes
- Include proper CORS headers for cross-origin requests
- Use middleware for authentication and rate limiting
- Implement proper logging without exposing sensitive data
- Use proper HTTP methods: GET for reads, POST for creates, PUT for updates, DELETE for removes

## Security Standards
- Never log sensitive data (API keys, user tokens, passwords)
- Validate all user inputs on both client and server
- Use environment variables for all configuration
- Implement proper session management
- Use prepared statements to prevent SQL injection
- Include proper error handling that doesn't expose internal details

## Code Quality Standards
- Use TypeScript strictly - no `any` types without explicit justification
- Include JSDoc comments for complex functions and components
- Follow consistent naming conventions: PascalCase for components, camelCase for functions
- Implement proper prop validation and default values
- Use forwardRef for components that need ref forwarding
- Include comprehensive error handling with user-friendly messages

## Styling and UI Standards
- Use Tailwind CSS for styling with consistent design tokens
- Implement responsive design with mobile-first approach
- Include hover, focus, and active states for interactive elements
- Use semantic HTML elements (button, nav, main, section)
- Prioritize accessibility (ARIA labels, semantic HTML, keyboard navigation)
- Implement proper loading and error states for async operations
