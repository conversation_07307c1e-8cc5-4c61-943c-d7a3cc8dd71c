---
type: "agent_requested"
description: "Example description"
---
description: "Testing and quality assurance patterns"

# Testing Standards

## When writing or modifying code:

### Unit Testing Patterns
- Write unit tests for utility functions and business logic
- Test pure functions with various input scenarios
- Mock external dependencies and API calls
- Use meaningful test descriptions that explain expected behavior
- Follow the Arrange-Act-Assert (AAA) pattern
- Test both happy path and error scenarios
- Aim for high code coverage but focus on critical paths

### Integration Testing
- Include integration tests for API endpoints
- Test database operations with proper setup and teardown
- Test authentication and authorization flows
- Verify proper error handling and response formats
- Test middleware functionality and request/response cycles
- Use test databases separate from development/production

### Component Testing
- Test React components with React Testing Library
- Focus on user interactions and behavior rather than implementation
- Test accessibility features and keyboard navigation
- Mock external dependencies and API calls
- Test different component states (loading, error, success)
- Use data-testid attributes for reliable element selection

### End-to-End Testing
- Test critical user journeys and workflows
- Verify cross-browser compatibility
- Test responsive design on different screen sizes
- Validate form submissions and data persistence
- Test authentication flows and protected routes
- Use realistic test data and scenarios

## Quality Assurance Standards

### Pre-Deployment Checklist
Before any deployment, ALWAYS execute:

1. **TypeScript validation**: `npm run typecheck` or `npx tsc --noEmit`
2. **Linting**: `npm run lint` - fix all errors and warnings
3. **Build verification**: `npm run build` - ensure clean build
4. **Test execution**: `npm run test` - all tests must pass
5. **Local testing**: `npm run dev` and manually test changed features
6. **Console error check**: Verify no errors in browser DevTools
7. **Security check**: Ensure no sensitive data is logged or exposed

### Code Quality Standards
- Use TypeScript strictly - no `any` types without explicit justification
- Follow consistent code formatting with Prettier
- Use ESLint rules and fix all warnings
- Implement proper error handling with try-catch blocks
- Include JSDoc comments for complex functions
- Use meaningful variable and function names
- Keep functions small and focused on single responsibility

### Performance Testing
- Test application performance under load
- Monitor memory usage and potential leaks
- Test database query performance
- Verify proper caching mechanisms
- Test image and asset loading times
- Monitor bundle size and optimize when necessary

### Security Testing
- Test input validation and sanitization
- Verify authentication and authorization
- Test for common vulnerabilities (XSS, CSRF, SQL injection)
- Validate secure cookie settings
- Test API rate limiting and throttling
- Verify proper error handling without information disclosure

## Testing Environment Setup

### Test Configuration
- Use separate test databases and environments
- Mock external services and APIs
- Use environment variables for test configuration
- Implement proper test data seeding and cleanup
- Use consistent test naming conventions
- Organize tests in logical directory structure

### Continuous Integration
- Run tests automatically on pull requests
- Fail builds on test failures or linting errors
- Generate test coverage reports
- Run security scans and dependency checks
- Validate build process in CI environment
- Use proper test parallelization for faster execution

### Debugging and Troubleshooting
- Use proper debugging tools and techniques
- Log test failures with sufficient context
- Use test reporters for clear failure messages
- Implement proper test isolation to avoid flaky tests
- Use snapshot testing judiciously for UI components
- Keep tests maintainable and easy to understand
