---
type: "agent_requested"
description: "Example description"
---
description: "React component development and UI patterns"

# Component Development Patterns

## When working with React components:

### Component Structure and TypeScript
- Use TypeScript interfaces for all props with proper documentation
- Implement proper prop validation and default values
- Use forwardRef for components that need ref forwarding
- Follow naming convention: PascalCase for components, camelCase for functions
- Export components as both named and default exports when appropriate

### React 19 Patterns
- Prefer Server Components by default, use `use client` only when necessary
- Use React Actions for form submissions and data mutations instead of useEffect
- Implement useOptimistic for optimistic updates in forms and interactions
- Use useActionState for managing form state and pending states
- Leverage Suspense boundaries for loading states and error boundaries for errors

### Component Architecture
- Follow the pattern: Server Component → Client Component → UI Components
- Keep Server Components for data fetching and initial rendering
- Use Client Components for interactivity, state management, and browser APIs
- Create reusable UI components that are pure and testable
- Implement proper component composition with children and render props

### State Management
- Use useState for local component state
- Use useReducer for complex state logic
- Implement useOptimistic for immediate UI feedback
- Use Server Actions for server state mutations
- Avoid useEffect for data fetching - prefer Server Components or React Query

### Error Handling and Loading States
- Include loading and error states for all async operations
- Implement proper error boundaries for component error handling
- Use Suspense for loading states with meaningful fallbacks
- Provide user-friendly error messages with recovery options
- Handle edge cases and empty states gracefully

## UI Component Standards

### Styling and Design
- Use Tailwind CSS for styling with consistent design tokens
- Implement responsive design with mobile-first approach
- Follow consistent spacing, typography, and color schemes
- Use CSS Grid and Flexbox for layouts
- Implement dark mode support where applicable

### Accessibility and Interaction
- Use semantic HTML elements (button, nav, main, section, article)
- Include proper ARIA labels, roles, and descriptions
- Implement keyboard navigation and focus management
- Include hover, focus, and active states for interactive elements
- Ensure proper color contrast and text sizing
- Support screen readers with meaningful alt text and labels

### Performance Optimization
- Use React.memo for expensive components
- Implement proper key props for list items
- Lazy load components and images when appropriate
- Optimize bundle size with dynamic imports
- Use proper image optimization with Next.js Image component

### Testing Considerations
- Write components that are easily testable
- Use data-testid attributes for testing selectors
- Keep components pure and avoid side effects where possible
- Mock external dependencies and API calls in tests
- Test both happy path and error scenarios
