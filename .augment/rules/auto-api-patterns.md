---
type: "agent_requested"
description: "Example description"
---
description: "API route development and database operations"

# API Development Patterns

## When working with API routes:

### Request Handling and Validation
- Use proper HTTP methods: GET for reads, POST for creates, PUT for updates, DELETE for removes
- Implement request validation with Zod schemas for all input data
- Validate query parameters, request bodies, and headers
- Return consistent JSON responses with proper error handling
- Include proper CORS headers for cross-origin requests
- Implement rate limiting and request throttling where appropriate

### Response Standards
- Use consistent response format: `{ data, error, message, status }`
- Return proper HTTP status codes (200, 201, 400, 401, 403, 404, 500)
- Include meaningful error messages without exposing internal details
- Implement proper pagination for list endpoints
- Use proper content-type headers (application/json)
- Include relevant metadata in responses (timestamps, counts, etc.)

### Authentication and Authorization
- Use middleware for authentication and authorization checks
- Implement proper session management and token validation
- Check user permissions before allowing operations
- Use secure cookie settings for session data
- Implement proper logout and session cleanup
- Handle expired tokens and refresh token logic

### Logging and Monitoring
- Include proper logging for debugging without sensitive data
- Log request/response times and error rates
- Use structured logging with consistent format
- Implement health check endpoints for monitoring
- Track API usage and performance metrics
- Set up alerts for error rates and response times

## Database Operations

### Connection and Transaction Management
- Use proper connection pooling and cleanup
- Implement transactions for multi-step operations
- Handle connection timeouts and retries
- Use prepared statements to prevent SQL injection
- Close connections and clean up resources properly
- Implement proper error handling for database failures

### Model and Schema Patterns
- Follow database-first model architecture
- Use consistent naming conventions for tables and columns
- Implement proper foreign key relationships
- Use database migrations for schema changes
- Follow the `provider/model` format (e.g., `openai/gpt-4o`)
- Model names must follow consistent prefixes: `openai/`, `anthropic/`, `gemini/`, `xai/`

### Query Optimization
- Use indexes for frequently queried columns
- Implement proper query optimization and explain plans
- Use pagination for large result sets
- Implement caching for frequently accessed data
- Use database views for complex queries
- Monitor query performance and optimize slow queries

### Data Validation and Security
- Validate all data before database operations
- Use parameterized queries to prevent SQL injection
- Implement proper data sanitization
- Use database constraints for data integrity
- Implement soft deletes where appropriate
- Audit sensitive data changes with proper logging

## Error Handling and Resilience

### Error Response Patterns
- Implement consistent error response format
- Use proper error codes and messages
- Handle different types of errors appropriately (validation, authorization, server)
- Implement proper error logging without exposing sensitive information
- Use error boundaries and fallback mechanisms
- Provide helpful error messages for debugging

### Resilience and Recovery
- Implement retry logic for transient failures
- Use circuit breakers for external service calls
- Implement proper timeout handling
- Use graceful degradation when services are unavailable
- Implement proper backup and recovery procedures
- Monitor service health and implement alerting
