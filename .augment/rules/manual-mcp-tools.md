---
type: "agent_requested"
description: "Example description"
---
# MCP Tool Usage Guidelines

## When Research or Documentation is Needed

Always use MCP (Model Context Protocol) tools for latest documentation instead of relying on training data.

### Essential MCP Servers
```bash
# Set timeout first
export BASH_DEFAULT_TIMEOUT_MS=600000

# Install core servers
claude mcp add perplexity-ask --scope user -e PERPLEXITY_API_KEY=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 -- npx -y perplexity-mcp
claude mcp add firecrawl --scope user -e FIRECRAWL_API_KEY=fc-9fcee76d691b4452b4fbccc283a8e158 -- npx -y firecrawl-mcp
claude mcp add context7 --scope user -- npx -y @upstash/context7-mcp@latest
claude mcp add sequential-thinking --scope user -- npx -y @modelcontextprotocol/server-sequential-thinking
```

### Tool-Specific Usage

#### Context7 (Library Documentation)
- **Purpose**: Access up-to-date documentation for libraries and frameworks
- **Best for**: React 19, Next.js 15, TypeScript, library APIs
- **Key Functions**:
  - `resolve-library-id`: Find Context7-compatible library ID from name
  - `get-library-docs`: Fetch current documentation for libraries
- **Usage**: Always use `resolve-library-id` first unless exact ID is provided
- **Library Format**: IDs like `/mongodb/docs`, `/vercel/next.js`, `/supabase/supabase`

#### Firecrawl (Web Scraping & Content Extraction)
- **Purpose**: Advanced web scraping, crawling, and content extraction
- **Best for**: Website content extraction, competitive analysis, research
- **Key Functions**:
  - `firecrawl_scrape`: Single page content extraction (best for known URLs)
  - `firecrawl_map`: Discover all URLs on a website
  - `firecrawl_crawl`: Extract content from multiple pages (async job)
  - `firecrawl_search`: Web search with optional content scraping
  - `firecrawl_extract`: Structured data extraction using LLM
  - `firecrawl_deep_research`: Complex research across multiple sources

#### Perplexity (AI Search & Research)
- **Purpose**: AI-powered search and research capabilities
- **Best for**: Current events, latest information, research questions
- **Key Functions**:
  - `search`: Quick search for simple queries (Sonar Pro model)
  - `reason`: Complex, multi-step reasoning tasks (Sonar Reasoning Pro)
  - `deep_research`: In-depth analysis with detailed reports (Sonar Deep Research)
- **Usage**: Supports conversation context and can access real-time web data

#### Sequential Thinking (Complex Problem Solving)
- **Purpose**: Dynamic and reflective problem-solving through structured thoughts
- **Best for**: Breaking down complex problems, planning, analysis
- **Usage**: Use for multi-step solutions, design decisions, debugging complex issues

### MCP Usage Rules
1. **Always use MCP tools** for latest documentation - don't rely on outdated knowledge
2. **Check capabilities** before assuming tool functionality
3. **Verify before implementing** - always check latest docs before coding
4. **Context7 for library docs** - essential for React 19, Next.js 15, etc.
5. **Perplexity for research** - when you need current, accurate information
6. **Firecrawl for web content** - use appropriate function for your needs

### Troubleshooting MCP Tools
- **Tools missing**: Reinstall using installation commands above
- **Timeout issues**: Set `export BASH_DEFAULT_TIMEOUT_MS=600000`
- **Check availability**: Use `/mcp` command in Claude session
- **Verify installation**: `claude mcp list`
