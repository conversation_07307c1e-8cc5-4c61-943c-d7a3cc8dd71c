const fs = require('fs');

async function testPDFProcessing() {
  try {
    console.log('🔍 Testing PDF processing with pdf2json...');
    
    // Import pdf2json
    const PDFParser = (await import('pdf2json')).default;
    
    const pdfPath = '/home/<USER>/Royal Mail_End_of_Day_Form_July 17 2025 04_05 PM UTC.pdf';
    
    console.log('📁 PDF file exists:', fs.existsSync(pdfPath));
    console.log('📊 PDF file size:', fs.statSync(pdfPath).size, 'bytes');
    
    const buffer = fs.readFileSync(pdfPath);
    console.log('📖 <PERSON>uffer created, length:', buffer.length);
    
    return new Promise((resolve, reject) => {
      const pdfParser = new PDFParser();
      
      pdfParser.on('pdfParser_dataError', (errData) => {
        console.error('❌ PDF parsing error:', errData);
        resolve('PDF parsing failed: ' + JSON.stringify(errData));
      });
      
      pdfParser.on('pdfParser_dataReady', (pdfData) => {
        try {
          console.log('✅ PDF parsed successfully');
          console.log('📄 Pages found:', pdfData.Pages?.length || 0);
          
          // Extract text from all pages
          let fullText = '';
          if (pdfData.Pages) {
            for (const page of pdfData.Pages) {
              if (page.Texts) {
                for (const text of page.Texts) {
                  if (text.R) {
                    for (const run of text.R) {
                      if (run.T) {
                        fullText += decodeURIComponent(run.T) + ' ';
                      }
                    }
                  }
                }
                fullText += '\n';
              }
            }
          }
          
          console.log('📝 Extracted text length:', fullText.trim().length);
          console.log('📝 First 200 chars:', fullText.substring(0, 200));
          
          resolve(fullText.trim());
        } catch (extractError) {
          console.error('❌ Text extraction error:', extractError);
          resolve('Text extraction failed: ' + extractError.message);
        }
      });
      
      // Parse the PDF buffer
      console.log('🚀 Starting PDF parsing...');
      pdfParser.parseBuffer(buffer);
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return 'Test failed: ' + error.message;
  }
}

testPDFProcessing().then(result => {
  console.log('🎯 Final result:', result);
}).catch(err => {
  console.error('💥 Unhandled error:', err);
});