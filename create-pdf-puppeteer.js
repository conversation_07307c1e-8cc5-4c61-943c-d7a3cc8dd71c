const puppeteer = require('puppeteer');
const fs = require('fs');

async function createTestPDF() {
  try {
    console.log('🚀 Starting PDF creation...');
    
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    const htmlContent = `
    <html>
      <head>
        <title>Test PDF Document</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1 { color: #333; }
          p { line-height: 1.6; margin-bottom: 16px; }
        </style>
      </head>
      <body>
        <h1>Test PDF Document</h1>
        <p>This is a test PDF document created for attachment processing functionality.</p>
        <p>It contains multiple paragraphs and should be properly extracted by the pdf2json library.</p>
        <p>The system should be able to read this content and provide it to the AI model for analysis.</p>
        <p>Testing PDF processing capabilities with real PDF content extraction.</p>
      </body>
    </html>
    `;
    
    await page.setContent(htmlContent);
    
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20px',
        right: '20px',
        bottom: '20px',
        left: '20px'
      }
    });
    
    fs.writeFileSync('/tmp/test-document.pdf', pdfBuffer);
    
    await browser.close();
    
    const stats = fs.statSync('/tmp/test-document.pdf');
    console.log('✅ Created test PDF successfully:', stats.size, 'bytes');
    
  } catch (error) {
    console.error('❌ Failed to create PDF:', error.message);
  }
}

createTestPDF();