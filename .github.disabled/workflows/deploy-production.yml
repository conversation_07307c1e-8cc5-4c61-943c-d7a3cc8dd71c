name: Deploy to Production

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      skip_approval:
        description: 'Skip manual approval (emergency deployments only)'
        required: false
        default: 'false'
        type: boolean

env:
  NODE_VERSION: '22.13.0'

jobs:
  test:
    name: Test & Build
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Run type checking
        run: npm run typecheck

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: ${{ secrets.PROD_DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.PROD_NEXTAUTH_SECRET }}

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_PUBLIC_APP_URL: https://justsimple.chat
          # Database
          DATABASE_URL: ${{ secrets.PROD_DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.PROD_NEXTAUTH_SECRET }}
          NEXTAUTH_URL: https://justsimple.chat
          # AI Provider Keys
          OPENAI_API_KEY: ********************************************************************************************************************************************************************
          ANTHROPIC_API_KEY: ************************************************************************************************************
          GOOGLE_API_KEY: AIzaSyBl-dgrfGvI-Z2OL4-iKlNZKSBNUdFqMt8
          GROQ_API_KEY: ********************************************************
          TOGETHER_API_KEY: 8e5bffca039419cf9ba30ebb965dc57e973ebe6f0e8a96797b19e8c0c0e983e4
          XAI_API_KEY: ************************************************************************************
          DEEPSEEK_API_KEY: ***********************************
          MISTRAL_API_KEY: UyX0HmPQi6JTeIGcWLkDZ8q4n1VXnRi7
          PERPLEXITY_API_KEY: 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
          COHERE_API_KEY: BnKBNLVsmFKzn6h2q8mHQMy9oLILWudOGFmTUQvs
          OPENROUTER_API_KEY: 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
          # Bedrock
          USE_BEDROCK_ROUTER: true
          AWS_REGION: us-east-1
          BEDROCK_ROUTER_MODEL: amazon.nova-lite-v1:0
          # Email
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY || '' }}
          # Redis
          REDIS_URL: redis://localhost:6379
          # Stripe
          STRIPE_SECRET_KEY: sk_test_dummy
          STRIPE_WEBHOOK_SECRET: whsec_dummy
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_dummy

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            .next
            public
          retention-days: 7

  approval:
    name: Manual Approval
    runs-on: ubuntu-latest
    needs: test
    environment: production-approval
    if: github.event.inputs.skip_approval != 'true'
    
    steps:
      - name: Request deployment approval
        run: echo "⏸️ Waiting for manual approval..."

  deploy:
    name: Deploy to justsimple.chat
    runs-on: ubuntu-latest
    needs: [test, approval]
    if: always() && needs.test.result == 'success' && (needs.approval.result == 'success' || needs.approval.result == 'skipped')
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOYMENT_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.PROD_SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Create rollback point
        run: |
          ssh -i ~/.ssh/deploy_key ${{ secrets.PROD_SERVER_USER }}@${{ secrets.PROD_SERVER_HOST }} << 'EOF'
            set -e
            cd /home/<USER>/deployments/production/simplechat-ai
            
            # Create backup tag
            BACKUP_TAG="rollback-$(date +%Y%m%d-%H%M%S)"
            git tag -f $BACKUP_TAG
            
            # Save PM2 state
            pm2 save
            
            # Backup database schema
            mysqldump -h 127.0.0.1 -u root -p${{ secrets.MYSQL_ROOT_PASSWORD }} \
              --no-data --routines --triggers \
              justsimplechat_production > /tmp/schema-backup-$(date +%Y%m%d-%H%M%S).sql
          EOF

      - name: Deploy to production
        run: |
          ssh -i ~/.ssh/deploy_key ${{ secrets.PROD_SERVER_USER }}@${{ secrets.PROD_SERVER_HOST }} << 'EOF'
            set -e
            
            # Navigate to production directory
            cd /home/<USER>/deployments/production/simplechat-ai
            
            # Pull latest changes
            git fetch origin main
            git reset --hard origin/main
            
            # Copy zero-downtime deployment script
            cp .github/scripts/zero-downtime-deploy.sh /tmp/
            chmod +x /tmp/zero-downtime-deploy.sh
            
            # Execute zero-downtime deployment
            export DATABASE_URL="${{ secrets.PROD_DATABASE_URL }}"
            /tmp/zero-downtime-deploy.sh production
            
            # Verify critical endpoints
            curl -f https://justsimple.chat/api/models || exit 1
            curl -f https://justsimple.chat/api/auth/providers || exit 1
            
            # Cleanup
            rm -f /tmp/zero-downtime-deploy.sh
            
            echo "✅ Production deployment successful with zero downtime!"
          EOF

      - name: Run production smoke tests
        run: |
          # Test critical user flows
          npm run test:e2e:production || true

      - name: Create deployment record
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.ref,
              environment: 'production',
              description: 'Production deployment',
              production_environment: true,
              required_contexts: []
            });
            
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: deployment.data.id,
              state: 'success',
              environment_url: 'https://justsimple.chat'
            });

      - name: Notify deployment
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const status = '${{ job.status }}' === 'success' ? '✅ Success' : '❌ Failed';
            const sha = context.sha.substring(0, 7);
            const actor = context.actor;
            const message = `🚀 Production Deployment: ${status}\nCommit: ${sha}\nDeployed by: ${actor}\nEnvironment: https://justsimple.chat`;
            
            // Add Slack/Discord notification here
            console.log(message);