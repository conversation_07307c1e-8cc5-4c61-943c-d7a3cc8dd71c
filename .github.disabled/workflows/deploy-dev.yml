name: Deploy to Development

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  NODE_VERSION: '22.13.0'

jobs:
  test:
    name: Test & Type Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Run type checking
        run: npm run typecheck

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: ${{ secrets.DEV_DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.DEV_NEXTAUTH_SECRET }}
          
      - name: Build application
        run: npm run build
        env:
          NODE_ENV: development
          NEXT_PUBLIC_APP_URL: https://dev.justsimple.chat
          # Database
          DATABASE_URL: ${{ secrets.DEV_DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.DEV_NEXTAUTH_SECRET }}
          NEXTAUTH_URL: https://dev.justsimple.chat
          # AI Provider Keys
          OPENAI_API_KEY: ********************************************************************************************************************************************************************
          ANTHROPIC_API_KEY: ************************************************************************************************************
          GOOGLE_API_KEY: AIzaSyBl-dgrfGvI-Z2OL4-iKlNZKSBNUdFqMt8
          GROQ_API_KEY: ********************************************************
          TOGETHER_API_KEY: 8e5bffca039419cf9ba30ebb965dc57e973ebe6f0e8a96797b19e8c0c0e983e4
          XAI_API_KEY: ************************************************************************************
          DEEPSEEK_API_KEY: ***********************************
          MISTRAL_API_KEY: UyX0HmPQi6JTeIGcWLkDZ8q4n1VXnRi7
          PERPLEXITY_API_KEY: 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
          COHERE_API_KEY: BnKBNLVsmFKzn6h2q8mHQMy9oLILWudOGFmTUQvs
          OPENROUTER_API_KEY: 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
          # Bedrock
          USE_BEDROCK_ROUTER: true
          AWS_REGION: us-east-1
          BEDROCK_ROUTER_MODEL: amazon.nova-lite-v1:0
          # Email
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY || '' }}
          # Redis
          REDIS_URL: redis://localhost:6379
          # Stripe
          STRIPE_SECRET_KEY: sk_test_dummy
          STRIPE_WEBHOOK_SECRET: whsec_dummy
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_dummy

  deploy:
    name: Deploy to dev.justsimple.chat
    runs-on: ubuntu-latest
    needs: test
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOYMENT_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.DEV_SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          ssh -i ~/.ssh/deploy_key ${{ secrets.DEV_SERVER_USER }}@${{ secrets.DEV_SERVER_HOST }} << 'EOF'
            set -e
            
            # Navigate to dev directory
            cd /home/<USER>/deployments/dev/simplechat-ai
            
            # Pull latest changes
            git fetch origin develop
            git reset --hard origin/develop
            
            # Install dependencies
            npm ci --legacy-peer-deps
            
            # Run database migrations
            export DATABASE_URL="${{ secrets.DEV_DATABASE_URL }}"
            npm run db:generate || true
            npm run db:push || true
            
            # Build application
            npm run build
            
            # Reload PM2 process (zero downtime)
            pm2 reload simplechat-dev --update-env
            
            # Health check
            sleep 5
            curl -f http://localhost:3004/api/health || exit 1
            
            echo "✅ Deployment successful!"
          EOF

      - name: Notify deployment status
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const status = '${{ job.status }}' === 'success' ? '✅ Success' : '❌ Failed';
            const environment = 'Development (dev.justsimple.chat)';
            const sha = context.sha.substring(0, 7);
            const actor = context.actor;
            const message = `Deployment to ${environment}: ${status}\nCommit: ${sha}\nTriggered by: ${actor}`;
            
            // You can add Slack/Discord webhook here
            console.log(message);