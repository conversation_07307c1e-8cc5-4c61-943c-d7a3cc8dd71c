# GitHub Actions CI/CD Workflow Documentation

## Overview

This repository uses a three-tier deployment strategy with automated CI/CD pipelines:

- **develop** branch → deploys to `dev.justsimple.chat`
- **staging** branch → deploys to `staging.justsimple.chat`
- **main** branch → deploys to `justsimple.chat` (production)

## Workflow Architecture

```
Feature Branch
    ↓
develop (auto-deploy to dev)
    ↓
staging (auto-deploy to staging)
    ↓
main (manual approval → deploy to production)
```

## Workflows

### 1. Development Deployment (`deploy-dev.yml`)
- **Trigger**: Push to `develop` branch
- **Environment**: dev.justsimple.chat
- **Process**:
  1. Run tests and type checking
  2. Deploy to dev server
  3. Zero-downtime reload with PM2
  4. Health check verification

### 2. Staging Deployment (`deploy-staging.yml`)
- **Trigger**: Push to `staging` branch
- **Environment**: staging.justsimple.chat
- **Process**:
  1. Run comprehensive tests
  2. Build application
  3. Create backup point
  4. Deploy with rollback capability
  5. Run smoke tests
  6. Create deployment record

### 3. Production Deployment (`deploy-production.yml`)
- **Trigger**: Push to `main` branch
- **Environment**: justsimple.chat
- **Features**:
  - Manual approval required (can be skipped for emergencies)
  - Automatic rollback on failure
  - Database backup before migrations
  - Comprehensive health checks
  - Production smoke tests

### 4. Rollback Workflow (`rollback.yml`)
- **Trigger**: Manual workflow dispatch
- **Use Cases**:
  - Emergency rollbacks
  - Reverting failed deployments
  - Testing previous versions

## Required GitHub Secrets

### Server Access
- `DEPLOYMENT_SSH_KEY`: SSH private key for server access
- `DEV_SERVER_HOST`: Development server IP/hostname
- `DEV_SERVER_USER`: SSH user for dev server
- `STAGING_SERVER_HOST`: Staging server IP/hostname
- `STAGING_SERVER_USER`: SSH user for staging server
- `PROD_SERVER_HOST`: Production server IP/hostname
- `PROD_SERVER_USER`: SSH user for production server

### Database URLs
- `DEV_DATABASE_URL`: mysql://user:pass@host:3306/justsimplechat_dev
- `STAGING_DATABASE_URL`: mysql://user:pass@host:3306/justsimplechat_staging
- `PROD_DATABASE_URL`: mysql://user:pass@host:3306/justsimplechat_production

### Authentication Secrets
- `DEV_NEXTAUTH_SECRET`: Development NextAuth secret
- `STAGING_NEXTAUTH_SECRET`: Staging NextAuth secret
- `PROD_NEXTAUTH_SECRET`: Production NextAuth secret

### Additional Secrets
- `MYSQL_ROOT_PASSWORD`: For database backups

## Environment Configuration

### GitHub Environments
Create these environments in repository settings:

1. **development**
   - No protection rules
   - Auto-deploy enabled

2. **staging**
   - No protection rules
   - Deployment branch: `staging`

3. **production-approval**
   - Required reviewers: 1
   - Deployment branch: `main`

4. **production**
   - Deployment branch: `main`

## Deployment Process

### Development Workflow
```bash
# Developer creates feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push to GitHub
git push origin feature/new-feature

# Create PR to develop branch
# After review, merge to develop
# Automatic deployment to dev.justsimple.chat
```

### Staging Workflow
```bash
# When ready for QA
git checkout staging
git merge develop
git push origin staging
# Automatic deployment to staging.justsimple.chat
```

### Production Workflow
```bash
# After QA approval
git checkout main
git merge staging
git push origin main
# Wait for manual approval in GitHub Actions
# Automatic deployment to justsimple.chat
```

## Rollback Procedures

### Automatic Rollback
Production deployments automatically rollback if:
- Health checks fail (10 attempts)
- Critical endpoints return errors
- Database migrations fail

### Manual Rollback
1. Go to Actions → Rollback Deployment
2. Click "Run workflow"
3. Select environment and optionally specify commit/tag
4. Monitor rollback progress

## Health Checks

Each deployment includes health checks:
- `/api/health` - Basic health endpoint
- `/api/models` - Verify AI models loaded
- `/api/auth/providers` - Verify auth configuration

## Zero-Downtime Deployments

All deployments use PM2's reload feature:
- Starts new processes before killing old ones
- Maintains service availability
- Graceful shutdown of existing connections

## Security Considerations

1. **SSH Keys**: Use ED25519 keys with restricted permissions
2. **Secrets**: Never commit sensitive data
3. **Database**: Migrations run with restricted user
4. **Rollback**: Automatic backup points created

## Monitoring & Alerts

Add these integrations:
- Slack/Discord webhooks for deployment notifications
- Sentry for error tracking
- CloudWatch/Datadog for metrics

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify DEPLOYMENT_SSH_KEY is correct
   - Check server allows key-based auth
   - Ensure known_hosts is updated

2. **Health Check Failed**
   - Check PM2 logs: `pm2 logs simplechat-{env}`
   - Verify database connection
   - Check port configuration

3. **Build Failed**
   - Review Node.js version (22.13.0)
   - Check npm dependencies
   - Verify environment variables

### Debug Commands

```bash
# Check deployment status
pm2 list

# View logs
pm2 logs simplechat-{env} --lines 100

# Manual health check
curl https://{env}.justsimple.chat/api/health

# Database connection test
mysql -h 127.0.0.1 -u root -p{password} -e "SELECT 1"
```

## Best Practices

1. **Always test in dev first**
2. **Use meaningful commit messages**
3. **Document breaking changes**
4. **Monitor deployment progress**
5. **Keep rollback tags clean**
6. **Regular backup verification**

## Future Improvements

- [ ] Add E2E tests before production deploy
- [ ] Implement canary deployments
- [ ] Add performance benchmarks
- [ ] Create deployment dashboard
- [ ] Implement blue-green deployments