name: Rollback Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
          - development
          - staging
          - production
      rollback_tag:
        description: 'Git tag or commit SHA to rollback to (leave empty for last stable)'
        required: false
        type: string

jobs:
  rollback:
    name: Rollback ${{ github.event.inputs.environment }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOYMENT_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          
          # Determine server based on environment
          if [ "${{ github.event.inputs.environment }}" == "production" ]; then
            SERVER_HOST="${{ secrets.PROD_SERVER_HOST }}"
            SERVER_USER="${{ secrets.PROD_SERVER_USER }}"
            APP_NAME="simplechat-production"
            APP_DIR="/home/<USER>/deployments/production/simplechat-ai"
          elif [ "${{ github.event.inputs.environment }}" == "staging" ]; then
            SERVER_HOST="${{ secrets.STAGING_SERVER_HOST }}"
            SERVER_USER="${{ secrets.STAGING_SERVER_USER }}"
            APP_NAME="simplechat-staging"
            APP_DIR="/home/<USER>/deployments/staging/simplechat-ai"
          else
            SERVER_HOST="${{ secrets.DEV_SERVER_HOST }}"
            SERVER_USER="${{ secrets.DEV_SERVER_USER }}"
            APP_NAME="simplechat-dev"
            APP_DIR="/home/<USER>/deployments/dev/simplechat-ai"
          fi
          
          echo "SERVER_HOST=$SERVER_HOST" >> $GITHUB_ENV
          echo "SERVER_USER=$SERVER_USER" >> $GITHUB_ENV
          echo "APP_NAME=$APP_NAME" >> $GITHUB_ENV
          echo "APP_DIR=$APP_DIR" >> $GITHUB_ENV
          
          ssh-keyscan -H $SERVER_HOST >> ~/.ssh/known_hosts

      - name: Perform rollback
        run: |
          ssh -i ~/.ssh/deploy_key ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} << 'EOF'
            set -e
            
            cd ${{ env.APP_DIR }}
            
            # Determine rollback target
            if [ -n "${{ github.event.inputs.rollback_tag }}" ]; then
              ROLLBACK_TARGET="${{ github.event.inputs.rollback_tag }}"
            else
              # Find last rollback tag
              ROLLBACK_TARGET=$(git describe --tags --match="rollback-*" --abbrev=0 2>/dev/null || echo "HEAD~1")
            fi
            
            echo "🔄 Rolling back to: $ROLLBACK_TARGET"
            
            # Save current state as rollback point
            git tag -f "rollback-failed-$(date +%Y%m%d-%H%M%S)"
            
            # Rollback
            git reset --hard $ROLLBACK_TARGET
            
            # Reinstall dependencies
            npm ci --legacy-peer-deps
            
            # Rebuild
            npm run build
            
            # Reload PM2
            pm2 reload ${{ env.APP_NAME }} --update-env
            
            # Health check
            sleep 10
            if [ "${{ github.event.inputs.environment }}" == "production" ]; then
              curl -f https://justsimple.chat/api/health || exit 1
            elif [ "${{ github.event.inputs.environment }}" == "staging" ]; then
              curl -f https://staging.justsimple.chat/api/health || exit 1
            else
              curl -f http://localhost:3004/api/health || exit 1
            fi
            
            echo "✅ Rollback successful!"
          EOF

      - name: Notify rollback
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const status = '${{ job.status }}' === 'success' ? '✅ Success' : '❌ Failed';
            const env = '${{ github.event.inputs.environment }}';
            const target = '${{ github.event.inputs.rollback_tag }}' || 'previous stable version';
            const actor = context.actor;
            const message = `🔄 Rollback ${env}: ${status}\nTarget: ${target}\nInitiated by: ${actor}`;
            
            console.log(message);