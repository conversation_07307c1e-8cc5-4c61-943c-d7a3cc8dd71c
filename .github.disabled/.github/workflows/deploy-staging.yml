name: Deploy to Staging

on:
  push:
    branches:
      - staging
  workflow_dispatch:

env:
  NODE_VERSION: '22.13.0'

jobs:
  test:
    name: Test & Build
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --legacy-peer-deps

      - name: Run type checking
        run: npm run typecheck

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm test
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.STAGING_NEXTAUTH_SECRET }}

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: staging
          NEXT_PUBLIC_APP_URL: https://staging.justsimple.chat
          # Database
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.STAGING_NEXTAUTH_SECRET }}
          NEXTAUTH_URL: https://staging.justsimple.chat
          # AI Provider Keys
          OPENAI_API_KEY: ********************************************************************************************************************************************************************
          ANTHROPIC_API_KEY: ************************************************************************************************************
          GOOGLE_API_KEY: AIzaSyBl-dgrfGvI-Z2OL4-iKlNZKSBNUdFqMt8
          GROQ_API_KEY: ********************************************************
          TOGETHER_API_KEY: 8e5bffca039419cf9ba30ebb965dc57e973ebe6f0e8a96797b19e8c0c0e983e4
          XAI_API_KEY: ************************************************************************************
          DEEPSEEK_API_KEY: ***********************************
          MISTRAL_API_KEY: UyX0HmPQi6JTeIGcWLkDZ8q4n1VXnRi7
          PERPLEXITY_API_KEY: 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
          COHERE_API_KEY: BnKBNLVsmFKzn6h2q8mHQMy9oLILWudOGFmTUQvs
          OPENROUTER_API_KEY: 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
          # Bedrock
          USE_BEDROCK_ROUTER: true
          AWS_REGION: us-east-1
          BEDROCK_ROUTER_MODEL: amazon.nova-lite-v1:0
          # Email
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY || '' }}
          # Redis
          REDIS_URL: redis://localhost:6379
          # Stripe
          STRIPE_SECRET_KEY: sk_test_dummy
          STRIPE_WEBHOOK_SECRET: whsec_dummy
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: pk_test_dummy

  deploy:
    name: Deploy to staging.justsimple.chat
    runs-on: ubuntu-latest
    needs: test
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOYMENT_SSH_KEY }}" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H ${{ secrets.STAGING_SERVER_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          ssh -i ~/.ssh/deploy_key ${{ secrets.STAGING_SERVER_USER }}@${{ secrets.STAGING_SERVER_HOST }} << 'EOF'
            set -e
            
            # Navigate to staging directory
            cd /home/<USER>/deployments/staging/simplechat-ai
            
            # Backup current version (for rollback)
            pm2 save
            
            # Pull latest changes
            git fetch origin staging
            git reset --hard origin/staging
            
            # Install dependencies
            npm ci --legacy-peer-deps
            
            # Run database migrations
            export DATABASE_URL="${{ secrets.STAGING_DATABASE_URL }}"
            npm run db:generate || true
            npm run db:push || true
            
            # Build application
            npm run build
            
            # Reload PM2 process (zero downtime)
            pm2 reload simplechat-staging --update-env
            
            # Health check
            sleep 10
            for i in {1..5}; do
              if curl -f https://staging.justsimple.chat/api/health; then
                echo "✅ Health check passed!"
                break
              fi
              if [ $i -eq 5 ]; then
                echo "❌ Health check failed after 5 attempts"
                pm2 resurrect
                exit 1
              fi
              sleep 5
            done
            
            echo "✅ Staging deployment successful!"
          EOF

      - name: Run smoke tests
        run: |
          # Basic API tests
          curl -f https://staging.justsimple.chat/api/models
          curl -f https://staging.justsimple.chat/api/health

      - name: Create deployment record
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.ref,
              environment: 'staging',
              description: 'Staging deployment',
              production_environment: false,
              required_contexts: []
            });