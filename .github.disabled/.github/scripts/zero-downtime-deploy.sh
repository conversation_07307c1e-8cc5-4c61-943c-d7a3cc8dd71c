#!/bin/bash
# Zero-Downtime Deployment Script for JustSimpleChat
# This script ensures continuous service availability during deployments

set -euo pipefail

# Configuration
ENVIRONMENT=${1:-development}
APP_DIR=""
APP_NAME=""
PORT=""
HEALTH_URL=""

# Set environment-specific variables
case $ENVIRONMENT in
  development)
    APP_DIR="/home/<USER>/deployments/dev/simplechat-ai"
    APP_NAME="simplechat-dev"
    PORT="3004"
    HEALTH_URL="http://localhost:3004/api/health"
    ;;
  staging)
    APP_DIR="/home/<USER>/deployments/staging/simplechat-ai"
    APP_NAME="simplechat-staging"
    PORT="3005"
    HEALTH_URL="https://staging.justsimple.chat/api/health"
    ;;
  production)
    APP_DIR="/home/<USER>/deployments/production/simplechat-ai"
    APP_NAME="simplechat-production"
    PORT="3006"
    HEALTH_URL="https://justsimple.chat/api/health"
    ;;
  *)
    echo "❌ Invalid environment: $ENVIRONMENT"
    exit 1
    ;;
esac

echo "🚀 Starting zero-downtime deployment for $ENVIRONMENT"

# Function to check if service is healthy
check_health() {
  local max_attempts=30
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    if curl -sf "$HEALTH_URL" > /dev/null; then
      echo "✅ Health check passed (attempt $attempt/$max_attempts)"
      return 0
    fi
    echo "⏳ Health check attempt $attempt/$max_attempts failed, retrying..."
    sleep 2
    ((attempt++))
  done
  
  echo "❌ Health check failed after $max_attempts attempts"
  return 1
}

# Function to get current PM2 process info
get_pm2_info() {
  pm2 jlist | jq -r ".[] | select(.name == \"$APP_NAME\") | .pm2_env.instances // 1"
}

# Navigate to application directory
cd "$APP_DIR"

# Step 1: Pre-deployment checks
echo "📋 Pre-deployment checks..."
if ! pm2 show "$APP_NAME" > /dev/null 2>&1; then
  echo "❌ PM2 process $APP_NAME not found"
  exit 1
fi

# Get current number of instances
CURRENT_INSTANCES=$(get_pm2_info)
echo "📊 Current instances: $CURRENT_INSTANCES"

# Step 2: Pull latest code (already done by workflow)
echo "📦 Code already updated by workflow"

# Step 3: Install dependencies without disrupting service
echo "📚 Installing dependencies..."
npm ci --legacy-peer-deps --prefer-offline --no-audit

# Step 4: Build application
echo "🔨 Building application..."
npm run build

# Step 5: Database migrations (if needed)
if [ -f "prisma/schema.prisma" ]; then
  echo "🗄️ Running database migrations..."
  npm run db:generate || true
  npm run db:push || echo "⚠️ Database migration failed, continuing..."
fi

# Step 6: Prepare for zero-downtime reload
echo "🔄 Preparing zero-downtime reload..."

# For production, scale up temporarily for extra safety
if [ "$ENVIRONMENT" == "production" ] && [ "$CURRENT_INSTANCES" -lt 2 ]; then
  echo "📈 Scaling up to 2 instances for safe deployment..."
  pm2 scale "$APP_NAME" 2
  sleep 5
fi

# Step 7: Graceful reload with PM2
echo "♻️ Performing graceful reload..."
pm2 reload "$APP_NAME" --update-env

# Step 8: Wait for new instances to be ready
echo "⏱️ Waiting for new instances to become healthy..."
sleep 5

# Step 9: Health check verification
if ! check_health; then
  echo "❌ Deployment failed health checks, attempting rollback..."
  
  # Rollback procedure
  git reset --hard HEAD~1
  npm ci --legacy-peer-deps --prefer-offline --no-audit
  npm run build
  pm2 reload "$APP_NAME" --update-env
  
  if check_health; then
    echo "✅ Rollback successful"
  else
    echo "🚨 Rollback failed! Manual intervention required"
    exit 1
  fi
  exit 1
fi

# Step 10: Scale back to original instance count if needed
if [ "$ENVIRONMENT" == "production" ] && [ "$CURRENT_INSTANCES" -eq 1 ]; then
  echo "📉 Scaling back to 1 instance..."
  sleep 10  # Give time for connections to balance
  pm2 scale "$APP_NAME" 1
fi

# Step 11: Post-deployment verification
echo "🔍 Running post-deployment verification..."

# Check response time
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "$HEALTH_URL")
echo "📊 Health endpoint response time: ${RESPONSE_TIME}s"

if (( $(echo "$RESPONSE_TIME > 5" | bc -l) )); then
  echo "⚠️ Warning: Response time is high (${RESPONSE_TIME}s)"
fi

# Check memory usage
MEMORY_USAGE=$(pm2 jlist | jq -r ".[] | select(.name == \"$APP_NAME\") | .monit.memory" | numfmt --to=iec)
echo "💾 Memory usage: $MEMORY_USAGE"

# Save PM2 configuration
pm2 save

echo "✅ Zero-downtime deployment completed successfully!"
echo "📊 Summary:"
echo "   - Environment: $ENVIRONMENT"
echo "   - Application: $APP_NAME"
echo "   - Port: $PORT"
echo "   - Health URL: $HEALTH_URL"
echo "   - Response Time: ${RESPONSE_TIME}s"
echo "   - Memory Usage: $MEMORY_USAGE"