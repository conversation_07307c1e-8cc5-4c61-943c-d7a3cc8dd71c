// PM2 Configuration for Zero-Downtime Deployments
// This configuration ensures maximum availability during deployments

module.exports = {
  apps: [
    {
      // Development Environment
      name: 'simplechat-dev',
      script: 'npm',
      args: 'run dev',
      cwd: '/home/<USER>/deployments/dev/simplechat-ai',
      env: {
        NODE_ENV: 'development',
        PORT: 3004
      },
      
      // Zero-downtime settings
      instances: 1,
      exec_mode: 'cluster',
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Graceful shutdown
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 3000,
      
      // Memory management
      max_memory_restart: '1G',
      
      // Advanced options
      instance_var: 'INSTANCE_ID',
      merge_logs: true,
      
      // Error handling
      error_file: '/home/<USER>/deployments/logs/dev-error.log',
      out_file: '/home/<USER>/deployments/logs/dev-out.log',
      log_file: '/home/<USER>/deployments/logs/dev-combined.log',
      time: true,
      
      // Health check endpoint
      health_check: {
        url: 'http://localhost:3004/api/health',
        interval: 30000,
        timeout: 5000
      }
    },
    
    {
      // Staging Environment
      name: 'simplechat-staging',
      script: 'npm',
      args: 'run start',
      cwd: '/home/<USER>/deployments/staging/simplechat-ai',
      env: {
        NODE_ENV: 'staging',
        PORT: 3005
      },
      
      // Zero-downtime settings
      instances: 1,
      exec_mode: 'cluster',
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '30s',
      
      // Graceful shutdown
      kill_timeout: 10000,
      wait_ready: true,
      listen_timeout: 5000,
      
      // Memory management
      max_memory_restart: '1.5G',
      
      // Advanced options
      instance_var: 'INSTANCE_ID',
      merge_logs: true,
      
      // Error handling
      error_file: '/home/<USER>/deployments/logs/staging-error.log',
      out_file: '/home/<USER>/deployments/logs/staging-out.log',
      log_file: '/home/<USER>/deployments/logs/staging-combined.log',
      time: true,
      
      // Health check endpoint
      health_check: {
        url: 'http://localhost:3005/api/health',
        interval: 30000,
        timeout: 5000
      }
    },
    
    {
      // Production Environment
      name: 'simplechat-production',
      script: 'npm',
      args: 'run start:3006',
      cwd: '/home/<USER>/deployments/production/simplechat-ai',
      env: {
        NODE_ENV: 'production',
        PORT: 3006
      },
      
      // Zero-downtime settings for production
      instances: 1,  // Can scale to 2 during deployment
      exec_mode: 'cluster',
      watch: false,
      autorestart: true,
      max_restarts: 5,
      min_uptime: '60s',
      
      // Graceful shutdown (longer for production)
      kill_timeout: 15000,
      wait_ready: true,
      listen_timeout: 10000,
      
      // Memory management
      max_memory_restart: '2G',
      
      // Advanced options
      instance_var: 'INSTANCE_ID',
      merge_logs: true,
      
      // Production-specific optimizations
      node_args: '--max-old-space-size=1536',
      
      // Error handling
      error_file: '/home/<USER>/deployments/logs/production-error.log',
      out_file: '/home/<USER>/deployments/logs/production-out.log',
      log_file: '/home/<USER>/deployments/logs/production-combined.log',
      time: true,
      
      // Health check endpoint
      health_check: {
        url: 'http://localhost:3006/api/health',
        interval: 20000,
        timeout: 5000
      },
      
      // Production safeguards
      min_uptime: '60s',
      max_restarts: 3,
      restart_delay: 5000
    }
  ],
  
  // Deploy configuration
  deploy: {
    production: {
      user: 'ec2-user',
      host: process.env.PROD_SERVER_HOST,
      ref: 'origin/main',
      repo: '**************:y3dltd/justsimplechat.git',
      path: '/home/<USER>/deployments/production',
      'pre-deploy': 'git pull',
      'post-deploy': 'npm ci --legacy-peer-deps && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-deploy-local': 'echo "Deploying to production..."'
    },
    staging: {
      user: 'ec2-user',
      host: process.env.STAGING_SERVER_HOST,
      ref: 'origin/staging',
      repo: '**************:y3dltd/justsimplechat.git',
      path: '/home/<USER>/deployments/staging',
      'post-deploy': 'npm ci --legacy-peer-deps && npm run build && pm2 reload ecosystem.config.js --env staging'
    },
    development: {
      user: 'ec2-user',
      host: process.env.DEV_SERVER_HOST,
      ref: 'origin/develop',
      repo: '**************:y3dltd/justsimplechat.git',
      path: '/home/<USER>/deployments/dev',
      'post-deploy': 'npm ci --legacy-peer-deps && pm2 reload ecosystem.config.js --env development'
    }
  }
};