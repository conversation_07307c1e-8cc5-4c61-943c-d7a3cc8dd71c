# Deployment Checklist

**Description**: Comprehensive checklist to ensure safe and successful deployments to staging and production environments.

## Pre-Deployment Checklist

### Code Quality
- [ ] All TypeScript checks pass (`npm run typecheck`)
- [ ] ESLint passes with no errors (`npm run lint`)
- [ ] All tests pass (`npm test`)
- [ ] Code review completed and approved

### Build Verification
- [ ] Build completes successfully (`npm run build`)
- [ ] No build warnings that affect functionality
- [ ] Bundle size is within acceptable limits
- [ ] All environment variables are set

### Security Check
- [ ] No sensitive data in code or logs
- [ ] API keys are properly secured
- [ ] No debug code or console.logs in production
- [ ] Security headers configured

### Feature Testing
- [ ] New features tested in development
- [ ] Edge cases tested
- [ ] Cross-browser testing completed
- [ ] Mobile responsiveness verified

## Deployment Steps

### Staging Deployment
- [ ] Checkout main branch
- [ ] Pull latest changes
- [ ] Install dependencies with `--legacy-peer-deps`
- [ ] Run build process
- [ ] Restart PM2 process
- [ ] Verify health endpoint
- [ ] Test critical user flows
- [ ] Monitor logs for errors

### Production Deployment
- [ ] Staging testing completed successfully
- [ ] Backup database (if schema changes)
- [ ] Announce maintenance window (if needed)
- [ ] Deploy to production
- [ ] Verify health endpoint
- [ ] Test critical features
- [ ] Monitor error rates
- [ ] Check performance metrics

## Post-Deployment Verification

### Immediate Checks (First 5 minutes)
- [ ] Application loads correctly
- [ ] Authentication works
- [ ] Chat functionality operational
- [ ] No console errors in browser
- [ ] API endpoints responding

### Extended Monitoring (First hour)
- [ ] Error rates normal
- [ ] Response times acceptable
- [ ] Database connections stable
- [ ] Memory usage stable
- [ ] No user complaints

## Rollback Criteria

### Immediate Rollback Triggers
Execute rollback immediately if any of these occur:

- [ ] **Authentication System Down**: Users cannot log in or access the application
- [ ] **Chat Functionality Broken**: Messages fail to send or receive
- [ ] **Data Loss Detected**: Any indication of data corruption or loss
- [ ] **Security Breach**: Exposed credentials, unauthorised access, or vulnerabilities
- [ ] **Complete API Failure**: All endpoints returning errors

### Performance-Based Rollback
Monitor for 15 minutes, rollback if thresholds exceeded:

- [ ] **Error Rate > 5%**: More than 5% of requests failing
- [ ] **Response Time > 3s**: P95 latency exceeds 3 seconds
- [ ] **Database Connection Errors**: Persistent connection pool exhaustion
- [ ] **Memory Leak Detected**: Memory usage growing without bounds
- [ ] **CPU Usage > 90%**: Sustained high CPU preventing normal operation

### User Impact Rollback
Based on user reports and monitoring:

- [ ] **Multiple User Complaints**: 3+ reports of the same critical issue
- [ ] **Payment System Issues**: Any billing or subscription failures
- [ ] **Data Integrity Issues**: Conversations not saving or loading incorrectly
- [ ] **Model Router Failures**: Auto mode consistently selecting wrong models

## Rollback Procedure

### Quick Rollback Steps
```bash
# 1. Switch to production directory
cd /home/<USER>/deployments/production/simplechat-ai

# 2. Stash any local changes
git stash

# 3. Rollback to previous release
git checkout <previous-release-tag>

# 4. Install dependencies
npm install --legacy-peer-deps

# 5. Rebuild application
npm run build

# 6. Restart service
pm2 restart simplechat-production

# 7. Verify rollback
curl https://justsimple.chat/api/health
```

### Database Rollback (if schema changed)
```bash
# Only if database migrations were applied
mysql -h 127.0.0.1 -u root -p < /backups/pre-deployment-backup.sql
```

## Communication

### Before Deployment

#### Internal Communication
- [ ] **Development Team Notified**
  - Deployment time window
  - Expected duration (typically 5-10 minutes)
  - Features being deployed
  - Potential risks identified

- [ ] **Operations Team Briefed**
  - Monitoring requirements
  - Rollback procedures reviewed
  - On-call engineer confirmed

- [ ] **Support Team Prepared**
  - New features documentation shared
  - Known issues communicated
  - FAQ updated for common questions

#### External Communication (Major Releases)
- [ ] **Status Page Updated**: Scheduled maintenance posted
- [ ] **User Notification**: In-app banner for major changes
- [ ] **API Consumers**: Email sent 24 hours prior for breaking changes

### During Deployment

- [ ] **Slack Status**: Team channel updated with progress
- [ ] **Monitoring Dashboard**: Open and actively watched
- [ ] **Incident Channel**: Ready for quick communication

### After Deployment

#### Successful Deployment
- [ ] **Deployment Success Confirmed**
  - Health checks passing
  - No critical errors in logs
  - Key features tested and working

- [ ] **Release Notes Published**
  - User-facing changelog updated
  - API documentation updated if needed
  - Internal wiki updated with technical details

- [ ] **Stakeholder Update**
  - Product team notified of completion
  - Metrics dashboard link shared
  - Next deployment window discussed

- [ ] **Documentation Updates**
  - README files updated with new features
  - Configuration changes documented
  - Troubleshooting guide expanded

#### Failed Deployment
- [ ] **Incident Report Created**
  - Root cause analysis
  - Timeline of events
  - Lessons learned
  - Action items to prevent recurrence

- [ ] **Stakeholder Communication**
  - Clear explanation of what went wrong
  - Impact assessment
  - Resolution timeline
  - Preventive measures

## Post-Deployment Review

### Within 24 Hours
- [ ] **Metrics Review**
  - Performance compared to baseline
  - Error rates analysed
  - User engagement metrics checked
  - Cost impact assessed

- [ ] **Team Retrospective** (if issues occurred)
  - What went well
  - What could be improved
  - Process refinements needed
  - Tools or automation opportunities

### Within 1 Week
- [ ] **User Feedback Analysis**
  - Support tickets reviewed
  - User satisfaction scores
  - Feature adoption rates
  - Bug reports triaged

- [ ] **Technical Debt Assessment**
  - New debt introduced
  - Old debt resolved
  - Priority adjustments needed
  - Refactoring opportunities

## Emergency Contacts

### Escalation Path
1. **On-Call Developer**: Check PM2 logs and application health
2. **DevOps Lead**: Infrastructure and deployment issues
3. **Product Manager**: User impact and communication
4. **CTO**: Major incidents requiring executive decision

### Key Resources
- **Monitoring**: `https://justsimple.chat/admin/monitoring`
- **Logs**: `pm2 logs simplechat-production --nostream`
- **Health Check**: `https://justsimple.chat/api/health`
- **Status Page**: `https://status.justsimple.chat`
- **Runbook**: `/home/<USER>/deployments/RUNBOOK.md`

---

**Remember**: A smooth deployment is a boring deployment. If in doubt, delay and investigate.