# Troubleshooting Guide

## Table of Contents
- [Common Issues](#common-issues)
- [Build & Deployment Issues](#build--deployment-issues)
- [Runtime Errors](#runtime-errors)
- [Database Issues](#database-issues)
- [API & Integration Issues](#api--integration-issues)
- [Debug Commands](#debug-commands)
- [Log Analysis](#log-analysis)

## Common Issues

### 1. Web Search Not Triggering

**Symptoms:**
- Model says it doesn't have access to current information
- No web search performed for time-sensitive queries

**Solutions:**
```bash
# Check router logs
pm2 logs simplechat-dev --nostream | grep -i "router"

# Verify router decision includes searchQueries
pm2 logs simplechat-dev --nostream | grep "searchQueries"

# Test router directly
curl -X POST https://dev.justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "What is in the news today?"}]}'
```

**Common Causes:**
- Router not detecting real-time keywords
- Web search disabled for user plan
- Brave Search API key missing

### 2. Streaming Errors

**Error:** `Cannot read properties of null (reading 'generateStream')`

**Solution:**
```typescript
// Use getFallbackProvider() instead of direct access
const provider = getFallbackProvider();
if (!provider) {
  throw new Error('No provider available');
}
```

### 3. O-Series Double Reasoning

**Symptoms:**
- Reasoning appears in both reasoning box AND message content

**Solution:**
Check route.ts to ensure reasoning chunks are filtered:
```typescript
if (chunk.type === 'content') {
  // Only add non-reasoning content
  assistantMessage += chunk.content;
}
```

## Build & Deployment Issues

### 1. Build Failures

**Error:** `PageNotFoundError: Cannot find module for page`

**Solutions:**
```bash
# Clear Next.js cache
rm -rf .next
rm -rf node_modules/.cache

# Reinstall dependencies
npm install --legacy-peer-deps

# Rebuild
npm run build
```

### 2. TypeScript Errors

**Common Type Issues:**
```typescript
// Missing reasoning property
interface TokenUsage {
  prompt_tokens: number;
  completion_tokens: number;
  reasoning?: number; // Add this
}
```

**Check for errors:**
```bash
npm run typecheck
```

### 3. PM2 Process Issues

**Process won't start:**
```bash
# Check error logs
pm2 logs simplechat-dev --err --nostream

# Delete and restart
pm2 delete simplechat-dev
pm2 start ecosystem.config.js --only simplechat-dev

# Save PM2 state
pm2 save
```

### 4. Legacy Peer Dependencies

**Error:** `npm ERR! peer dep missing`

**Solution:**
Always use legacy peer deps flag:
```bash
npm install --legacy-peer-deps
npm ci --legacy-peer-deps  # For CI/CD
```

### 5. Environment Variable Issues

**Symptoms:**
- Features not working as expected
- API keys not recognised

**Debug:**
```bash
# Check loaded environment
pm2 env simplechat-dev

# Reload with new env
pm2 restart simplechat-dev --update-env

# Verify .env.local is loaded
node -e "require('dotenv').config({path: '.env.local'}); console.log(process.env.LITELLM_API_KEY)"
```

## Runtime Errors

### 1. Memory Issues

**Symptoms:**
- Process crashes with heap errors
- Slow performance

**Solutions:**
```bash
# Increase memory limit
pm2 start app.js --node-args="--max-old-space-size=4096"

# Monitor memory usage
pm2 monit
```

### 2. Port Conflicts

**Error:** `EADDRINUSE: address already in use`

**Solution:**
```bash
# Find process using port
lsof -i :3004

# Kill process
kill -9 <PID>

# Or use different port
PORT=3005 npm run dev
```

### 3. Hot Reload Not Working

**Solution:**
```bash
# Ensure using dev script for development
pm2 restart simplechat-dev --update-env

# Check PM2 config uses npm run dev
cat ecosystem.config.js | grep script
```

## Database Issues

### 1. Connection Refused

**Error:** `ECONNREFUSED 127.0.0.1:3306`

**Solutions:**
```bash
# Check MySQL is running
sudo systemctl status mysql

# Start MySQL
sudo systemctl start mysql

# Test connection
mysql -h 127.0.0.1 -u root -p
```

### 2. Authentication Failures

**Error:** `ER_ACCESS_DENIED_ERROR`

**Solutions:**
```bash
# Reset password
mysql -u root
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;

# Update .env.local
DATABASE_URL="mysql://root:new_password@127.0.0.1:3306/justsimplechat_dev"
```

### 3. Migration Issues

**Solutions:**
```bash
# Run migrations
npx prisma migrate deploy

# Reset database (CAUTION!)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate
```

## API & Integration Issues

### 1. LiteLLM Connection

**Error:** `Failed to connect to LiteLLM`

**Solutions:**
```bash
# Test LiteLLM health
curl http://litellm-proxy-alb-**********.us-east-1.elb.amazonaws.com/health

# Check API key
echo $LITELLM_API_KEY

# Test with curl
curl -X POST http://litellm-proxy-alb-**********.us-east-1.elb.amazonaws.com/v1/chat/completions \
  -H "Authorization: Bearer sk-simplechat-master-2025" \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-3.5-turbo", "messages": [{"role": "user", "content": "test"}]}'
```

### 2. Model Not Found

**Error:** `Model xxx not found`

**Solutions:**
1. Check exact model name in LiteLLM
2. Verify model is added to database
3. Check user plan has access to model

### 3. Rate Limiting

**Error:** `Rate limit exceeded`

**Solutions:**
```javascript
// Implement exponential backoff
const backoff = (attempt) => Math.min(1000 * Math.pow(2, attempt), 30000);

// Add retry logic
for (let i = 0; i < 3; i++) {
  try {
    return await makeRequest();
  } catch (error) {
    if (error.status === 429) {
      await sleep(backoff(i));
      continue;
    }
    throw error;
  }
}
```

## Debug Commands

### Application Health
```bash
# Check all services
curl https://dev.justsimple.chat/api/health
curl http://litellm-proxy-alb-**********.us-east-1.elb.amazonaws.com/health

# PM2 status
pm2 list
pm2 monit

# System resources
htop
df -h
free -m
```

### Database Queries
```bash
# Check conversations
mysql -h 127.0.0.1 -u root -p justsimplechat_dev -e "SELECT COUNT(*) FROM conversations;"

# Check messages
mysql -h 127.0.0.1 -u root -p justsimplechat_dev -e "SELECT COUNT(*) FROM messages;"

# Active sessions
mysql -h 127.0.0.1 -u root -p justsimplechat_dev -e "SELECT COUNT(*) FROM sessions WHERE expires > NOW();"
```

### Log Analysis
```bash
# Search for errors
pm2 logs --nostream | grep -i error | tail -50

# Router decisions
pm2 logs --nostream | grep "Router Decision" | tail -20

# Web search activity
pm2 logs --nostream | grep -i "web.*search" | tail -30

# API calls
pm2 logs --nostream | grep "API call" | tail -20
```

## Log Analysis

### Understanding Log Patterns

**Router Logs:**
```
[Router] Decision: {
  model: { id: 'claude-3-5-sonnet', name: 'Claude 3.5 Sonnet' },
  reason: 'Best for complex analysis',
  searchQueries: ['latest news today', 'current events']
}
```

**API Logs:**
```
{"level":"info","category":"api","message":"chat-stream completed","data":{"model":"gpt-4-turbo","duration":3421}}
```

**Error Patterns:**
```
[Error: ECONNREFUSED] - Network/connection issue
[Error: 401] - Authentication failure
[Error: 429] - Rate limiting
[Error: 500] - Server error
```

### Useful Grep Patterns
```bash
# Find slow requests (>5s)
pm2 logs --nostream | grep -E "duration\":[5-9][0-9]{3}|duration\":[0-9]{5}"

# Find failed requests
pm2 logs --nostream | grep -E "status\":(4|5)[0-9]{2}"

# Memory warnings
pm2 logs --nostream | grep -i "heap\|memory"
```

## Quick Fixes

### Reset Everything
```bash
# Stop all processes
pm2 stop all

# Clear caches
rm -rf .next
rm -rf node_modules/.cache
redis-cli FLUSHALL

# Reinstall and rebuild
npm install --legacy-peer-deps
npm run build

# Restart
pm2 restart all
```

### Emergency Rollback
```bash
# In production directory
cd /home/<USER>/deployments/production/simplechat-ai

# Rollback to previous commit
git log --oneline -10  # Find good commit
git reset --hard <commit-hash>

# Rebuild and restart
npm install --legacy-peer-deps
npm run build
pm2 restart simplechat-production
```

## Performance Troubleshooting

### 1. Slow API Responses

**Identify bottlenecks:**
```javascript
// Add timing middleware
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    if (duration > 1000) {
      console.log(`Slow request: ${req.method} ${req.url} took ${duration}ms`);
    }
  });
  next();
});
```

**Common causes:**
- Slow database queries
- Large response payloads
- Inefficient model routing
- Network latency

### 2. High Memory Usage

**Monitor and debug:**
```bash
# Check Node.js memory
pm2 show simplechat-dev | grep memory

# Generate heap snapshot
pm2 trigger simplechat-dev heapdump /tmp/heap.heapsnapshot

# Analyze with Chrome DevTools
# 1. Open chrome://inspect
# 2. Load heap snapshot
# 3. Look for memory leaks
```

### 3. CPU Spikes

**Profile CPU usage:**
```bash
# Enable profiling
NODE_ENV=production node --prof app.js

# Process profile
node --prof-process isolate-*.log > processed.txt

# Find hot functions
grep "^ *[0-9]" processed.txt | sort -nr | head -20
```

## Advanced Debugging

### Enable Debug Logging

```bash
# Set debug environment
export DEBUG=*
export NODE_ENV=development

# Specific debug namespaces
export DEBUG=app:*,litellm:*,router:*

# Run with debug
DEBUG=* npm run dev
```

### Network Debugging

```bash
# Trace API calls
tcpdump -i any -A 'tcp port 3004 and (((ip[2:2] - ((ip[0]&0xf)<<2)) - ((tcp[12]&0xf0)>>2)) != 0)'

# Monitor HTTP traffic
ngrep -q -W byline "^(GET|POST)" tcp and port 3004

# Check DNS resolution
dig justsimple.chat
nslookup litellm-proxy-alb.amazonaws.com
```

### Database Performance

```sql
-- Find slow queries
SELECT 
  query_time,
  lock_time,
  rows_sent,
  rows_examined,
  sql_text
FROM mysql.slow_log
ORDER BY query_time DESC
LIMIT 10;

-- Check index usage
SELECT 
  table_name,
  index_name,
  non_unique,
  cardinality
FROM information_schema.statistics
WHERE table_schema = 'justsimplechat_dev'
ORDER BY cardinality DESC;

-- Table sizes
SELECT 
  table_name,
  ROUND((data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'justsimplechat_dev'
ORDER BY (data_length + index_length) DESC;
```

## Common Error Reference

| Error | Cause | Quick Fix |
|-------|-------|-----------|
| `ECONNREFUSED` | Service down | Check service status, restart |
| `ETIMEDOUT` | Network issue | Check connectivity, firewall |
| `ENOMEM` | Out of memory | Increase heap size, check leaks |
| `EADDRINUSE` | Port in use | Kill process or change port |
| `401 Unauthorized` | Invalid API key | Check credentials in .env |
| `429 Too Many Requests` | Rate limited | Implement backoff, check limits |
| `500 Internal Error` | Server crash | Check logs, restart service |
| `503 Service Unavailable` | Overloaded | Scale up, optimize code |

## Recovery Procedures

### Data Recovery

```bash
# Backup current state
mysqldump -u root -p justsimplechat_dev > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
mysql -u root -p justsimplechat_dev < backup_20240624_120000.sql

# Export specific tables
mysqldump -u root -p justsimplechat_dev conversations messages > conversations_backup.sql
```

### Redis Recovery

```bash
# Save current state
redis-cli BGSAVE

# Check save status
redis-cli LASTSAVE

# Restore from dump
sudo cp /backup/dump.rdb /var/lib/redis/dump.rdb
sudo systemctl restart redis
```

### Application State Recovery

```bash
# Save PM2 process list
pm2 save

# Restore PM2 processes
pm2 resurrect

# Export ecosystem
pm2 ecosystem > ecosystem.backup.js
```

## Monitoring Setup

### Health Check Script

```bash
#!/bin/bash
# save as: /home/<USER>/health-check.sh

# Check services
services=("mysql" "redis" "nginx")
for service in "${services[@]}"; do
  if ! systemctl is-active --quiet $service; then
    echo "ERROR: $service is not running"
    # Send alert
  fi
done

# Check endpoints
endpoints=(
  "https://dev.justsimple.chat/api/health"
  "http://litellm-proxy-alb.amazonaws.com/health"
)
for endpoint in "${endpoints[@]}"; do
  if ! curl -f -s "$endpoint" > /dev/null; then
    echo "ERROR: $endpoint is not responding"
    # Send alert
  fi
done

# Check disk space
if [ $(df -h / | awk 'NR==2 {print $(NF-1)}' | sed 's/%//') -gt 80 ]; then
  echo "WARNING: Disk usage above 80%"
fi
```

### Automated Monitoring

```bash
# Add to crontab
*/5 * * * * /home/<USER>/health-check.sh >> /home/<USER>/health-check.log 2>&1

# Log rotation
cat > /etc/logrotate.d/health-check << EOF
/home/<USER>/health-check.log {
  daily
  rotate 7
  compress
  missingok
  notifempty
}
EOF
```

---
*Last Updated: June 24, 2025*

**Emergency Contacts:**
- On-Call Developer: Check Slack #oncall
- DevOps Lead: <EMAIL>
- Escalation: CTO for P0 incidents