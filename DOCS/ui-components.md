# UI Components Documentation

## Table of Contents
- [Chat Components](#chat-components)
- [Web Search Components](#web-search-components)
- [O-Series Components](#o-series-components)
- [Router Components](#router-components)
- [Component Interactions](#component-interactions)
- [Recent UI Changes](#recent-ui-changes)

## Chat Components

### ChatInterface (`/src/components/chat/chat-interface.tsx`)
Main chat container component that manages:
- Message state and streaming
- Web search toggle state
- Model selection (auto/manual)
- Conversation management

**Key States:**
```typescript
const [messages, setMessages] = useState<Message[]>([])
const [webSearchEnabled, setWebSearchEnabled] = useState(false)
const [selectedModel, setSelectedModel] = useState<string>('auto')
const [isAutoMode, setIsAutoMode] = useState(true)
```

### MessageList (`/src/components/chat/message-list.tsx`)
Renders the list of messages with:
- Smooth animations (framer-motion)
- Layout stabilization
- Streaming message handling

**Props:**
```typescript
interface MessageListProps {
  messages: Message[]
  streamingMessage?: Message | null
  webSearchActive?: boolean
  webSearchQueries?: string[]
  isLoading: boolean
}
```

### MessageItem (`/src/components/chat/message-item.tsx`)
Individual message display with:
- Markdown rendering
- Code syntax highlighting
- Source citations (via CondensedSources)
- Copy/retry functionality

## Web Search Components

### WebSearchButton (`/src/components/chat/web-search-button.tsx`)
Toggle button in chat input area:
- Visual glow effect when enabled
- Tooltip with status
- Keyboard shortcut support (Cmd/Ctrl + /)

### CondensedSources (`/src/components/chat/condensed-sources.tsx`)
**New component** displaying search sources as compact pills:
```typescript
interface CondensedSourcesProps {
  searchResults?: SearchResult[]
  className?: string
}
```

**Features:**
- Domain extraction with favicon
- Clickable links to sources
- Hover effects
- Compact horizontal layout

### ~~Removed Components~~
1. **GlobalWebSearchIndicator**: Fixed bottom-left green indicator
2. **WebSearchIndicator**: Large green search status box
3. **Search indicator in streaming**: Blue box during search

## O-Series Components

### UnifiedMessageDisplay (`/src/components/chat/unified-message-display.tsx`)
Special display for O-series models combining:
- Reasoning summary box
- Main message content
- Auto-collapse functionality
- Unified visual design

**Key Features:**
- 4-second minimum display time
- 1.5-second grace period after streaming
- Click to prevent auto-collapse
- Smooth expand/collapse animations

### ReasoningSummary (`/src/components/chat/reasoning-summary.tsx`)
Displays O-series reasoning process:
- Collapsible container
- Thinking animation during streaming
- Gradient effects
- "Show more/less" toggle

**Animation States:**
```typescript
const animationVariants = {
  streaming: { /* shimmer effect */ },
  complete: { /* static display */ },
  collapsed: { /* minimized view */ }
}
```

## Router Components

### RouterAnalysisAnimation (`/src/components/chat/router-analysis-animation.tsx`)
Shows router decision process:
- Multi-stage progress animation
- Web search detection stage
- Model selection visualization
- Floating particle effects

**Stages:**
1. Analyzing complexity
2. Understanding intent
3. Matching capabilities
4. (Optional) Web search
5. Model selected

### ModelSelectorEnhanced (`/src/components/chat/model-selector-enhanced.tsx`)
Advanced model selection UI:
- Auto/Manual mode toggle
- Categorized model display
- Search functionality
- Visual model cards

## Component Interactions

### Message Flow
```
ChatInterface
  ├── ChatInput
  │   └── WebSearchButton
  ├── MessageList
  │   ├── MessageItem
  │   │   └── CondensedSources
  │   └── SmoothStreamingMessage
  └── ModelSelectorEnhanced
```

### State Flow
```
User Input → Router Decision → Model Selection → Response Generation
     ↓             ↓                                    ↓
Web Search   Search Queries                    Message + Sources
  Toggle      Generated                          Display
```

### O-Series Flow
```
O-Series Selected → Responses API → Reasoning Events → UnifiedMessageDisplay
                                        ↓                      ↓
                                  Content Events      ReasoningSummary
```

## Recent UI Changes

### June 23, 2025

1. **Web Search UI Cleanup**
   - Removed all floating web search indicators
   - Consolidated sources into message display
   - Cleaner, less cluttered interface

2. **O-Series Enhancements**
   - Unified display with better visual hierarchy
   - Modern animations (gradient shimmer)
   - Smart auto-collapse timing
   - Improved mobile responsiveness

3. **Component Removals**
   ```typescript
   // Removed from chat-interface.tsx
   - import { GlobalWebSearchIndicator } from './global-web-search-indicator'
   
   // Removed from smooth-streaming-message.tsx (lines 92-115)
   - {webSearchActive && (<motion.div>...web search indicator...</motion.div>)}
   ```

## Styling Patterns

### Common Classes
```typescript
// Dark theme optimized
"bg-gray-800 dark:bg-gray-900"
"text-gray-300 dark:text-gray-200"
"border-gray-700 dark:border-gray-600"

// Gradient effects
"bg-gradient-to-r from-purple-600 to-blue-600"
"bg-gradient-to-br from-gray-900/90 to-gray-800/70"

// Animation utilities
"transition-all duration-200 ease-out"
"animate-pulse"
"motion-safe:animate-spin"
```

### Responsive Design
- Mobile-first approach
- Breakpoints: `sm:`, `md:`, `lg:`, `xl:`
- Touch-friendly tap targets (min 44px)
- Collapsible sidebars on mobile

## Performance Considerations

1. **Lazy Loading**
   - Code splitting for heavy components
   - Dynamic imports for modals
   - Suspense boundaries for async data

2. **Animation Performance**
   - Use `transform` and `opacity` for animations
   - Avoid animating `width`/`height`
   - GPU-accelerated transforms

3. **State Optimization**
   - Minimize re-renders with memo
   - Use callbacks for event handlers
   - Optimize context usage

---
*Last Updated: June 24, 2025*