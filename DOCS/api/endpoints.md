# API Endpoints

**Description**: Complete reference of all API endpoints, including request/response examples and parameter documentation.

## Chat Endpoints

### POST /api/chat

The primary endpoint for AI chat interactions, supporting both streaming and non-streaming responses.

#### Request

```http
POST /api/chat
Content-Type: application/json
Authorization: Bearer <api_key>
```

```json
{
  "messages": [
    {
      "role": "user",
      "content": "Explain quantum computing"
    }
  ],
  "model": "auto",  // or specific model like "gpt-4o"
  "stream": true,
  "temperature": 0.7,
  "max_tokens": 2000,
  "conversation_id": "optional-conversation-id"
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `messages` | Array | Yes | Array of message objects with `role` and `content` |
| `model` | String | No | Model identifier or "auto" for intelligent routing (default: "auto") |
| `manualModel` | String | No | Bypass router with specific model selection |
| `stream` | Boolean | No | Enable streaming responses (default: true) |
| `temperature` | Number | No | Randomness control 0-2 (default: 0.7) |
| `max_tokens` | Number | No | Maximum response length (default: model-specific) |
| `conversation_id` | String | No | Continue existing conversation |
| `system_prompt` | String | No | Override default system prompt |

#### Response (Non-Streaming)

```json
{
  "id": "chatcmpl-abc123",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-4o",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "Quantum computing is..."
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 15,
    "completion_tokens": 150,
    "total_tokens": 165
  },
  "routing_info": {
    "selected_model": "gpt-4o",
    "reason": "Complex technical explanation",
    "fallback_used": false
  }
}
```

#### Response (Streaming)

Server-Sent Events stream with the following event types:

```
data: {"type":"start","model":"gpt-4o"}

data: {"type":"content","content":"Quantum"}

data: {"type":"content","content":" computing"}

data: {"type":"reasoning","content":"Step 1: Understanding..."}

data: {"type":"usage","usage":{"prompt_tokens":15,"completion_tokens":150}}

data: {"type":"done"}
```

#### Error Responses

```json
{
  "error": {
    "message": "Model not available",
    "type": "model_error",
    "code": "model_unavailable",
    "status": 503
  }
}
```

### GET /api/chat/history

Retrieve chat conversation history with filtering and pagination support.

#### Request

```http
GET /api/chat/history?page=1&limit=20&search=quantum
Authorization: Bearer <api_key>
```

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | Number | No | Page number for pagination (default: 1) |
| `limit` | Number | No | Results per page, max 100 (default: 20) |
| `search` | String | No | Search conversations by content |
| `model` | String | No | Filter by model used |
| `start_date` | ISO 8601 | No | Filter conversations after date |
| `end_date` | ISO 8601 | No | Filter conversations before date |
| `sort` | String | No | Sort by: "created", "updated", "messages" (default: "updated") |
| `order` | String | No | Sort order: "asc" or "desc" (default: "desc") |
| `tags` | String | No | Comma-separated tag filter |

#### Response

```json
{
  "conversations": [
    {
      "id": "conv_abc123",
      "title": "Quantum Computing Discussion",
      "created_at": "2025-06-24T10:30:00Z",
      "updated_at": "2025-06-24T11:45:00Z",
      "message_count": 12,
      "models_used": ["gpt-4o", "o4-mini"],
      "tags": ["science", "technology"],
      "preview": "User: Explain quantum computing\nAssistant: Quantum computing is a revolutionary...",
      "metadata": {
        "total_tokens": 3456,
        "total_cost": 0.0234
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total_pages": 5,
    "total_count": 98,
    "has_next": true,
    "has_previous": false
  }
}
```

#### Get Single Conversation

```http
GET /api/chat/history/conv_abc123
Authorization: Bearer <api_key>
```

Returns full conversation with all messages:

```json
{
  "id": "conv_abc123",
  "title": "Quantum Computing Discussion",
  "messages": [
    {
      "id": "msg_1",
      "role": "user",
      "content": "Explain quantum computing",
      "timestamp": "2025-06-24T10:30:00Z"
    },
    {
      "id": "msg_2",
      "role": "assistant",
      "content": "Quantum computing is...",
      "model": "gpt-4o",
      "timestamp": "2025-06-24T10:30:15Z",
      "usage": {
        "prompt_tokens": 15,
        "completion_tokens": 150
      }
    }
  ]
}
```

## Model Management

### GET /api/models

Retrieve list of all available AI models with capabilities and pricing information.

#### Request

```http
GET /api/models?provider=openai&capability=vision
Authorization: Bearer <api_key>
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `provider` | String | Filter by provider (openai, anthropic, google, etc.) |
| `capability` | String | Filter by capability (vision, search, reasoning, code) |
| `active` | Boolean | Show only currently active models (default: true) |
| `include_pricing` | Boolean | Include detailed pricing info (default: false) |

#### Response

```json
{
  "models": [
    {
      "id": "gpt-4o",
      "name": "GPT-4 Optimized",
      "provider": "openai",
      "category": "general",
      "capabilities": ["vision", "function_calling", "json_mode"],
      "context_window": 128000,
      "max_output_tokens": 16384,
      "supports_streaming": true,
      "supports_tools": true,
      "pricing": {
        "input_cost_per_million": 2.50,
        "output_cost_per_million": 10.00,
        "currency": "USD"
      },
      "performance": {
        "average_response_time": 2.3,
        "reliability_score": 0.99
      },
      "status": "active",
      "description": "OpenAI's most capable model with vision support"
    },
    {
      "id": "o4-mini",
      "name": "O4 Mini",
      "provider": "openai",
      "category": "reasoning",
      "capabilities": ["reasoning", "complex_analysis"],
      "context_window": 128000,
      "supports_streaming": true,
      "reasoning_enabled": true,
      "pricing": {
        "input_cost_per_million": 3.00,
        "output_cost_per_million": 12.00,
        "reasoning_cost_per_million": 12.00
      },
      "status": "active"
    }
  ],
  "total_count": 182,
  "active_count": 47,
  "providers": {
    "openai": 57,
    "anthropic": 8,
    "google": 10,
    "meta": 18,
    "mistral": 7,
    "others": 82
  }
}
```

### GET /api/models/health

Check real-time health status of AI models and providers.

#### Request

```http
GET /api/models/health?check_all=false
Authorization: Bearer <api_key>
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `check_all` | Boolean | Perform live health check (expensive, default: false) |
| `model_id` | String | Check specific model health |
| `provider` | String | Check all models from provider |

#### Response

```json
{
  "timestamp": "2025-06-24T12:00:00Z",
  "overall_health": "degraded",
  "summary": {
    "healthy": 45,
    "degraded": 2,
    "down": 0,
    "unknown": 135
  },
  "models": [
    {
      "id": "gpt-4o",
      "status": "healthy",
      "response_time_ms": 234,
      "last_checked": "2025-06-24T11:58:00Z",
      "success_rate_24h": 0.997,
      "average_latency_24h": 245,
      "error_rate_24h": 0.003,
      "last_error": null
    },
    {
      "id": "claude-3-5-sonnet-20241022",
      "status": "degraded",
      "response_time_ms": 5234,
      "last_checked": "2025-06-24T11:57:00Z",
      "success_rate_24h": 0.923,
      "issues": ["High latency detected"],
      "last_error": {
        "timestamp": "2025-06-24T11:45:00Z",
        "message": "Rate limit exceeded",
        "code": "rate_limit_error"
      }
    }
  ],
  "providers": [
    {
      "name": "openai",
      "status": "operational",
      "models_active": 7,
      "uptime_percentage": 99.9
    },
    {
      "name": "anthropic",
      "status": "partial_outage",
      "models_active": 3,
      "uptime_percentage": 95.2,
      "issues": ["API latency spike"]
    }
  ]
}
```

#### Status Codes

- `healthy`: Model responding normally
- `degraded`: Slow responses or minor issues
- `down`: Model not responding
- `unknown`: No recent health data

## User Management

### GET /api/user/profile

Retrieve current user's profile information and settings.

#### Request

```http
GET /api/user/profile
Authorization: Bearer <api_key>
```

#### Response

```json
{
  "user": {
    "id": "user_123abc",
    "email": "<EMAIL>",
    "name": "Jane Smith",
    "created_at": "2025-01-15T10:00:00Z",
    "role": "premium",
    "avatar_url": "https://avatars.justsimple.chat/user_123abc.jpg"
  },
  "preferences": {
    "theme": "dark",
    "default_model": "auto",
    "streaming_enabled": true,
    "language": "en-GB",
    "timezone": "Europe/London",
    "message_density": "comfortable",
    "code_theme": "monokai",
    "show_reasoning": true,
    "auto_collapse_reasoning": true
  },
  "usage": {
    "total_messages": 1234,
    "total_tokens": 456789,
    "total_cost_usd": 12.34,
    "this_month": {
      "messages": 234,
      "tokens": 56789,
      "cost_usd": 2.34
    }
  },
  "limits": {
    "daily_token_limit": 1000000,
    "daily_tokens_used": 123456,
    "rate_limit_rpm": 100,
    "concurrent_chats": 10
  }
}
```

### PUT /api/user/profile

Update user profile information and preferences.

#### Request

```http
PUT /api/user/profile
Content-Type: application/json
Authorization: Bearer <api_key>
```

```json
{
  "name": "Jane Smith",
  "preferences": {
    "theme": "light",
    "default_model": "gpt-4o",
    "streaming_enabled": false,
    "show_reasoning": true
  }
}
```

#### Updatable Fields

| Field | Type | Validation |
|-------|------|------------|
| `name` | String | 1-100 characters |
| `preferences.theme` | String | "light", "dark", "system" |
| `preferences.default_model` | String | Valid model ID or "auto" |
| `preferences.streaming_enabled` | Boolean | - |
| `preferences.language` | String | ISO 639-1 code |
| `preferences.timezone` | String | Valid timezone identifier |
| `preferences.message_density` | String | "compact", "comfortable", "spacious" |
| `preferences.code_theme` | String | Valid highlight.js theme |
| `preferences.show_reasoning` | Boolean | - |
| `preferences.auto_collapse_reasoning` | Boolean | - |

#### Response

```json
{
  "success": true,
  "user": {
    // Updated user object
  },
  "message": "Profile updated successfully"
}
```

#### Validation Errors

```json
{
  "error": {
    "message": "Validation failed",
    "errors": [
      {
        "field": "preferences.theme",
        "message": "Invalid theme value"
      }
    ]
  }
}
```

## System Endpoints

### GET /api/health

Basic health check endpoint for monitoring and load balancers.

#### Request

```http
GET /api/health
```

No authentication required.

#### Response (Healthy)

```json
{
  "status": "healthy",
  "timestamp": "2025-06-24T12:00:00Z",
  "version": "2.5.1",
  "uptime_seconds": 864000,
  "checks": {
    "database": "ok",
    "redis": "ok",
    "litellm": "ok"
  }
}
```

#### Response (Degraded)

```json
{
  "status": "degraded",
  "timestamp": "2025-06-24T12:00:00Z",
  "version": "2.5.1",
  "issues": [
    "Redis connection slow",
    "High memory usage (85%)"
  ],
  "checks": {
    "database": "ok",
    "redis": "slow",
    "litellm": "ok"
  }
}
```

#### Status Codes

- `200 OK`: System healthy or degraded but functional
- `503 Service Unavailable`: Critical component down

### GET /api/status

Detailed system status with metrics and component health.

#### Request

```http
GET /api/status
Authorization: Bearer <api_key>
```

#### Response

```json
{
  "system": {
    "version": "2.5.1",
    "environment": "production",
    "node_version": "20.11.0",
    "uptime": {
      "seconds": 864000,
      "human_readable": "10 days"
    },
    "deployment": {
      "commit": "abc123def",
      "timestamp": "2025-06-14T10:00:00Z",
      "branch": "main"
    }
  },
  "metrics": {
    "requests_per_minute": 1234,
    "average_response_time_ms": 234,
    "active_connections": 89,
    "queue_size": 12,
    "cache_hit_rate": 0.87
  },
  "resources": {
    "cpu_usage_percent": 45.2,
    "memory": {
      "used_mb": 2048,
      "total_mb": 4096,
      "percentage": 50.0
    },
    "disk": {
      "used_gb": 45.6,
      "total_gb": 100.0,
      "percentage": 45.6
    }
  },
  "services": {
    "database": {
      "status": "healthy",
      "connections": 45,
      "pool_size": 100,
      "query_time_avg_ms": 12
    },
    "redis": {
      "status": "healthy",
      "memory_used_mb": 234,
      "connected_clients": 15,
      "ops_per_second": 5678
    },
    "litellm_proxy": {
      "status": "healthy",
      "models_loaded": 182,
      "active_requests": 23,
      "endpoint": "http://litellm-proxy-alb.amazonaws.com"
    }
  },
  "feature_flags": {
    "bedrock_router_enabled": true,
    "o_series_streaming": true,
    "web_search_enabled": true
  }
}
```

## Admin Endpoints

### GET /api/admin/providers

Retrieve detailed information about AI providers and their configuration. Requires admin role.

#### Request

```http
GET /api/admin/providers?includeHealth=true
Authorization: Bearer <admin_api_key>
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `includeHealth` | Boolean | Include live health check data (default: false) |
| `provider` | String | Filter to specific provider |

#### Response

```json
{
  "providers": [
    {
      "id": "openai",
      "name": "OpenAI",
      "status": "active",
      "api_key_configured": true,
      "api_key_masked": "sk-proj-...7HPmIpGKkNJg",
      "models_available": 57,
      "models_active": 7,
      "total_requests_24h": 12345,
      "total_cost_24h_usd": 234.56,
      "error_rate_24h": 0.002,
      "average_latency_ms": 234,
      "configuration": {
        "base_url": "https://api.openai.com/v1",
        "timeout_seconds": 60,
        "max_retries": 3,
        "rate_limit_rpm": 10000
      },
      "health": {
        "status": "healthy",
        "last_checked": "2025-06-24T12:00:00Z",
        "uptime_percentage": 99.95
      }
    },
    {
      "id": "anthropic",
      "name": "Anthropic",
      "status": "active",
      "api_key_configured": true,
      "api_key_masked": "sk-ant-...pubS7T0",
      "models_available": 8,
      "models_active": 4,
      "total_requests_24h": 5678,
      "total_cost_24h_usd": 123.45,
      "error_rate_24h": 0.005,
      "configuration": {
        "base_url": "https://api.anthropic.com/v1",
        "supports_streaming": true,
        "supports_tools": true,
        "supports_vision": true
      }
    }
  ],
  "summary": {
    "total_providers": 11,
    "active_providers": 11,
    "total_models": 182,
    "active_models": 47,
    "total_cost_24h_usd": 1234.56
  }
}
```

### POST /api/admin/cache/clear

Clear various system caches. Requires admin role.

#### Request

```http
POST /api/admin/cache/clear
Content-Type: application/json
Authorization: Bearer <admin_api_key>
```

```json
{
  "targets": ["model_list", "conversations", "user_preferences"],
  "pattern": "*",  // Optional: Redis key pattern
  "force": false   // Skip confirmation
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `targets` | Array | Yes | Cache targets to clear |
| `pattern` | String | No | Redis pattern for selective clearing (default: "*") |
| `force` | Boolean | No | Skip safety checks (default: false) |

#### Available Targets

- `model_list`: Cached model configurations
- `conversations`: Conversation history cache
- `user_preferences`: User settings cache
- `health_status`: Model health check cache
- `router_decisions`: Routing decision cache
- `all`: Clear all caches

#### Response

```json
{
  "success": true,
  "cleared": {
    "model_list": 182,
    "conversations": 456,
    "user_preferences": 89
  },
  "total_keys_cleared": 727,
  "execution_time_ms": 123,
  "message": "Cache cleared successfully"
}
```

#### Error Response

```json
{
  "error": {
    "message": "Insufficient permissions",
    "code": "auth_error",
    "required_role": "admin",
    "current_role": "user"
  }
}
```

## Error Handling

All endpoints follow consistent error response format:

```json
{
  "error": {
    "message": "Human-readable error description",
    "code": "machine_readable_code",
    "type": "error_category",
    "status": 400,
    "details": {
      // Additional context
    }
  }
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `auth_error` | 401 | Authentication failed |
| `permission_denied` | 403 | Insufficient permissions |
| `not_found` | 404 | Resource not found |
| `validation_error` | 400 | Request validation failed |
| `rate_limit_exceeded` | 429 | Too many requests |
| `model_unavailable` | 503 | AI model not responding |
| `internal_error` | 500 | Server error |

## Rate Limiting

Rate limit information included in response headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1703124000
X-RateLimit-Reset-After: 300
```

## WebSocket Support

For real-time features, WebSocket connections available at:

```
wss://justsimple.chat/api/ws
```

See [WebSocket Documentation](./websockets.md) for details.