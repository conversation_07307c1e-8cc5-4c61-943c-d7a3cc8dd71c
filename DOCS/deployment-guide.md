# Deployment Guide

## Table of Contents
- [Overview](#overview)
- [Environment Setup](#environment-setup)
- [Deployment Process](#deployment-process)
- [PM2 Management](#pm2-management)
- [Troubleshooting](#troubleshooting)
- [Important Notes](#important-notes)

## Overview

JustSimpleChat uses a manual deployment process with three environments:
- **Development**: Port 3004, develop branch
- **Staging**: Port 3005, staging branch
- **Production**: Port 3006, main branch

### Key Principles
- **ALWAYS** edit in development environment
- **NEVER** edit directly in production
- **Manual deployments only** (no CI/CD)
- **Git-based deployment** (pull, build, restart)

## Environment Setup

### Directory Structure
```
/home/<USER>/deployments/
├── dev/simplechat-ai/         # Development environment
├── staging/simplechat-ai/     # Staging environment  
├── production/simplechat-ai/  # Production environment
└── logs/                      # PM2 logs for all processes
```

### Environment Configuration

| Environment | Domain | Port | Branch | PM2 Process | Database |
|-------------|--------|------|--------|-------------|----------|
| Development | dev.justsimple.chat | 3004 | develop | simplechat-dev | justsimplechat_dev |
| Staging | staging.justsimple.chat | 3005 | staging | simplechat-staging | justsimplechat_staging |
| Production | justsimple.chat | 3006 | main | simplechat-production | justsimplechat_production |

## Deployment Process

### 1. Development Workflow

```bash
# Always start in dev directory
cd /home/<USER>/deployments/dev/simplechat-ai

# Check current branch
git branch --show-current

# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and test
npm run dev

# Commit changes
git add .
git commit -m "feat: descriptive message"
git push origin feature/your-feature-name

# Merge to main
git checkout main
git merge feature/your-feature-name
git push origin main
```

### 2. Pre-Deployment Checklist

**ALWAYS run these checks before deploying:**

```bash
# 1. TypeScript check
npm run typecheck

# 2. Lint check
npm run lint

# 3. Build test
npm run build

# 4. Test locally
npm run dev
# Test your changes in browser

# 5. Check for console errors in DevTools
# 6. Verify no sensitive data is logged
```

### 3. Deploy to Production

```bash
# Option 1: Change directory
cd /home/<USER>/deployments/production/simplechat-ai
git pull origin main
npm install --legacy-peer-deps
npm run build
pm2 restart simplechat-production

# Option 2: From dev directory (workaround)
git -C /home/<USER>/deployments/production/simplechat-ai pull origin main
npm --prefix /home/<USER>/deployments/production/simplechat-ai install --legacy-peer-deps
npm --prefix /home/<USER>/deployments/production/simplechat-ai run build
pm2 restart simplechat-production
```

### 4. Verify Deployment

```bash
# Check process status
pm2 list

# View logs
pm2 logs simplechat-production --nostream --lines 50

# Test health endpoint
curl https://justsimple.chat/api/health
```

## PM2 Management

### Essential Commands

```bash
# List all processes
pm2 list

# View logs (ALWAYS use --nostream)
pm2 logs --nostream
pm2 logs simplechat-dev --nostream
pm2 logs simplechat-production --nostream

# Restart processes
pm2 restart simplechat-dev
pm2 restart simplechat-production

# Monitor in real-time
pm2 monit

# Save PM2 configuration
pm2 save
pm2 startup
```

### Process Status

Expected PM2 processes:
```
┌─────┬──────────────────────┬────────┬─────────┬──────────┐
│ id  │ name                 │ status │ cpu     │ memory   │
├─────┼──────────────────────┼────────┼─────────┼──────────┤
│ 1   │ simplechat-dev       │ online │ 0%      │ 200MB    │
│ 2   │ simplechat-staging   │ online │ 0%      │ 180MB    │
│ 3   │ simplechat-production│ online │ 0%      │ 220MB    │
│ 14  │ litellm-proxy        │ online │ 0%      │ 150MB    │
└─────┴──────────────────────┴────────┴─────────┴──────────┘
```

## Troubleshooting

### Common Issues

1. **Build Errors**
   ```bash
   # Clear cache and rebuild
   rm -rf .next
   npm install --legacy-peer-deps
   npm run build
   ```

2. **PM2 Process Not Starting**
   ```bash
   # Check error logs
   pm2 logs simplechat-production --err --nostream
   
   # Delete and restart
   pm2 delete simplechat-production
   pm2 start ecosystem.config.js --only simplechat-production
   ```

3. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :3006
   
   # Kill process
   kill -9 <PID>
   ```

4. **Database Connection Issues**
   ```bash
   # Test MySQL connection
   mysql -h 127.0.0.1 -u root -p
   
   # Check .env file
   cat .env.local | grep DATABASE_URL
   ```

### Health Check Endpoints

```bash
# Development
curl https://dev.justsimple.chat/api/health

# Staging  
curl https://staging.justsimple.chat/api/health

# Production
curl https://justsimple.chat/api/health
```

## Important Notes

### ⚠️ Critical Warnings

1. **Never edit in production directory**
   - Always make changes in `/home/<USER>/deployments/dev/simplechat-ai`
   - Production is for deployment only

2. **Always use --legacy-peer-deps**
   - Required for npm install due to dependency conflicts
   - Example: `npm install --legacy-peer-deps`

3. **Database Sharing**
   - Dev uses same database as production for speed
   - Be careful with database operations in dev

4. **Branch Management**
   - Development: `develop` branch (or feature branches)
   - Production: `main` branch only
   - Always check branch before editing: `git branch --show-current`

### 📝 Deployment Checklist

- [ ] All changes tested in development
- [ ] TypeScript check passes (`npm run typecheck`)
- [ ] Lint check passes (`npm run lint`)
- [ ] Build succeeds (`npm run build`)
- [ ] No console errors in browser
- [ ] No sensitive data in logs
- [ ] Feature branch merged to main
- [ ] Production pulled latest from main
- [ ] Dependencies installed with `--legacy-peer-deps`
- [ ] Build completed successfully
- [ ] PM2 process restarted
- [ ] Health check returns 200 OK
- [ ] Feature working in production

## Quick Commands Reference

```bash
# Development cycle
cd /home/<USER>/deployments/dev/simplechat-ai
git checkout -b feature/xyz
# ... make changes ...
npm run dev  # Test locally
npm run typecheck && npm run lint && npm run build
git add . && git commit -m "feat: description"
git checkout main && git merge feature/xyz && git push

# Production deployment
cd /home/<USER>/deployments/production/simplechat-ai
git pull origin main && npm install --legacy-peer-deps && npm run build && pm2 restart simplechat-production

# Check status
pm2 list
pm2 logs simplechat-production --nostream --lines 30
curl https://justsimple.chat/api/health
```

---
*Last Updated: June 24, 2025*