# Environment Configuration Documentation

## Table of Contents
- [Overview](#overview)
- [API Keys](#api-keys)
- [Database Configuration](#database-configuration)
- [Service URLs](#service-urls)
- [Environment Files](#environment-files)
- [Security Notes](#security-notes)

## Overview

JustSimpleChat uses environment-specific configuration files to manage API keys, database connections, and service endpoints across development, staging, and production environments.

## API Keys

### Synchronized Across All Environments

All environments use the same API keys for consistency:

| Provider | Key Pattern | Models | Features |
|----------|-------------|---------|----------|
| **OpenAI** | `sk-proj-H1VUZEcryxD9hHq5Xov...` | GPT-4, GPT-3.5, O-series | Vision, Function calling, JSON mode |
| **Anthropic** | `sk-ant-api03-zew_2JJfCkqreav6pubS7T0...` | Claude 3.5, <PERSON> 3 | Vision, Tool use, Caching |
| **Google** | `AIzaSyCk367PTr68fJiqy3idip_7HPmIpGKkNJg` | Gemini Pro, Flash | Vision, Function calling |
| **OpenRouter** | `sk-or-v1-7740ab561d6fa0d468e8bc8...` | 200+ models | Universal access, Auto routing |
| **Groq** | `gsk_eExYvSvzOntNxFMLkWczWGdyb3FYW0UiUkLAu0O8...` | Llama, Mixtral | Hardware acceleration |
| **DeepSeek** | `***********************************` | DeepSeek V3 | Reasoning, Code generation |
| **Mistral** | `UyX0HmPQi6JTeIGcWLkDZ8q4n1VXnRi7` | Mistral Large, Medium | Multilingual, Code |
| **xAI** | `xai-FZjOGR7ZcGsJ3X2nYpM8Q1KvE4wC6TdLhBmAuR9...` | Grok models | Reasoning, Web search |

### Special Service Keys

| Service | Key | Purpose |
|---------|-----|---------|
| **Brave Search** | `BSA...` | Web search API |
| **LiteLLM Master** | `sk-simplechat-master-2025` | Proxy authentication |
| **SendGrid** | `SG...` | Email notifications |

## Database Configuration

### MySQL Databases

| Environment | Database Name | Host | Port | User |
|-------------|---------------|------|------|------|
| Development | `justsimplechat_dev` | 127.0.0.1 | 3306 | root |
| Staging | `justsimplechat_staging` | 127.0.0.1 | 3306 | root |
| Production | `justsimplechat_production` | 127.0.0.1 | 3306 | root |

### Redis Cache
- **Host**: localhost
- **Port**: 6379
- **Shared**: Single instance across all environments

### Connection Strings
```bash
# Format
DATABASE_URL="mysql://user:password@host:port/database"

# Example
DATABASE_URL="mysql://root:password@127.0.0.1:3306/justsimplechat_dev"
```

## Service URLs

### External Services

| Service | URL | Purpose |
|---------|-----|---------|
| **LiteLLM Proxy** | `http://litellm-proxy-alb-1992456982.us-east-1.elb.amazonaws.com/v1` | AI model proxy |
| **OpenAI API** | `https://api.openai.com/v1` | Direct OpenAI access |
| **Anthropic API** | `https://api.anthropic.com/v1` | Direct Claude access |
| **Google AI** | `https://generativelanguage.googleapis.com/v1beta` | Gemini models |

### Application URLs

| Environment | Public URL | Internal Port |
|-------------|------------|---------------|
| Development | `https://dev.justsimple.chat` | 3004 |
| Staging | `https://staging.justsimple.chat` | 3005 |
| Production | `https://justsimple.chat` | 3006 |

## Environment Files

### File Structure
```
/deployments/[env]/simplechat-ai/
├── .env                    # Base configuration
├── .env.local             # Secret keys (git-ignored)
└── .env.production.local  # Production overrides
```

### .env (Base Configuration)
```bash
# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=https://dev.justsimple.chat

# Database
DATABASE_URL=mysql://root:password@127.0.0.1:3306/justsimplechat_dev

# Redis
REDIS_URL=redis://localhost:6379

# Email
EMAIL_FROM=<EMAIL>
```

### .env.local (Secret Keys)
```bash
# Authentication
NEXTAUTH_SECRET=your-secret-here
NEXTAUTH_URL=https://dev.justsimple.chat

# API Keys (see table above)
OPENAI_API_KEY=sk-proj-...
ANTHROPIC_API_KEY=sk-ant-api03-...
# ... etc

# Services
LITELLM_API_KEY=sk-simplechat-master-2025
LITELLM_BASE_URL=http://litellm-proxy-alb-1992456982.us-east-1.elb.amazonaws.com/v1
```

### Environment-Specific Overrides
```bash
# Production only
NEXT_PUBLIC_APP_URL=https://justsimple.chat
DATABASE_URL=mysql://root:password@127.0.0.1:3306/justsimplechat_production
```

## Security Notes

### Best Practices

1. **Never Commit Secrets**
   - `.env.local` files are git-ignored
   - Use environment variables for sensitive data
   - Rotate keys regularly

2. **Access Control**
   - Limit database user permissions
   - Use read-only credentials where possible
   - Implement IP whitelisting for production

3. **Key Rotation**
   - Schedule regular key rotation
   - Update all environments simultaneously
   - Test after rotation

### Sensitive Files
```bash
# Files that should NEVER be committed
.env.local
.env.production.local
*.pem
*.key
.DS_Store
```

### Environment Variable Loading Order
1. `.env` (base)
2. `.env.local` (secrets)
3. `.env.[NODE_ENV]` (environment-specific)
4. `.env.[NODE_ENV].local` (environment secrets)

## Quick Reference

### Check Current Configuration
```bash
# View non-sensitive config
cat .env

# Check if secrets are loaded
node -e "console.log(process.env.OPENAI_API_KEY ? 'Loaded' : 'Missing')"

# Verify database connection
mysql -h 127.0.0.1 -u root -p -e "SHOW DATABASES;"
```

### Update Configuration
```bash
# Edit secrets
nano .env.local

# Restart to apply changes
pm2 restart simplechat-dev

# Verify changes
pm2 logs simplechat-dev --nostream | grep "API key"
```

---
*Last Updated: June 24, 2025*