# Configuration Reference

**Description**: Complete reference for all configuration options, environment variables, and settings in JustSimpleChat.

## Environment Variables

### Core Configuration

#### NODE_ENV
TODO: Environment mode
- Type: `string`
- Values: `development`, `staging`, `production`
- Default: `development`
- Description: Application environment

#### PORT
TODO: Server port
- Type: `number`
- Values: 3004 (dev), 3005 (staging), 3006 (production)
- Default: 3000
- Description: Application port

### Database Configuration

#### DATABASE_URL
TODO: MySQL connection
- Type: `string`
- Format: `mysql://user:pass@host:port/database`
- Required: Yes
- Description: Primary database connection

#### DATABASE_POOL_SIZE
TODO: Connection pool
- Type: `number`
- Default: 10
- Range: 5-100
- Description: Maximum connections

### Redis Configuration

#### REDIS_URL
TODO: Redis connection
- Type: `string`
- Format: `redis://host:port`
- Default: `redis://localhost:6379`
- Description: Cache server

### API Keys

#### Provider API Keys
TODO: AI provider keys
- `OPENAI_API_KEY` - OpenAI access
- `ANTHROPIC_API_KEY` - Anthropic access
- `GOOGLE_API_KEY` - Google AI access
- Additional provider keys...

### Feature Flags

#### ENABLE_BEDROCK_ROUTER
TODO: Router toggle
- Type: `boolean`
- Default: `true`
- Description: Enable intelligent routing

#### ENABLE_O_SERIES
TODO: O-series models
- Type: `boolean`
- Default: `true`
- Description: Enable reasoning models

## Application Settings

### Build Configuration

#### Next.js Config
TODO: Next.js settings
```javascript
// next.config.js options
module.exports = {
  // Configuration options
};
```

### TypeScript Configuration

#### Compiler Options
TODO: TypeScript settings
```json
{
  "compilerOptions": {
    // Compiler settings
  }
}
```

## Runtime Configuration

### PM2 Configuration

#### Ecosystem File
TODO: PM2 settings
```javascript
module.exports = {
  apps: [{
    // PM2 app configuration
  }]
};
```

### Nginx Configuration

#### Server Blocks
TODO: Proxy configuration
```nginx
server {
  # Nginx configuration
}
```

## Security Configuration

### CORS Settings
TODO: CORS configuration
- Allowed origins
- Allowed methods
- Credentials handling
- Header configuration

### Rate Limiting
TODO: Rate limit settings
- Request limits
- Time windows
- IP-based limits
- User-based limits

### Session Configuration
TODO: Session settings
- Session timeout
- Cookie settings
- Storage backend
- Security options

## Logging Configuration

### Log Levels
TODO: Logging settings
- Development: `debug`
- Staging: `info`
- Production: `warn`
- Error handling

### Log Outputs
TODO: Log destinations
- Console output
- File logging
- Log rotation
- External services

## Performance Configuration

### Cache Settings
TODO: Caching configuration
- TTL values
- Cache keys
- Invalidation rules
- Memory limits

### Optimization Settings
TODO: Performance tuning
- Bundle optimization
- Image optimization
- Code splitting
- Compression

## Provider Configuration

### LiteLLM Settings
TODO: LiteLLM configuration
- Model mappings
- Endpoint URLs
- Timeout values
- Retry settings

### Model Configuration
TODO: Model-specific settings
- Context windows
- Token limits
- Cost tracking
- Feature flags

## Monitoring Configuration

### Health Check Settings
TODO: Health monitoring
- Check intervals
- Timeout values
- Failure thresholds
- Alert triggers

### Metrics Collection
TODO: Metrics configuration
- Enabled metrics
- Collection interval
- Storage backend
- Retention period