# Database Schema Reference

**Description**: Complete database schema documentation including tables, relationships, indexes, and migration history.

## Database Overview

TODO: Database architecture
- MySQL configuration
- Database per environment
- Connection pooling
- Performance settings

## Core Tables

### users
TODO: User table schema
```sql
-- Table structure
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  email VARCHAR(255) UNIQUE NOT NULL,
  -- Additional fields
);
```

### conversations
TODO: Conversation storage
```sql
-- Table structure
CREATE TABLE conversations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  title VARCHAR(255),
  -- Additional fields
);
```

### messages
TODO: Message storage
```sql
-- Table structure
CREATE TABLE messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  conversation_id INT NOT NULL,
  role ENUM('user', 'assistant', 'system'),
  content TEXT,
  -- Additional fields
);
```

### model_usage
TODO: Usage tracking
```sql
-- Table structure
CREATE TABLE model_usage (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  model VARCHAR(100),
  tokens_used INT,
  -- Additional fields
);
```

## Supporting Tables

### user_preferences
TODO: User settings storage
- Preference types
- Default values
- Constraints
- Indexes

### api_keys
TODO: API key management
- Key storage
- Encryption
- Permissions
- Audit fields

### provider_health
TODO: Provider status tracking
- Health metrics
- Status history
- Alert thresholds
- Update frequency

## Relationships

### Foreign Keys
TODO: Table relationships
- User relationships
- Conversation relationships
- Message relationships
- Cascade rules

### Indexes
TODO: Performance indexes
- Primary indexes
- Search indexes
- Composite indexes
- Performance impact

## Data Types

### Custom Types
TODO: Special data types
- JSON fields
- ENUM values
- Binary data
- Timestamps

### Constraints
TODO: Data constraints
- NOT NULL fields
- UNIQUE constraints
- CHECK constraints
- Default values

## Migrations

### Migration Strategy
TODO: Schema evolution
- Migration tools
- Version tracking
- Rollback procedures
- Testing approach

### Migration History
TODO: Applied migrations
- Initial schema
- Feature additions
- Performance improvements
- Data migrations

## Performance Considerations

### Query Optimization
TODO: Performance tips
- Index usage
- Query patterns
- Denormalization
- Caching strategy

### Partitioning
TODO: Data partitioning
- Partition strategies
- Archive policies
- Performance benefits
- Management overhead

## Backup & Recovery

### Backup Strategy
TODO: Data protection
- Backup frequency
- Backup types
- Storage location
- Retention policy

### Recovery Procedures
TODO: Disaster recovery
- Recovery steps
- Point-in-time recovery
- Data validation
- Testing procedures

## Security

### Data Encryption
TODO: Security measures
- Encryption at rest
- Field encryption
- Key management
- Compliance requirements

### Access Control
TODO: Database security
- User permissions
- Role definitions
- Audit logging
- Security policies