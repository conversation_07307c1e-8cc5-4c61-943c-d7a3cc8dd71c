# Model Matrix Reference

**Description**: Comprehensive matrix of all supported AI models, their capabilities, pricing, and provider information.

## Model Overview

### Summary Statistics
- **Total Models**: 182 available through LiteLLM
- **Active Models**: 47 verified working (25.8% success rate)
- **Providers**: 11 major AI providers integrated
- **Model Categories**: General, Reasoning, Vision, Code, Search, Fast
- **Last Updated**: June 24, 2025
- **Average Response Time**: 2.3 seconds
- **Cost Range**: $0.075 - $75 per million tokens

## OpenAI Models

### GPT-4 Series
| Model | Context | Vision | Tools | JSON | Pricing (Input/Output) | Status | Notes |
|-------|---------|--------|-------|------|------------------------|---------|--------|
| gpt-4-turbo | 128K | ✅ | ✅ | ✅ | $10.00/$30.00 | ✅ Active | Latest GPT-4 Turbo with vision |
| gpt-4o | 128K | ✅ | ✅ | ✅ | $2.50/$10.00 | ✅ Active | Optimized for speed & cost |
| gpt-4o-realtime | 128K | ✅ | ✅ | ✅ | $5.00/$20.00 | ✅ Active | Real-time streaming optimized |
| gpt-4o-mini | 128K | ✅ | ✅ | ✅ | $0.15/$0.60 | ✅ Active | Most cost-effective GPT-4 |
| gpt-4-vision-preview | 128K | ✅ | ✅ | ❌ | $10.00/$30.00 | ⚠️ Deprecated | Use gpt-4o instead |
| gpt-4-32k | 32K | ❌ | ✅ | ✅ | $60.00/$120.00 | ❌ Inactive | Legacy model |

### O-Series (Reasoning)
| Model | Context | Reasoning | Stream | Pricing (In/Out/Reasoning) | Status | Best For |
|-------|---------|-----------|--------|----------------------------|---------|----------|
| o1-preview | 128K | ✅ | ✅ | $15.00/$60.00/$60.00 | ✅ Active | Complex reasoning tasks |
| o1-mini | 128K | ✅ | ✅ | $3.00/$12.00/$12.00 | ✅ Active | Budget reasoning |
| o3-mini | 200K | ✅ | ✅ | $1.10/$4.40 flat | ✅ Active | Extended context reasoning |
| o4-mini | 128K | ✅ | ✅ | $3.00/$12.00/$12.00 | ✅ Active | Latest reasoning model |

**Note**: O-series models use the Responses API for clean reasoning separation. Reasoning tokens are charged separately.

## Anthropic Models

### Claude Series
| Model | Context | Vision | Tools | Cache | Pricing (Input/Output) | Status | Strengths |
|-------|---------|--------|-------|-------|------------------------|---------|------------|
| claude-3.5-sonnet-20241022 | 200K | ✅ | ✅ | ✅ | $3.00/$15.00 | ✅ Active | Best overall Claude model |
| claude-3.5-haiku-20241022 | 200K | ✅ | ✅ | ✅ | $0.80/$4.00 | ✅ Active | Fast & affordable |
| claude-3-opus-20240229 | 200K | ✅ | ✅ | ✅ | $15.00/$75.00 | ✅ Active | Highest quality |
| claude-3-haiku-20240307 | 200K | ✅ | ✅ | ❌ | $0.25/$1.25 | ✅ Active | Legacy fast model |
| claude-2.1 | 200K | ❌ | ✅ | ❌ | $8.00/$24.00 | ❌ Deprecated | Use Claude 3 |

**Note**: Claude models support prompt caching for reduced costs on repeated content.

## Google Models

### Gemini Series
| Model | Context | Vision | Tools | Code Exec | Pricing (Input/Output) | Status | Use Case |
|-------|---------|--------|-------|-----------|------------------------|---------|----------|
| gemini-2.5-flash | 1M | ✅ | ✅ | ✅ | $0.075/$0.30 | ✅ Active | Router model, ultra-fast |
| gemini-2.5-flash-8b | 1M | ✅ | ✅ | ❌ | $0.0375/$0.15 | ✅ Active | Cheapest option |
| gemini-2.5-pro | 2M | ✅ | ✅ | ✅ | $1.25/$5.00 | ✅ Active | Long context tasks |
| gemini-1.5-flash | 1M | ✅ | ✅ | ✅ | $0.075/$0.30 | ✅ Active | Legacy flash model |
| gemini-1.5-pro | 2M | ✅ | ✅ | ✅ | $1.25/$5.00 | ✅ Active | Legacy pro model |
| gemini-1.0-pro | 32K | ❌ | ✅ | ❌ | $0.50/$1.50 | ❌ Deprecated | Use Gemini 1.5+ |

**Note**: Gemini models excel at extremely long context windows up to 2M tokens.

## Meta/Groq Models

### Llama Series (via Groq)
| Model | Context | Speed | Vision | Pricing (Input/Output) | Status | Performance |
|-------|---------|-------|--------|------------------------|---------|-------------|
| llama-3.3-70b-versatile | 128K | ⚡ Ultra | ❌ | $0.59/$0.79 | ✅ Active | 250+ tokens/sec |
| llama-3.2-90b-vision | 128K | ⚡ Fast | ✅ | $0.90/$0.90 | ✅ Active | Vision capable |
| llama-3.1-70b-versatile | 128K | ⚡ Ultra | ❌ | $0.59/$0.79 | ✅ Active | Best speed/quality |
| llama-3.1-405b | 128K | 🚀 Fast | ❌ | $3.00/$3.00 | ❌ Limited | Largest Llama |
| llama-3.1-8b | 128K | ⚡ Ultra | ❌ | $0.05/$0.08 | ✅ Active | Fastest inference |

**Note**: Groq hardware acceleration provides 10x faster inference than standard deployments.

## xAI Models

### Grok Series (xAI)
| Model | Context | Vision | Web Search | Reasoning | Pricing (Input/Output) | Status | Features |
|-------|---------|--------|------------|-----------|------------------------|---------|----------|
| grok-2-vision-1212 | 128K | ✅ | ✅ | ✅ | $2.00/$10.00 | ✅ Active | Latest vision model |
| grok-2-1212 | 128K | ❌ | ✅ | ✅ | $2.00/$10.00 | ✅ Active | Web-aware AI |
| grok-beta | 128K | ❌ | ✅ | ✅ | $5.00/$15.00 | ✅ Active | Advanced reasoning |
| grok-vision-beta | 32K | ✅ | ✅ | ✅ | $5.00/$15.00 | ❌ Deprecated | Use grok-2-vision |

**Note**: Grok models have built-in web search and reasoning content generation.

## Mistral Models

### Mistral Series
| Model | Context | Code | Languages | Tools | Pricing (Input/Output) | Status | Specialty |
|-------|---------|------|-----------|-------|------------------------|---------|------------|
| mistral-large-2411 | 128K | ✅ | 80+ | ✅ | $2.00/$6.00 | ✅ Active | Best overall |
| codestral-2405 | 256K | ✅ | 80+ | ✅ | $0.20/$0.60 | ✅ Active | Code generation |
| mistral-small-2409 | 32K | ✅ | 5 | ✅ | $0.20/$0.60 | ✅ Active | Fast & cheap |
| ministral-8b-2410 | 128K | ✅ | 9 | ❌ | $0.10/$0.10 | ✅ Active | Edge deployment |
| ministral-3b-2410 | 128K | ❌ | 5 | ❌ | $0.04/$0.04 | ✅ Active | Ultra lightweight |
| pixtral-large-2411 | 128K | ✅ | 80+ | ✅ | $2.00/$6.00 | ✅ Active | Multimodal |

**Note**: Mistral models excel at multilingual tasks and code generation.

## Additional Providers

### DeepSeek Models
| Model | Context | Code | Math | Pricing (Input/Output) | Status | Specialty |
|-------|---------|------|------|------------------------|---------|------------|
| deepseek-chat | 128K | ✅ | ✅ | $0.07/$0.28 | ✅ Active | General purpose |
| deepseek-reasoner | 64K | ✅ | ✅ | $0.55/$2.19 | ✅ Active | Advanced reasoning |

### Cohere Models
| Model | Context | Tools | Citations | Languages | Pricing | Status |
|-------|---------|-------|-----------|-----------|---------|---------||
| command-r-plus-08-2024 | 128K | ✅ | ✅ | 10+ | $2.50/$10.00 | ✅ Active |
| command-r-plus | 128K | ✅ | ✅ | 10+ | $3.00/$15.00 | ✅ Active |
| command-r | 128K | ✅ | ✅ | 10+ | $0.50/$1.50 | ✅ Active |
| command-light | 4K | ❌ | ❌ | 10+ | $0.15/$0.60 | ✅ Active |

### Qwen Models (Alibaba)
| Model | Context | Vision | Math | Code | Pricing | Status |
|-------|---------|--------|------|------|---------|---------||
| qwen-plus | 128K | ❌ | ✅ | ✅ | $0.50/$1.00 | ✅ Active |
| qwen-turbo | 128K | ❌ | ✅ | ✅ | $0.10/$0.30 | ✅ Active |
| qwen-vl-plus | 128K | ✅ | ✅ | ✅ | $0.80/$1.60 | ✅ Active |
| qwen-vl-max | 32K | ✅ | ✅ | ✅ | $2.00/$4.00 | ✅ Active |
| qwen-math-plus | 128K | ❌ | ✅ | ❌ | $0.50/$1.00 | ✅ Active |

### Perplexity Models
| Model | Context | Search | Citations | Pricing | Status |
|-------|---------|--------|-----------|---------|---------||
| sonar-pro | 200K | ✅ | ✅ | $5.00/$5.00 | ✅ Active |
| sonar | 127K | ✅ | ✅ | $1.00/$1.00 | ✅ Active |

## Model Capabilities Summary

### Vision Support
**✅ Vision-Capable Models (23 total)**
- **OpenAI**: GPT-4o, GPT-4o-mini, GPT-4-turbo
- **Anthropic**: All Claude-3 and Claude-3.5 models
- **Google**: All Gemini models (6 models)
- **xAI**: Grok-2-vision-1212
- **Meta**: Llama-3.2-90b-vision
- **Qwen**: qwen-vl-plus, qwen-vl-max
- **Mistral**: pixtral-large-2411

### Tool Use / Function Calling
**✅ Tool-Capable Models (35+ total)**
- **OpenAI**: All GPT models support function calling
- **Anthropic**: All Claude models with advanced tool use
- **Google**: All Gemini models with code execution
- **Mistral**: mistral-large, codestral, mistral-small
- **Cohere**: All Command R models with grounded generation

### Web Search Integration
**✅ Search-Enabled Models (6 total)**
- **Perplexity**: sonar-pro, sonar (with citations)
- **xAI**: All Grok models (real-time web access)
- **Cohere**: Command R models (with citations)

### Reasoning Capabilities
**🧠 Advanced Reasoning Models (7 total)**
- **OpenAI O-Series**: o1-preview, o1-mini, o3-mini, o4-mini
- **xAI**: All Grok models include reasoning content
- **DeepSeek**: deepseek-reasoner
- **Features**: Step-by-step thinking, complex problem solving

### Code Generation Specialists
**💻 Code-Optimized Models (12 total)**
- **Mistral**: codestral-2405 (256K context)
- **DeepSeek**: All models excel at code
- **Qwen**: qwen-plus, qwen-turbo
- **OpenAI**: All GPT models
- **Anthropic**: Claude models with strong coding

### Cost-Effective Options
**💰 Budget Models (< $1/M tokens)**
| Model | Input/Output Cost | Provider | Best For |
|-------|-------------------|----------|----------|
| gemini-2.5-flash-8b | $0.0375/$0.15 | Google | Ultra cheap |
| ministral-3b-2410 | $0.04/$0.04 | Mistral | Edge cases |
| llama-3.1-8b | $0.05/$0.08 | Groq | Fast inference |
| gpt-4o-mini | $0.15/$0.60 | OpenAI | Quality + cost |
| qwen-turbo | $0.10/$0.30 | Alibaba | Asian languages |

### Performance Leaders
**🚀 Fastest Models (via hardware acceleration)**
- **Groq Llama**: 250+ tokens/second
- **Gemini Flash**: < 1s first token latency
- **Mistral Small**: Optimized for speed

### Long Context Champions
**📚 Extended Context Models**
| Model | Context Window | Provider |
|-------|---------------|----------|
| Gemini 2.5 Pro | 2M tokens | Google |
| Gemini 1.5 Pro | 2M tokens | Google |
| Gemini Flash | 1M tokens | Google |
| Mistral Codestral | 256K tokens | Mistral |
| Claude 3.5 | 200K tokens | Anthropic |
| Perplexity Sonar | 200K tokens | Perplexity |

## Model Selection Guide

### By Use Case
1. **General Chat**: GPT-4o, Claude 3.5 Sonnet, Gemini Pro
2. **Reasoning Tasks**: O-series models, Grok, DeepSeek Reasoner
3. **Code Generation**: Codestral, GPT-4o, Claude 3.5 Sonnet
4. **Image Analysis**: GPT-4o, Claude 3.5, Gemini Pro
5. **Web Search**: Perplexity Sonar, Grok models
6. **Fast Responses**: Groq Llama, Gemini Flash, GPT-4o-mini
7. **Long Documents**: Gemini models (up to 2M tokens)
8. **Multilingual**: Mistral, Qwen, Cohere Command R

### By Budget
- **Premium** ($10+ per M): Claude Opus, O-series, GPT-4-turbo
- **Standard** ($1-10 per M): GPT-4o, Claude Sonnet, Gemini Pro
- **Budget** (< $1 per M): GPT-4o-mini, Gemini Flash, Llama models

### By Provider Strengths
- **OpenAI**: Most versatile, best tooling
- **Anthropic**: Safety, nuanced responses
- **Google**: Longest context, vision excellence
- **Groq**: Fastest inference speeds
- **Mistral**: Multilingual, code generation
- **xAI**: Web search, reasoning content
- **Perplexity**: Real-time information with sources

## Router Configuration

The intelligent router (Gemini 2.5 Flash Lite) considers:
1. **Query Complexity**: Simple vs complex reasoning
2. **Task Type**: Chat, code, analysis, creative
3. **Cost Constraints**: Optimizes for budget
4. **Model Availability**: Automatic failover
5. **Special Requirements**: Vision, search, tools

Manual override always available for specific model preferences.

## Pricing Notes

- Prices shown as Input/Output per million tokens
- Some models have additional charges (reasoning tokens, cache hits)
- Prices subject to change - check provider websites
- Volume discounts may apply for enterprise usage

---

**Last Updated**: June 24, 2025  
**Total Models**: 182 in LiteLLM configuration  
**Working Models**: 47 verified and tested  
**Success Rate**: 25.8% (many models require specific setup)