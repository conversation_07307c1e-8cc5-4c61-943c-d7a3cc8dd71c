# Web Search System Documentation

## Table of Contents
- [Overview](#overview)
- [Components](#components)
- [Flow Diagram](#flow-diagram)
- [Four Scenarios Analysis](#four-scenarios-analysis)
- [Implementation Details](#implementation-details)
- [UI Components](#ui-components)
- [API Endpoints](#api-endpoints)
- [Recent Changes](#recent-changes)

## Overview

The web search system in JustSimpleChat provides real-time information retrieval using the Brave Search API. It features intelligent routing that can automatically detect when web search is needed, even overriding user preferences for better UX.

### Key Features
- **Smart Override Logic**: Router can trigger web search for time-sensitive queries even when toggle is OFF
- **Native Model Support**: Some models (Grok, Perplexity) have built-in web search
- **Condensed Sources Display**: Clean UI showing source domains above messages
- **Router-Generated Queries**: AI generates optimal search queries based on context

## Components

### 1. Web Search Toggle (`/src/components/chat/web-search-button.tsx`)
- User-controlled on/off switch in chat input
- State managed in `chat-interface.tsx` as `webSearchEnabled`
- Visual indicator with glow effect when enabled

### 2. Router Intelligence (`/src/lib/ai/bedrock-router-enhanced-v2.ts`)
- **Context-Aware Detection** (Updated June 24, 2025)
  - LLM analyzes semantic intent, not just keywords
  - Understands follow-up questions ("Anything else?" doesn't trigger search)
  - Distinguishes between conversational "today" vs. real-time needs
- **Search Query Generation Rules**:
  - ONLY for genuine current information needs
  - Avoids searches for: general knowledge, coding, math, opinions
  - Context-aware: "How are you today?" → No search
  - Direct queries: "What's in the news today?" → Generates search

### 3. API Route Handler (`/src/app/api/chat/route.ts`)
- **Override Logic** (lines 782-788):
```typescript
const routerDetectedRealtimeNeeds = routerDecision?.searchQueries && 
                                   routerDecision.searchQueries.length > 0;

if ((webSearchEnabled || routerDetectedRealtimeNeeds) && canUseWebSearch) {
  // Perform web search
}
```

## Flow Diagram

```
┌─────────────────┐
│ User Message    │
└────────┬────────┘
         │
         ▼
┌─────────────────────────────┐
│ Router Analysis             │
│ • Detect real-time needs?   │
│ • Check web search toggle   │
└──────────┬──────────────────┘
           │
           ▼
┌──────────────────────────────────────┐
│ Decision Logic:                      │
│ IF (webSearchEnabled=true) OR        │
│    (router detects real-time need)   │
│ THEN → Generate search queries       │
└──────────┬───────────────────────────┘
           │
           ▼
┌─────────────────────┐     ┌────────────────────┐
│ Native Search Model?│ NO  │ Call Web Search API│
│ (Grok, Perplexity) ├────▶│ (/api/web-search)  │
└──────────┬──────────┘     └────────────────────┘
           │ YES
           ▼
┌─────────────────────┐
│ Use Model's Native  │
│ Search Capability   │
└─────────────────────┘
```

## Four Scenarios Analysis

### Scenario 1: "Hello" + Web Search OFF
- **Input**: Simple greeting
- **Toggle**: OFF ❌
- **Router Decision**: No real-time info needed
- **Result**: Normal response, NO web search

### Scenario 2: "Hello" + Web Search ON
- **Input**: Simple greeting
- **Toggle**: ON ✅
- **Router Decision**: No real-time info needed (smart filtering)
- **Result**: Normal response, NO web search

### Scenario 3: "What's in the news today?" + Web Search OFF
- **Input**: Time-sensitive query
- **Toggle**: OFF ❌
- **Router Decision**: LLM recognizes genuine need for current information
- **Result**: Web search PERFORMED (router override) ✅

### Additional Examples (Context-Aware):

#### "How are you today?" + Web Search ON
- **Input**: Conversational greeting
- **Toggle**: ON ✅
- **Router Decision**: LLM understands this is a greeting, not a time query
- **Result**: NO web search (context awareness)

#### News query followed by "Anything else?"
- **First**: "What's happening in the world today?" → Web search performed
- **Follow-up**: "Anything else?" 
- **Router Decision**: Recognizes as follow-up, not new information request
- **Result**: NO web search for follow-up

### Scenario 4: "What's in the news today?" + Web Search ON
- **Input**: Time-sensitive query
- **Toggle**: ON ✅
- **Router Decision**: Detects real-time need
- **Result**: Web search performed ✅

## Implementation Details

### Web Search API (`/src/app/api/web-search/route.ts`)
- **Endpoint**: POST `/api/web-search`
- **Authentication**: Currently bypassed for testing (line 36)
- **Process**:
  1. Receives search queries or generates them
  2. Calls Brave Search API
  3. Fetches and summarizes content
  4. Returns formatted results

### Search Query Generation
- **Router-generated**: When router detects need (preferred)
- **Fallback**: Uses GPT-4o-mini to generate query from user message
- **Format**: Array of 1-3 specific search queries

### Native Search Models
Models with built-in web search (no separate API call needed):
- `grok-*` (xAI models)
- `perplexity-*` models
- `deepseek-*` models with web capability

## UI Components

### 1. CondensedSources (`/src/components/chat/condensed-sources.tsx`)
- **Location**: Above message content
- **Display**: Compact domain pills
- **Features**: 
  - Clickable links to sources
  - Favicon display
  - Hover effects

### 2. Removed Components
- **GlobalWebSearchIndicator**: Removed (was bottom-left green box)
- **WebSearchIndicator**: Import removed, component deprecated
- **Search indicator in smooth-streaming-message**: Removed (lines 92-115)

## API Endpoints

### `/api/chat`
- **Web Search Parameters**:
  - `webSearchEnabled`: Boolean from UI toggle
  - Router adds `searchQueries` to decision

### `/api/web-search`
- **Input**: 
  ```json
  {
    "queries": ["search query 1", "search query 2"],
    "userMessage": "original user message"
  }
  ```
- **Output**: Formatted search results with sources

## Recent Changes

### June 23, 2025
1. **Fixed Router Configuration**:
   - Changed from Google API to LiteLLM proxy
   - Updated model from `gemini-2.5-flash-preview` to `gemini-2.0-flash`
   - Fixed JSON parsing issues with markdown wrapper stripping

2. **UI Cleanup**:
   - Removed green web search indicator (bottom-left)
   - Removed web search box from streaming messages
   - Added condensed sources display above messages

3. **Authentication**:
   - Temporarily bypassed auth for web search endpoint (testing)
   - Enabled web search for FREE plan users (testing)

### Configuration Changes
```typescript
// Before:
private baseURL = 'https://generativelanguage.googleapis.com/v1beta';
private readonly ROUTER_MODEL = 'models/gemini-2.5-flash-preview-04-17';

// After:
private baseURL = process.env.LITELLM_BASE_URL || 'http://litellm-proxy-alb-1992456982.us-east-1.elb.amazonaws.com/v1';
private readonly ROUTER_MODEL = 'gemini/gemini-2.0-flash';
```

## Troubleshooting

### Common Issues
1. **Empty router responses**: Check if model supports response_format parameter
2. **No search results**: Verify Brave API key is set
3. **Search not triggering**: Check router logs for decision reasoning

### Debug Points
- Router decision: Check `routerDecision.searchQueries` in route.ts logs
- Real-time detection: Log output of `detectRealtimeInfoNeeds()` function
- API calls: Monitor `/api/web-search` endpoint logs

---
*Last Updated: June 24, 2025*