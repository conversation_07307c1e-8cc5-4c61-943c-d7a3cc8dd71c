# LiteLLM Integration Documentation

## Table of Contents
- [Overview](#overview)
- [Configuration](#configuration)
- [Model Management](#model-management)
- [API Usage](#api-usage)
- [Failover & Load Balancing](#failover--load-balancing)
- [Troubleshooting](#troubleshooting)

## Overview

LiteLLM serves as a unified proxy for all AI model providers in JustSimpleChat. It standardizes different provider APIs into a single OpenAI-compatible interface.

### Key Benefits
- Single API format for 180+ models
- Unified error handling
- Cost tracking across providers
- Built-in retry logic
- Model fallbacks

### Architecture
```
Client Request → LiteLLM Proxy → Provider API (OpenAI, Anthropic, etc.)
                     ↓
                Model Database
                     ↓
                Rate Limiting
```

## Configuration

### Environment Variables
```bash
# LiteLLM Proxy Endpoint
LITELLM_BASE_URL=http://litellm-proxy-alb-**********.us-east-1.elb.amazonaws.com/v1

# Master API Key
LITELLM_API_KEY=sk-simplechat-master-2025

# Database Configuration (in LiteLLM)
STORE_MODEL_IN_DB=True
DATABASE_URL=postgresql://...
```

### Proxy Settings
- **Mode**: Database-only (no YAML config)
- **Task Definition**: `litellm-proxy:6`
- **Health Check**: `/health` endpoint
- **Port**: 4000 (internal)

## Model Management

### Adding Models via API
```bash
curl -X POST http://litellm-proxy/model/new \
  -H "Authorization: Bearer sk-simplechat-master-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "model_name": "gpt-4-turbo",
    "litellm_params": {
      "model": "gpt-4-turbo-preview",
      "api_key": "sk-proj-..."
    },
    "model_info": {
      "mode": "chat",
      "supports_vision": true
    }
  }'
```

### Model Format in Database
```json
{
  "model_name": "claude-3-5-sonnet-20241022",
  "litellm_params": {
    "model": "claude-3-5-sonnet-20241022",
    "api_key": "sk-ant-api03-...",
    "api_base": "https://api.anthropic.com/v1"
  },
  "model_info": {
    "mode": "chat",
    "supports_function_calling": true,
    "supports_vision": true,
    "max_tokens": 200000
  }
}
```

### Provider-Specific Configurations

#### OpenAI Models
```json
{
  "model": "gpt-4-turbo-preview",
  "api_key": "sk-proj-...",
  "organization": "org-..." // Optional
}
```

#### Anthropic Models
```json
{
  "model": "claude-3-5-sonnet-20241022",
  "api_key": "sk-ant-api03-...",
  "anthropic_version": "2023-06-01"
}
```

#### Google Models (via LiteLLM)
```json
{
  "model": "gemini/gemini-2.0-flash",
  "api_key": "AIzaSy...",
  "safety_settings": [...] // Optional
}
```

## API Usage

### Standard Chat Completion
```typescript
const response = await fetch(`${LITELLM_BASE_URL}/chat/completions`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${LITELLM_API_KEY}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: 'gpt-4-turbo',
    messages: [
      { role: 'user', content: 'Hello' }
    ],
    stream: true
  })
});
```

### O-Series Models (Responses API)
```typescript
// For O-series models, use different endpoint
const endpoint = shouldUseResponsesAPI(model) 
  ? '/chat/completions/responses' 
  : '/chat/completions';

const response = await fetch(`${LITELLM_BASE_URL}${endpoint}`, {
  // ... headers ...
  body: JSON.stringify({
    model: 'o4-mini',
    messages: [...],
    reasoning: { summary: 'auto' }, // O-series specific
    stream: true
  })
});
```

### Streaming Response Handling
```typescript
const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.slice(6));
      // Handle streaming data
    }
  }
}
```

## Failover & Load Balancing

### Model Naming for Failover
```python
# Same model_name = Load balancing (round-robin)
model_name: "gpt-4-turbo"
model_name: "gpt-4-turbo"  # Both will be used

# Different model_name = True failover
model_name: "gpt-4-turbo"
model_name: "gpt-4-turbo-fallback"  # Only used if first fails
```

### Current Failover Setup
1. **Primary**: Native provider (OpenAI, Anthropic, etc.)
2. **Fallback**: OpenRouter (for most models)
3. **Application Retry**: 3 attempts with exponential backoff

### Health Checks
- **Status**: Currently DISABLED for cost optimization
- **Alternative**: Reactive failover on request failure
- **Manual Check**: `/model/info` endpoint

## Troubleshooting

### Common Issues

1. **Model Not Found**
   ```json
   {
     "error": {
       "message": "Model not found",
       "type": "invalid_request_error"
     }
   }
   ```
   **Solution**: Check model exists in database, verify exact model name

2. **Authentication Error**
   ```json
   {
     "error": {
       "message": "Incorrect API key provided",
       "type": "authentication_error"
     }
   }
   ```
   **Solution**: Verify LITELLM_API_KEY is correct

3. **Rate Limiting**
   ```json
   {
     "error": {
       "message": "Rate limit exceeded",
       "type": "rate_limit_error"
     }
   }
   ```
   **Solution**: Implement backoff, check provider limits

### Debug Endpoints

```bash
# List all models
curl http://litellm-proxy/model/info \
  -H "Authorization: Bearer sk-simplechat-master-2025"

# Health check
curl http://litellm-proxy/health

# Test specific model
curl -X POST http://litellm-proxy/chat/completions \
  -H "Authorization: Bearer sk-simplechat-master-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4-turbo",
    "messages": [{"role": "user", "content": "test"}],
    "max_tokens": 10
  }'
```

### Logs

```bash
# View LiteLLM logs
pm2 logs litellm-proxy --nostream

# Check for specific errors
pm2 logs litellm-proxy --nostream | grep -i error

# Database queries
pm2 logs litellm-proxy --nostream | grep -i "model_name"
```

## Model Migration Script

Used to migrate all models to LiteLLM (June 21, 2025):
```python
# Example model addition
models = [
  {
    "model_name": "gpt-4-turbo",
    "litellm_params": {
      "model": "gpt-4-turbo-preview",
      "api_key": os.getenv("OPENAI_API_KEY")
    }
  },
  # ... more models
]

for model in models:
  requests.post(
    f"{PROXY_BASE_URL}/model/new",
    headers={"Authorization": f"Bearer {MASTER_KEY}"},
    json=model
  )
```

## Best Practices

1. **Model Naming**
   - Use consistent names across environments
   - Include provider prefix for clarity (e.g., `openai/gpt-4`)

2. **Error Handling**
   - Always implement retry logic
   - Log errors with context
   - Provide user-friendly error messages

3. **Performance**
   - Use streaming for long responses
   - Implement request timeouts
   - Cache model information

4. **Security**
   - Never expose LITELLM_API_KEY to clients
   - Validate model names before requests
   - Monitor for unusual usage patterns

---
*Last Updated: June 24, 2025*