# Architecture Overview

**Description**: High-level overview of the JustSimpleChat architecture, including core components, design principles, and system boundaries.

## System Components

### Frontend Layer
- **React 19 Application**: Latest React with Server Components, Actions, and Suspense
- **Next.js 15 Framework**: App Router, streaming SSR, and edge runtime support
- **UI Components**: Modular component library with shadcn/ui base
- **State Management**: React Context API with optimistic updates
- **Real-time Updates**: Server-Sent Events for streaming responses

### API Layer
- **Next.js API Routes**: RESTful endpoints with TypeScript validation
- **Authentication**: NextAuth.js with JWT sessions
- **Request Processing**: Middleware for auth, rate limiting, and logging
- **Response Streaming**: SSE implementation for progressive AI responses

### Integration Layer
- **LiteLLM Proxy**: Unified interface to 180+ AI models
- **Model Router**: Intelligent selection using Gemini 2.5 Flash Lite
- **Provider Adapters**: Normalised interfaces for each AI provider
- **Fallback System**: Multi-tier failover for high availability

### Data Layer
- **MySQL Database**: Primary data store for users, conversations, and metadata
- **Redis Cache**: Session storage, rate limiting, and response caching
- **File Storage**: Local filesystem for uploaded images
- **Configuration**: Environment-based settings with .env files

### Infrastructure Layer
- **PM2 Process Manager**: Zero-downtime deployments and monitoring
- **Nginx Reverse Proxy**: SSL termination and request routing
- **Cloudflare CDN**: Global content delivery and DDoS protection
- **AWS EC2**: Scalable compute instances

## System Architecture Diagram

The following diagram illustrates the complete system architecture, showing all components, data flows, and external integrations:

![JustSimpleChat System Architecture](../../system_diagram.svg)

**Key Features:**
- **6 Distinct Layers**: Frontend, API, Core Services, Data, AI Providers, External Systems
- **182+ AI Models**: Across 11 providers with intelligent routing
- **Multi-Environment Setup**: Development, staging, and production deployments
- **Real-time Data Flow**: Numbered flow indicators showing request processing
- **Comprehensive Integration**: External systems including Stripe, SendGrid, and OAuth

For technical details about the diagram components and export instructions, see [Diagram Export Instructions](./diagram-export-instructions.md).

## Design Principles

### Scalability
- **Horizontal Scaling**: Stateless API design enables multi-instance deployment
- **Database Connection Pooling**: Efficient resource utilisation
- **Caching Strategy**: Multi-level caching reduces database load
- **Async Processing**: Non-blocking operations for AI model calls
- **Load Balancing**: Distribute requests across multiple instances

### Maintainability
- **Modular Architecture**: Clear separation of concerns
- **TypeScript Throughout**: Type safety and IDE support
- **Comprehensive Testing**: Unit, integration, and E2E test suites
- **Documentation First**: Code documentation and API specs
- **Feature Flags**: Gradual rollout and A/B testing capability

### Security
- **API Key Management**: Encrypted storage with environment isolation
- **Authentication**: Industry-standard JWT implementation
- **Input Validation**: Strict validation at all entry points
- **Rate Limiting**: Protection against abuse and DDoS
- **HTTPS Only**: End-to-end encryption for all communications
- **Content Security Policy**: XSS and injection protection

### Performance
- **Streaming Responses**: Progressive rendering for better UX
- **Intelligent Caching**: Cache AI responses where appropriate
- **Database Indexing**: Optimised queries for common operations
- **CDN Integration**: Static asset delivery at edge locations
- **Code Splitting**: Lazy loading for faster initial load
- **React Server Components**: Reduced client-side JavaScript

## Technology Stack

### Frontend Technologies
- **React 19.1.0**: Latest features including Actions and use() hook
- **Next.js 15.3.3**: App Router for better performance and DX
- **TypeScript 5.x**: Type safety and better tooling
- **Tailwind CSS**: Utility-first styling with custom design system
- **Radix UI**: Accessible component primitives
- **Framer Motion**: Smooth animations and transitions

### Backend Technologies
- **Node.js 20.x**: LTS version for stability
- **MySQL 8.0**: ACID-compliant relational database
- **Redis 7.x**: In-memory data structure store
- **PM2 5.x**: Advanced process management
- **LiteLLM**: Multi-provider AI gateway

### AI Provider Integration
- **OpenAI API**: GPT-4, O-series, and DALL-E models
- **Anthropic API**: Claude family of models
- **Google AI**: Gemini and PaLM models
- **Meta/Groq**: Open-source model inference
- **Custom Providers**: Extensible provider system

## System Boundaries

### Internal Systems
- Web application frontend
- API backend services
- Database and cache layers
- Authentication system
- File upload handling

### External Dependencies
- **AI Provider APIs**: OpenAI, Anthropic, Google, etc.
- **LiteLLM Proxy**: Managed service for model access
- **Cloudflare**: CDN and security services
- **npm Registry**: Package dependencies
- **GitHub**: Source control and CI/CD

### Integration Points
- REST API for client applications
- WebSocket for real-time features
- Webhook endpoints for provider callbacks
- Health check endpoints for monitoring
- Admin API for system management

## Architecture Patterns

### Request Flow
```
User Request → Cloudflare → Nginx → Next.js → API Route → LiteLLM → AI Provider
     ↑                                          ↓
     └──────────── Streaming Response ──────────┘
```

### Data Flow
```
User Input → Validation → Router Decision → Model Selection → AI Processing
     ↓                                                            ↓
  Database ← Response Processing ← Stream Transformation ← AI Response
```

### Caching Strategy
```
Level 1: Browser Cache (Static Assets)
Level 2: CDN Cache (Global Distribution)
Level 3: Redis Cache (API Responses)
Level 4: Application Cache (In-Memory)
```

## Scalability Considerations

### Current Scale
- 1000+ daily active users
- 100k+ API requests per day
- 50GB+ database storage
- 99.9% uptime target

### Growth Planning
- Auto-scaling group configuration ready
- Database read replicas for scale-out
- Microservices architecture migration path
- Event-driven architecture for async processing

## Security Architecture

### Defence in Depth
1. **Edge Security**: Cloudflare WAF and DDoS protection
2. **Network Security**: VPC with security groups
3. **Application Security**: Input validation and sanitisation
4. **Data Security**: Encryption at rest and in transit
5. **Access Control**: Role-based permissions

### Compliance Considerations
- GDPR compliance for EU users
- SOC 2 readiness checklist
- API key rotation policies
- Audit logging for all actions

## Monitoring and Observability

### Metrics Collection
- Application metrics via PM2
- Database performance monitoring
- API response time tracking
- Error rate monitoring

### Alerting Strategy
- Health check failures
- High error rates
- Performance degradation
- Security incidents

## Related Documents
- [System Design](./system-design.md) - Detailed component design
- [Data Flow](./data-flow.md) - Request and data lifecycle
- [Infrastructure](./infrastructure.md) - Deployment architecture