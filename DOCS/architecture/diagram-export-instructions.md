# System Diagram Export Instructions

## Available Formats

### 1. Draw.io Source File
- **File**: `system_diagram.drawio`
- **Usage**: Open in draw.io (app.diagrams.net) for editing
- **Format**: XML-based draw.io format with full editing capabilities

### 2. SVG Vector Format
- **File**: `system_diagram.svg`
- **Usage**: Web embedding, scalable vector graphics
- **Benefits**: Infinite zoom, crisp at any size, embeddable in documentation

### 3. PNG Export (Manual)
To generate PNG from the draw.io file:

1. **Option 1: Online Export**
   - Open `system_diagram.drawio` at https://app.diagrams.net/
   - Go to File → Export as → PNG
   - Set resolution to 300 DPI for high quality
   - Save as `system_diagram.png`

2. **Option 2: Command Line (requires ImageMagick)**
   ```bash
   # Install ImageMagick if not available
   sudo yum install ImageMagick -y
   
   # Convert SVG to PNG
   convert system_diagram.svg -density 300 system_diagram.png
   ```

3. **Option 3: Browser Export**
   - Open the SVG file in Chrome/Firefox
   - Right-click → "Save image as..." or print to PDF then convert

## Embedding Instructions

### In Markdown Documentation
```markdown
![JustSimpleChat System Architecture](./system_diagram.svg)
```

### In HTML Documentation
```html
<img src="./system_diagram.svg" alt="JustSimpleChat System Architecture" style="max-width: 100%; height: auto;">
```

### Direct SVG Embedding
```html
<object data="./system_diagram.svg" type="image/svg+xml" width="100%" height="600">
  <img src="./system_diagram.png" alt="JustSimpleChat System Architecture">
</object>
```

## Diagram Features

### Visual Elements
- ✅ 6 distinct layers with colour coding
- ✅ 182+ AI models across 11 providers  
- ✅ Numbered data flow indicators (1-5)
- ✅ Comprehensive legend and metrics
- ✅ External systems and infrastructure
- ✅ Performance metrics and version info

### Technical Accuracy
- ✅ React 19 + Next.js 15.3.3 frontend
- ✅ Complete API endpoint mapping
- ✅ LiteLLM universal AI gateway
- ✅ MySQL + Redis data layer
- ✅ Multi-environment deployment (dev/staging/prod)
- ✅ Real-world system relationships

## Diagram Maintenance

### Version Updates
- Update version number in bottom footer
- Modify creation date when substantial changes made
- Update model counts as providers are added/removed

### Content Updates
When adding new:
- **AI Providers**: Add to AI Providers layer, update total count
- **API Endpoints**: Add to API Layer with proper colour coding
- **Core Services**: Add to Core Services layer
- **External Systems**: Add to External Systems layer

### Colour Scheme Reference
- **Frontend**: Blue (`#E3F2FD` / `#1976D2`)
- **API Layer**: Green (`#E8F5E8` / `#4CAF50`) 
- **Core Services**: Orange (`#FFF3E0` / `#FF9800`)
- **Data Layer**: Purple (`#F3E5F5` / `#9C27B0`)
- **AI Providers**: Red (`#FFEBEE` / `#F44336`)
- **External Systems**: Teal (`#E0F2F1` / `#00695C`)