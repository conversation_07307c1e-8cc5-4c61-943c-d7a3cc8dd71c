# Web Search System Improvements

## Implemented Improvements (June 24, 2025)

### 1. Context-Aware Search Decisions
- **Before**: Crude keyword pattern matching (`/\b(today|current|latest)\b/`)
- **After**: LLM-based semantic understanding of user intent
- **Benefits**: 
  - "How are you today?" → No search
  - "What's in the news today?" → Search
  - "Anything else?" after news → No search

### 2. Multi-Query Strategy
- **Feature**: Generate 2-3 focused queries for complex requests
- **Example**: "AI regulation in Europe and Asia" generates:
  - `["AI regulation Europe 2025", "AI regulation Asia 2025"]`
- **Benefits**: Better coverage of multi-faceted questions

### 3. Smart Model-Search Coordination
- **Native Search Models**: Grok, Perplexity, DeepSeek have built-in search
- **Logic**: If selecting native search model → Skip external search API
- **Benefits**: Avoids duplicate searches, reduces latency

### 4. Time Sensitivity Granularity
- **IMMEDIATE** (last hour): "right now", "live"
- **TODAY** (last 24h): "today", "this morning"
- **RECENT** (last week): "this week", "recently"
- **CURRENT** (last month): "this month", "latest"
- **Benefits**: More accurate search timeframes

### 5. Search Query Optimization
- **Specific but not narrow**: "New York weather" ✓
- **Time markers**: "OpenAI news December 2024" ✓
- **Authoritative terms**: "official announcement" > "rumors"

## Future Improvements (Documented)

### 1. Conversation Context Memory
```typescript
interface RouterInput {
  recentMessages?: Array<{
    role: 'user' | 'assistant';
    content: string;
    hadWebSearch?: boolean;
    searchResults?: any;
  }>;
}
```
**Benefits**: 
- Understand follow-up questions
- Avoid redundant searches
- Better context awareness

### 2. Search Result Caching
```typescript
private searchCache = new Map<string, { 
  results: any; 
  timestamp: number; 
  ttl: number; // 5min weather, 30min news
}>();
```
**Benefits**:
- Reduce API costs
- Faster responses for similar queries
- Smart TTL based on content type

### 3. Search Confidence Scoring
```typescript
{
  "m": "model-id",
  "s": 95,
  "t": "real_time",
  "q": ["search query"],
  "qc": 0.85  // confidence: 0-1
}
```
**Benefits**:
- Flexible decision making
- Cost optimization
- Better UX for edge cases

### 4. User Preference Learning
```typescript
interface UserSearchPreferences {
  alwaysSearchFor: string[]; // ["stocks", "crypto"]
  neverSearchFor: string[];  // ["recipes", "history"]
  preferredTimeframe: "immediate" | "recent" | "periodic";
}
```
**Benefits**:
- Personalized experience
- Reduced friction
- Better predictions

### 5. Progressive Search Strategy
If initial search returns no results:
1. Try broader query
2. Try related terms
3. Try without time constraints

### 6. Search Result Quality Scoring
Post-search evaluation by LLM:
- "Did the search results help answer the query?"
- Track helpfulness to improve future decisions

## Implementation Priority

### Quick Wins (Implemented) ✅
1. Multi-query strategy
2. Time granularity  
3. Smart model coordination
4. Query optimization rules

### Medium Effort (Future)
1. Conversation context (requires API changes)
2. Search confidence scoring
3. Result caching
4. Progressive search

### Long Term
1. User preference learning
2. Quality scoring & feedback loop
3. Advanced caching strategies

## Testing Examples

### Multi-Query Test
```bash
curl -X POST https://dev.justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Compare weather in NYC and Tokyo"}],
    "webSearchEnabled": true
  }'
# Should generate: ["New York weather forecast", "Tokyo weather forecast"]
```

### Native Search Model Test
```bash
curl -X POST https://dev.justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Latest AI news"}],
    "manualModel": "grok-3"
  }'
# Should NOT generate external search queries (Grok has built-in search)
```

### Context Awareness Test
```bash
# First message
"What's happening in tech today?"
# Should search

# Follow-up
"Anything else interesting?"
# Should NOT search (context-aware)
```

---
*Last Updated: June 24, 2025*