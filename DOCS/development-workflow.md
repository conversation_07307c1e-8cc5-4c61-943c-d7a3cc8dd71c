# Development Workflow

## Table of Contents
- [Git Workflow](#git-workflow)
- [Development Process](#development-process)
- [Testing Guidelines](#testing-guidelines)
- [Code Standards](#code-standards)
- [Pre-Deployment Checklist](#pre-deployment-checklist)
- [Best Practices](#best-practices)

## Git Workflow

### Branch Strategy

```
main (production)
├── develop (development)
│   ├── feature/web-search-enhancement
│   ├── feature/o-series-ui
│   └── feature/router-improvements
└── staging (staging environment)
```

### Standard Workflow

1. **Start from develop branch:**
```bash
cd /home/<USER>/deployments/dev/simplechat-ai
git checkout develop
git pull origin develop
```

2. **Create feature branch:**
```bash
git checkout -b feature/your-feature-name
```

3. **Make changes and commit:**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/your-feature-name
```

4. **Merge to main:**
```bash
git checkout main
git merge feature/your-feature-name
git push origin main
```

### Commit Message Convention

Follow conventional commits format:

```
feat: add new feature
fix: resolve bug in component
docs: update README
style: format code
refactor: restructure module
test: add unit tests
chore: update dependencies
```

## Development Process

### 1. Local Development Setup

```bash
# Start development server
cd /home/<USER>/deployments/dev/simplechat-ai
npm run dev

# Access at https://dev.justsimple.chat
```

### 2. Hot Reload Configuration

PM2 is configured for hot reloading in development:
```javascript
// ecosystem.config.js
{
  name: 'simplechat-dev',
  script: 'npm',
  args: 'run dev',
  watch: false,  // Next.js handles hot reload
  env: {
    NODE_ENV: 'development',
    PORT: 3004
  }
}
```

### 3. Environment Variables

```bash
# Check loaded environment
node -e "console.log(process.env.NODE_ENV)"

# Verify API keys are loaded
node -e "console.log(Object.keys(process.env).filter(k => k.includes('API_KEY')).map(k => k + ': ' + (process.env[k] ? 'SET' : 'MISSING')))"
```

## Testing Guidelines

### 1. TypeScript Checking

```bash
# Run type check
npm run typecheck

# Fix common type issues
# Add missing types to interfaces
# Use proper generic types
# Handle null/undefined cases
```

### 2. Linting

```bash
# Run ESLint
npm run lint

# Auto-fix issues
npm run lint -- --fix

# Common lint rules:
# - No unused variables
# - Consistent naming
# - Proper imports
```

### 3. Build Testing

```bash
# Test production build
npm run build

# Common build issues:
# - Missing dependencies
# - Import errors
# - Type mismatches
```

### 4. Manual Testing Checklist

- [ ] Test with web search ON and OFF
- [ ] Test O-series model selection
- [ ] Verify streaming works smoothly
- [ ] Check mobile responsiveness
- [ ] Test error states
- [ ] Verify source citations display
- [ ] Check router decisions in logs

### 5. API Testing

```bash
# Test chat endpoint
curl -X POST https://dev.justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "auto"
  }'

# Test web search
curl -X POST https://dev.justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "What is in the news today?"}],
    "webSearchEnabled": true
  }'
```

## Code Standards

### 1. File Organization

```
src/
├── app/              # Next.js app router pages
├── components/       # React components
│   ├── chat/        # Chat-specific components
│   ├── ui/          # Reusable UI components
│   └── layout/      # Layout components
├── lib/             # Utilities and helpers
│   ├── ai/          # AI provider integrations
│   └── utils/       # General utilities
└── types/           # TypeScript type definitions
```

### 2. Component Structure

```typescript
// Standard component template
'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  // Props interface
}

export function Component({ ...props }: ComponentProps) {
  // State and hooks
  
  // Effects
  
  // Handlers
  
  // Render
  return (
    <div className={cn('base-classes', props.className)}>
      {/* Component content */}
    </div>
  );
}
```

### 3. API Route Structure

```typescript
// Standard API route template
import { NextRequest, NextResponse } from 'next/server';
import { apiLogger } from '@/lib/api-logger';

export async function POST(req: NextRequest) {
  try {
    // Parse request
    const body = await req.json();
    
    // Validate
    if (!body.required) {
      return NextResponse.json(
        { error: 'Missing required field' },
        { status: 400 }
      );
    }
    
    // Process
    const result = await processRequest(body);
    
    // Return
    return NextResponse.json(result);
    
  } catch (error) {
    apiLogger.error('Route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 4. Styling Guidelines

```typescript
// Use Tailwind classes
<div className="flex items-center gap-4 p-4">

// Dark mode support
<div className="bg-white dark:bg-gray-800">

// Responsive design
<div className="w-full md:w-1/2 lg:w-1/3">

// State variants
<button className="hover:bg-blue-600 active:scale-95">
```

## Pre-Deployment Checklist

### Required Checks

```bash
# 1. Run all checks
npm run typecheck && npm run lint && npm run build

# 2. Test critical paths
# - User can send messages
# - Web search works
# - Model selection works
# - Streaming displays properly

# 3. Check console for errors
# Open DevTools and verify no errors

# 4. Review git changes
git diff --staged

# 5. Check for secrets
git diff --staged | grep -i "api_key\|secret\|password"
```

### Deployment Commands

```bash
# Complete deployment sequence
cd /home/<USER>/deployments/production/simplechat-ai
git pull origin main
npm install --legacy-peer-deps
npm run build
pm2 restart simplechat-production
pm2 logs simplechat-production --nostream --lines 50
```

## Best Practices

### 1. Performance

- Use React.memo for expensive components
- Implement proper loading states
- Optimize images with next/image
- Use dynamic imports for large components
- Minimize re-renders with proper state management

### 2. Error Handling

```typescript
// Always handle errors gracefully
try {
  const result = await riskyOperation();
  return result;
} catch (error) {
  console.error('Operation failed:', error);
  // Provide user-friendly feedback
  return { error: 'Something went wrong. Please try again.' };
}
```

### 3. Logging

```typescript
// Use structured logging
apiLogger.info('Operation completed', {
  userId: user.id,
  action: 'chat',
  model: selectedModel,
  duration: Date.now() - startTime
});
```

### 4. Security

- Never log sensitive data
- Validate all user inputs
- Use environment variables for secrets
- Implement rate limiting
- Sanitize outputs

### 5. Code Reviews

Before merging:
- [ ] Code follows style guide
- [ ] No console.logs in production code
- [ ] Error handling is comprehensive
- [ ] Performance impact considered
- [ ] Documentation updated if needed

---
*Last Updated: June 24, 2025*