# O-Series Models Documentation

## Table of Contents
- [Overview](#overview)
- [The Problem](#the-problem)
- [The Solution](#the-solution)
- [Implementation](#implementation)
- [UI Components](#ui-components)
- [Known Issues](#known-issues)
- [Testing](#testing)

## Overview

OpenAI's O-series models (o1, o3, o4) are reasoning models that show their thought process before providing answers. This documentation covers the special handling required for these models to provide a good user experience.

### O-Series Models
- `o1-preview`
- `o1-mini`
- `o3-mini` 
- `o4-mini`

## The Problem

O-series models mix their reasoning process with final answers in standard Chat Completions API:

```
**Step 1: Understanding the question**
I need to analyze this...

**Step 2: Processing**  
Let me think about...

**Final Answer**
The answer is 42.
```

This creates a poor UX where users see the entire thinking process mixed with the actual response.

## The Solution

### 1. Use OpenAI's Responses API
- **Chat Completions API**: Reasoning mixed with content ❌
- **Responses API**: Reasoning and content separated ✅

The Responses API provides structured separation:
```json
{
  "type": "reasoning",
  "content": "Let me think step by step..."
}
{
  "type": "content", 
  "content": "The answer is 42."
}
```

### 2. Implementation Architecture

```
O-Series Request Flow:
├── Detect O-Series Model (shouldUseResponsesAPI)
├── Use Responses API (/chat/completions/responses)
├── Stream Reasoning Events Separately
├── Display in Dedicated UI Components
└── Auto-collapse After Completion
├── Stream Events:
│   ├── reasoning_summary_text.delta → Reasoning Box
│   └── output_text.delta → Main Message
└── Display Separately in UI
```

## Implementation

### Detection (`/src/lib/ai/o-series-models.ts`)
```typescript
export function shouldUseResponsesAPI(model: string): boolean {
  const oSeriesModels = ['o1-preview', 'o1-mini', 'o3-mini', 'o4-mini'];
  return oSeriesModels.some(m => model.includes(m));
}
```

### Streaming Handler (`/src/lib/ai/providers/litellm-adapter.ts`)
```typescript
function generateResponseStream() {
  // Use different endpoint for O-series
  const endpoint = shouldUseResponsesAPI(model) 
    ? '/chat/completions/responses' 
    : '/chat/completions';
    
  // Add reasoning parameter for O-series
  if (shouldUseResponsesAPI(model)) {
    requestBody.reasoning = { summary: "auto" };
  }
}
```

### Event Types
```typescript
// Reasoning events
if (chunk.type === 'response.reasoning_summary_text.delta') {
  yield {
    type: 'reasoning',
    content: chunk.reasoning_summary_text.delta
  };
}

// Content events  
if (chunk.type === 'response.output_text.delta') {
  yield {
    type: 'content',
    content: chunk.output_text.delta.content
  };
}
```

## UI Components

### 1. UnifiedMessageDisplay (`/src/components/chat/unified-message-display.tsx`)
- Combines reasoning and message in single container
- Auto-collapse reasoning after completion
- Smart timing: 4s minimum display + 1.5s buffer
- User can click to prevent auto-collapse

### 2. ReasoningSummary (`/src/components/chat/reasoning-summary.tsx`)
- Displays reasoning in collapsible box
- Shows thinking animation during streaming
- Gradient effects and smooth transitions
- Expandable for full reasoning view

### Visual Flow
```
┌─────────────────────────────────────┐
│ Assistant · o4-mini                 │
├─────────────────────────────────────┤
│ 📋 Reasoning (click to expand)      │
│ ─────────────────────────────────── │
│ Analyzing the request...            │
│ [Show more]                         │
├─────────────────────────────────────┤
│ Here's the answer to your question: │
│ The result is 42.                   │
└─────────────────────────────────────┘
```

## Known Issues

### 1. ~~Double Reasoning Issue~~ ✅ FIXED
- **Problem**: Reasoning appeared in both reasoning box AND message content
- **Cause**: Route.ts was adding all chunks to assistant message
- **Fix**: Filter out reasoning chunks from main content

### 2. LiteLLM Version Requirement
- **Issue**: Reasoning streaming requires LiteLLM ≥ v1.71
- **Current**: Using latest LiteLLM version
- **Status**: ✅ Working

### 3. Failover Limitations
- **OpenRouter**: Does NOT support Responses API
- **Solution**: O-series models only use native OpenAI (no failover)

## Testing

### Test O-Series Manual Selection
```bash
curl -X POST https://dev.justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Count Rs in strawberry. Think step by step."}],
    "manualModel": "o4-mini"
  }'
```

### Expected Response Structure
```json
{
  "content": "The word has 3 Rs...",
  "reasoning": "Step 1: Let me spell out strawberry...",
  "model": "o4-mini",
  "usage": {
    "prompt_tokens": 20,
    "completion_tokens": 100,
    "reasoning_tokens": 250
  }
}
```

## Configuration

### Required Parameters
```typescript
// For O-series models
{
  model: "o4-mini",
  messages: [...],
  reasoning: {
    summary: "auto" | "concise" | "detailed"
  },
  stream: true
}
```

### Model Routing
- O-series models bypass normal routing when manually selected
- Router shows `"reason": "Manual selection"`
- Only available in paid plans (not FREE tier)

## Recent Updates

### June 23, 2025
1. **Enhanced UI**:
   - New unified display component
   - Smart auto-collapse with timing
   - Modern animations (gradient shimmer)
   - Rotating status messages

2. **Fixed Issues**:
   - ✅ Double reasoning display
   - ✅ Width constraints
   - ✅ Animation performance
   - ✅ State management

---
*Last Updated: June 24, 2025*