# Recent Changes Log

## June 24, 2025

### Documentation
- Created comprehensive `/DOCS` directory with structured documentation
- Added documentation for web search system, router, O-series models, and deployment
- Established documentation index in `/DOCS/README.md`
- Added inline documentation references in key code files

### Web Search Intelligence Improvements
1. **Context-Aware Search Decisions**
   - Replaced crude keyword detection with LLM-based semantic analysis
   - Router now understands intent: "How are you today?" won't trigger search
   - Follow-up questions like "Anything else?" properly handled
   
2. **Improved Router Prompting**
   - Clear rules for when to generate search queries
   - Examples showing context-aware decisions
   - Removed aggressive keyword pattern matching
   
3. **Key Changes**:
   ```typescript
   // Before: Crude pattern matching
   /\b(today|current|latest)\b/ // Would match "How are you today?"
   
   // After: Semantic understanding via LLM
   "Look at the user's ACTUAL INTENT, not just keywords"
   ```

### Advanced Web Search Enhancements
1. **Multi-Query Strategy**
   - Complex questions now generate 2-3 focused search queries
   - Example: "Weather in NYC and LA" → Two separate weather queries
   
2. **Smart Model Coordination**
   - Native search models (Grok, Perplexity) skip external search API
   - Reduces latency and duplicate searches
   
3. **Time Sensitivity Levels**
   - IMMEDIATE: "right now" → last hour
   - TODAY: "today" → last 24 hours
   - RECENT: "this week" → last 7 days
   - CURRENT: "this month" → last 30 days
   
4. **Search Query Optimization**
   - Better specificity: "New York weather" not "weather" or "NYC 73F"
   - Time markers: "OpenAI news December 2024"
   - Authoritative terms: "official" > "rumors"
   
5. **Future-Ready Architecture**
   - Added interfaces for conversation context
   - Prepared for search result caching
   - User preference learning structure

## June 23, 2025

### Web Search System Overhaul
1. **Router Integration with LiteLLM**
   - Fixed router using Google API directly instead of LiteLLM
   - Changed base URL from `generativelanguage.googleapis.com` to LiteLLM proxy
   - Updated router model from `gemini-2.5-flash-preview` to `gemini-2.0-flash`
   - Fixed JSON parsing issues by stripping markdown code blocks

2. **Web Search UI Cleanup**
   - Removed `GlobalWebSearchIndicator` component (green box bottom-left)
   - Removed web search indicator from `smooth-streaming-message.tsx`
   - Created new `CondensedSources` component for compact source display
   - Sources now appear above messages as clickable domain pills

3. **Router Intelligence**
   - Implemented smart override: router triggers web search for time-sensitive queries even when toggle is OFF
   - Added real-time information detection with keywords like "today", "news", "current"
   - Router generates 1-3 focused search queries automatically

4. **Authentication Changes (Temporary)**
   - Temporarily bypassed auth check in `/api/web-search` endpoint
   - Enabled web search for FREE plan users (for testing)

### O-Series Model Enhancements
1. **UI Improvements**
   - New `UnifiedMessageDisplay` component combining reasoning and message
   - Smart auto-collapse with 4s minimum display time
   - Modern gradient shimmer animations replacing bouncing dots
   - Rotating status messages during reasoning

2. **Bug Fixes**
   - Fixed double reasoning issue (reasoning appearing in both box and content)
   - Resolved width constraints for O-series models
   - Improved animation performance with proper easing

## June 21, 2025

### LiteLLM Infrastructure Update
- Migrated to database-only model management
- Updated task definition from `litellm-proxy:5` to `litellm-proxy:6`
- Successfully added all 182 models to LiteLLM
- Resolved model duplication issues

## June 16, 2025

### Development Environment
- Fixed PM2 configuration for hot-reloading
- Resolved streaming errors with `getFallbackProvider()` function
- Updated all CLAUDE.md documentation files

## Key Code Changes

### Router Configuration Change
```typescript
// Before:
private baseURL = 'https://generativelanguage.googleapis.com/v1beta';
private readonly ROUTER_MODEL = 'models/gemini-2.5-flash-preview-04-17';

// After:
private baseURL = process.env.LITELLM_BASE_URL || 
  'http://litellm-proxy-alb-**********.us-east-1.elb.amazonaws.com/v1';
private readonly ROUTER_MODEL = 'gemini/gemini-2.0-flash';
```

### Web Search Override Logic
```typescript
// Web search triggers if toggle is ON OR router detects real-time needs
const routerDetectedRealtimeNeeds = routerDecision?.searchQueries && 
                                   routerDecision.searchQueries.length > 0;

if ((webSearchEnabled || routerDetectedRealtimeNeeds) && canUseWebSearch) {
  // Perform web search
}
```

### Sources Display Update
```typescript
// New condensed sources component
<CondensedSources 
  searchResults={message.searchResults}
  className="mb-2"
/>
```

## Deployment Notes

### Current Status
- Development: ✅ Healthy (Port 3004)
- Staging: ⚠️ Degraded (Port 3005)
- Production: ✅ Updated (Port 3006)

### Deployment Commands Used
```bash
# From dev directory
git -C /home/<USER>/deployments/production/simplechat-ai pull origin main
npm --prefix /home/<USER>/deployments/production/simplechat-ai run build
pm2 restart simplechat-production
```

---
*Last Updated: June 24, 2025*