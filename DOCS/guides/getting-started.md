# Getting Started with JustS<PERSON>ple<PERSON>hat

**Description**: Quick start guide for new users and developers, covering initial setup, basic usage, and key concepts.

## What is JustSimpleChat?

JustSimpleChat is a cutting-edge AI chat platform that provides unified access to over 180 AI models from 11+ providers through a single, intuitive interface. Built with React 19 and Next.js 15, it offers enterprise-grade features whilst maintaining simplicity for everyday users.

### Key Features
- **Multi-Provider Access**: Connect to OpenAI, Anthropic, Google, Meta, Mistral, and more through a single interface
- **Intelligent Model Routing**: Automatic selection of the best AI model for your query using Gemini 2.5 Flash Lite
- **O-Series Reasoning Models**: Advanced reasoning capabilities with dedicated UI for thought processes
- **Vision & Multimodal Support**: Upload images for analysis with compatible models
- **Web Search Integration**: Real-time information retrieval with search-enabled models
- **Cost Optimisation**: Smart routing minimises API costs whilst maximising response quality
- **Real-time Streaming**: See responses as they're generated with smooth, progressive rendering

## Quick Start

### For Users

#### 1. Accessing JustSimpleChat
Navigate to [justsimple.chat](https://justsimple.chat) in your web browser. The platform works best on modern browsers like Chrome, Firefox, Safari, or Edge.

#### 2. Creating Your First Chat
- Click the "New Chat" button in the sidebar
- Type your message in the input box at the bottom
- Press Enter or click the send button
- The intelligent router will automatically select the best model for your query

#### 3. Understanding the Interface
- **Chat Area**: Main conversation display with message bubbles
- **Model Selector**: Toggle between Auto mode (recommended) and manual model selection
- **Sidebar**: Access chat history, settings, and create new conversations
- **Input Box**: Compose messages with full markdown support

#### 4. Your First Conversation
Try these example prompts to explore capabilities:
- "Explain quantum computing in simple terms"
- "Write a Python function to calculate fibonacci numbers"
- "What are the key differences between React 18 and React 19?"
- Upload an image and ask "What's in this image?" (vision models only)

### For Developers

#### 1. Repository Setup
```bash
# Clone the repository
git clone https://github.com/yourusername/simplechat-ai.git
cd simplechat-ai

# Switch to development branch
git checkout develop

# Install dependencies (important: use legacy peer deps)
npm install --legacy-peer-deps
```

#### 2. Environment Configuration
Create a `.env.local` file in the root directory:
```env
# Database
DATABASE_URL="mysql://root:password@127.0.0.1:3306/justsimplechat_dev"

# Redis
REDIS_URL="redis://localhost:6379"

# LiteLLM Proxy
LITELLM_BASE_URL="http://your-litellm-proxy/v1"
LITELLM_API_KEY="sk-simplechat-master-key"

# NextAuth
NEXTAUTH_URL="http://localhost:3004"
NEXTAUTH_SECRET="your-secret-key"
```

#### 3. Running Locally
```bash
# Run development server with hot-reloading
npm run dev

# Access at http://localhost:3004
```

#### 4. Making Your First Change
1. Create a feature branch: `git checkout -b feature/my-first-change`
2. Edit a component in `src/components/`
3. See changes instantly with hot-reload
4. Test thoroughly before committing
5. Run checks: `npm run typecheck && npm run lint`

## Core Concepts

### AI Models

#### Model Categories
1. **General Purpose Models**
   - GPT-4o, Claude 3.5, Gemini Pro
   - Best for: General queries, creative writing, analysis
   - Balanced cost and performance

2. **Reasoning Models (O-Series)**
   - o1, o3-mini, o4-mini
   - Best for: Complex problem-solving, mathematics, logic
   - Shows step-by-step reasoning process

3. **Fast Models**
   - GPT-4o-mini, Claude Haiku, Gemini Flash
   - Best for: Quick responses, simple queries
   - Lower cost, faster response times

4. **Specialised Models**
   - Vision models: Image analysis and understanding
   - Code models: Programming and technical tasks
   - Search models: Real-time information retrieval

#### Provider Characteristics
- **OpenAI**: Industry standard, excellent general performance
- **Anthropic**: Strong safety focus, nuanced responses
- **Google**: Multimodal capabilities, long context windows
- **Meta/Groq**: Open models, fast inference
- **Mistral**: Multilingual excellence, code generation

### Auto Mode vs Manual Mode

#### Auto Mode (Recommended)
The intelligent router analyses your query and selects the optimal model based on:
- **Query complexity**: Simple vs complex reasoning requirements
- **Task type**: Creative, analytical, coding, or factual
- **Cost efficiency**: Balances quality with API costs
- **Model availability**: Automatically failover if primary choice is unavailable

**Benefits**:
- No need to understand model differences
- Optimal cost-performance ratio
- Automatic failover handling
- Continuously improved routing logic

#### Manual Mode
Directly select a specific model when you:
- Need consistent model behaviour across queries
- Testing model-specific features
- Require specific provider compliance
- Debugging or comparing model outputs

**Selection Tips**:
- Start with Auto mode for best results
- Use o-series models for complex reasoning
- Choose vision models for image analysis
- Select search-enabled models for current events

### Chat Interface

#### Message Composition
- **Markdown Support**: Format text with headers, lists, code blocks
- **Multi-line Input**: Shift+Enter for new lines, Enter to send
- **File Attachments**: Drag & drop or click to upload images
- **Edit Previous**: Click any of your messages to edit and regenerate

#### Model Selection Panel
- **Toggle Switch**: Auto mode (brain icon) vs Manual selection
- **Model List**: Searchable dropdown with provider badges
- **Capabilities**: Icons indicate vision, search, or reasoning support
- **Cost Indicator**: Relative pricing shown for each model

#### Settings Panel
- **Theme**: Light, dark, or system preference
- **Display Options**: Code highlighting, message density
- **Keyboard Shortcuts**: Customisable hotkeys
- **Export Settings**: Download conversation formats

#### History Navigation
- **Search**: Find conversations by content
- **Folders**: Organise chats by topic
- **Pin Important**: Keep frequently accessed chats at top
- **Bulk Actions**: Archive or delete multiple chats

## Basic Features

### Starting a Chat

#### Creating a New Conversation
1. Click the "+" or "New Chat" button in the sidebar
2. Your new chat starts with a clean slate
3. Previous conversations remain accessible in history

#### Sending Messages
- **Basic Text**: Simply type and press Enter
- **Code Blocks**: Use triple backticks with language specification
  ```python
  def hello_world():
      print("Hello from JustSimpleChat!")
  ```
- **Lists and Formatting**: Full markdown support
  - Bullet points with `-` or `*`
  - Numbered lists with `1.`
  - **Bold** with `**text**`
  - *Italic* with `*text*`

#### Understanding Responses
- **Streaming Display**: Text appears progressively as generated
- **Model Indicator**: Shows which AI model provided the response
- **Copy Button**: One-click copy of entire response
- **Regenerate**: Click the refresh icon to get a different response

#### Advanced Message Features
- **Edit & Retry**: Click your message to modify and regenerate
- **Branch Conversations**: Edit creates a new conversation branch
- **Code Syntax Highlighting**: Automatic language detection
- **Link Preview**: URLs automatically generate previews

### Model Selection

#### Understanding the Model List
The model selector displays all available AI models with rich metadata:
- **Model Name**: e.g., "GPT-4o" or "Claude 3.5 Sonnet"
- **Provider Logo**: Quick visual identification
- **Version Info**: Latest model versions automatically updated
- **Search Bar**: Filter models by name or capability

#### Provider Indicators
Each model shows its provider with distinctive branding:
- 🟢 **OpenAI**: Green badge for GPT models
- 🔵 **Anthropic**: Blue badge for Claude models
- 🔴 **Google**: Red badge for Gemini models
- 🟡 **Meta**: Yellow badge for Llama models
- ⚡ **Groq**: Lightning badge for hardware-accelerated models

#### Capability Badges
Icons indicate special model features:
- 👁️ **Vision**: Can analyse and understand images
- 🔍 **Search**: Access to real-time web information
- 🧠 **Reasoning**: Shows step-by-step thought process
- 💻 **Code**: Optimised for programming tasks
- 🗣️ **Voice**: Supports audio input/output

#### Cost Indicators
- 💚 **Low Cost**: Budget-friendly for high-volume use
- 💛 **Medium Cost**: Balanced price-performance
- ❤️ **Premium**: Highest quality, higher cost
- Exact pricing per 1M tokens shown on hover

### Conversation Management

#### Automatic Saving
- All conversations save automatically
- No manual save required
- Drafts preserved even if browser closes
- Sync across devices with your account

#### Searching History
1. Use the search bar in the sidebar
2. Search by:
   - Message content
   - Date range
   - Model used
   - Tags (if applied)
3. Results show preview snippets
4. Click to jump directly to conversation

#### Exporting Conversations
Export formats available:
- **Markdown**: Formatted text with code blocks preserved
- **JSON**: Structured data for processing
- **PDF**: Styled document for sharing
- **Plain Text**: Simple format for notes

#### Sharing & Collaboration
- **Share Link**: Generate public link (24-hour expiry)
- **Copy Thread**: Duplicate conversation for variations
- **Privacy**: Shared links are read-only
- **Revoke Access**: Disable share links anytime

## Advanced Features

### O-Series Models

#### What Are O-Series Models?
OpenAI's O-series (o1, o3-mini, o4-mini) are advanced reasoning models that "think" before responding:
- Show internal reasoning process
- Excel at complex problem-solving
- Better at mathematics and logic
- More expensive but higher quality

#### Reasoning Display Features
- **Thinking Animation**: Elegant progress indicator during reasoning
- **Collapsible Reasoning**: Auto-collapse after 4 seconds (click to expand)
- **Step Indicators**: See reasoning stages as they progress
- **Full Reasoning View**: Expand to see complete thought process

#### Ideal Use Cases
- Mathematical proofs and calculations
- Complex coding challenges
- Logic puzzles and reasoning tasks
- Scientific analysis and hypotheses
- Strategic planning and decision-making

#### Best Practices
- Be specific and detailed in prompts
- Allow extra time for reasoning (10-30 seconds typical)
- Use for quality over speed
- Review reasoning to understand conclusions
- Combine with simpler models for follow-ups

### Vision Models

#### Supported Vision Models
- **GPT-4o**: Excellent general image understanding
- **Claude 3.5**: Detailed analysis and descriptions
- **Gemini Pro Vision**: Strong at OCR and diagrams
- **Llama 3.2 Vision**: Open-source alternative

#### Image Upload Process
1. Click the attachment icon or drag & drop
2. Supported formats: JPEG, PNG, GIF, WebP
3. Maximum size: 20MB per image
4. Multiple images: Up to 5 per message
5. Preview appears before sending

#### Common Use Cases
- **Document Analysis**: Extract text, summarise content
- **Image Description**: Detailed explanations for accessibility
- **Code Screenshots**: Debug or explain code in images
- **Chart Interpretation**: Analyse graphs and data visualisations
- **Design Feedback**: UI/UX critique and suggestions
- **Problem Solving**: Maths problems, diagrams, flowcharts

#### Current Limitations
- No video analysis (images only)
- Cannot generate or edit images
- Medical images require disclaimer
- Some models may refuse inappropriate content
- OCR accuracy varies with image quality

### Web Search

#### Search-Enabled Models
Models with real-time web access:
- **Perplexity Models**: Specialised for search with citations
- **Cohere Command R+**: Returns search results with sources
- **Select GPT-4 Configurations**: Via function calling
- **Router Auto-Selection**: Automatically uses search when needed

#### Query Formatting Tips
- **Current Events**: "What happened in tech news today?"
- **Fact Checking**: "Verify: [claim to check]"
- **Research**: "Find recent studies on [topic]"
- **Comparisons**: "Compare 2024 vs 2025 data on [subject]"
- **Local Info**: "Weather in [city]" or "Events near [location]"

#### Understanding Search Results
- **Source Citations**: Links to original sources
- **Freshness Indicators**: Date of information
- **Confidence Levels**: How certain the model is
- **Multiple Perspectives**: Often presents various viewpoints

#### Best Practices
- Be specific about timeframes
- Request sources for verification
- Cross-reference important facts
- Use for dynamic information only
- Combine with reasoning models for analysis

## Configuration

### User Settings

#### Theme Selection
- **Light Mode**: Clean, bright interface for daytime use
- **Dark Mode**: Reduced eye strain for evening work
- **System**: Automatically matches your OS preference
- **Custom Accent**: Choose your preferred highlight colour

#### Default Model Preferences
- **Preferred Mode**: Set Auto or Manual as default
- **Favourite Models**: Pin frequently used models to top
- **Provider Preference**: Prioritise specific providers
- **Cost Limits**: Set maximum cost per query

#### Display Preferences
- **Message Density**: Compact, comfortable, or spacious
- **Font Size**: Adjustable for accessibility
- **Code Theme**: Syntax highlighting preferences
- **Animation Speed**: Reduce motion if preferred
- **Timestamp Display**: Relative or absolute times

#### Keyboard Shortcuts
Default shortcuts (customisable):
- `Ctrl/Cmd + Enter`: Send message
- `Ctrl/Cmd + K`: New chat
- `Ctrl/Cmd + /`: Toggle model selector
- `Ctrl/Cmd + Shift + C`: Copy last response
- `Ctrl/Cmd + Shift + E`: Export conversation
- `Escape`: Cancel current generation

### API Access

#### API Integration
JustSimpleChat provides programmatic access for developers:
```bash
# Example API call
curl -X POST https://justsimple.chat/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "messages": [{"role": "user", "content": "Hello"}],
    "model": "auto"
  }'
```

#### Authentication
- **API Keys**: Generate from account settings
- **Bearer Token**: Include in Authorization header
- **Key Rotation**: Regenerate keys periodically
- **Scope Control**: Limit key permissions

#### Rate Limits
- **Free Tier**: 100 requests/day, 10 requests/minute
- **Pro Tier**: 10,000 requests/day, 100 requests/minute
- **Enterprise**: Custom limits available
- **Headers**: Rate limit info in response headers

#### Usage Tracking
- **Dashboard**: Real-time usage statistics
- **Cost Breakdown**: Per model and provider
- **Monthly Reports**: Detailed usage analysis
- **Alerts**: Notification at usage thresholds

## Troubleshooting

### Common Issues

#### Connection Errors
**"Failed to connect to server"**
- Check internet connection
- Try refreshing the page (F5)
- Clear browser cache and cookies
- Disable VPN/proxy if active
- Check status page for outages

#### Model Unavailable
**"Selected model is currently unavailable"**
- Model may be at capacity
- Switch to Auto mode for automatic failover
- Try alternative model from same provider
- Check model status in settings
- Report persistent issues

#### Slow Response Times
**Responses taking longer than expected**
- O-series models naturally take 10-30 seconds
- Network congestion may cause delays
- Try a "mini" or "flash" model for faster responses
- Check if reasoning display is enabled
- Reduce message length for quicker processing

#### Browser Compatibility
**UI not displaying correctly**
- Update to latest browser version
- Disable problematic extensions
- Try incognito/private mode
- Supported: Chrome 90+, Firefox 88+, Safari 14+
- Clear site data and reload

### Getting Help

#### Documentation Resources
- **This Guide**: Getting started basics
- **[API Documentation](/DOCS/api/)**: Technical API reference
- **[Architecture Guide](/DOCS/architecture/)**: System design details
- **[Troubleshooting Guide](/DOCS/troubleshooting.md)**: Detailed solutions

#### Community Support
- **Discord Server**: Real-time help from community
- **GitHub Discussions**: Technical Q&A
- **Reddit Community**: r/JustSimpleChat
- **Stack Overflow**: Tag `justsimplechat`

#### Reporting Issues
**Bug Reports**:
1. Check known issues first
2. Gather reproduction steps
3. Include browser/OS details
4. Submit via GitHub Issues
5. Follow issue template

**Feature Requests**:
1. Search existing requests
2. Describe use case clearly
3. Explain expected behaviour
4. Vote on others' requests
5. Participate in discussions

## Next Steps

### Exploring Advanced Features
1. **Master O-Series Models**: Try complex reasoning tasks
2. **Vision Experiments**: Upload diagrams and screenshots
3. **Workflow Automation**: Create templates for repeated tasks
4. **Cost Optimisation**: Learn which models to use when
5. **Custom Integrations**: Explore API capabilities

### Advanced Guides
- **[O-Series Models Deep Dive](/DOCS/o-series-models.md)**: Master reasoning models
- **[Web Search Integration](/DOCS/web-search-system.md)**: Advanced search techniques
- **[Model Selection Strategy](/DOCS/reference/model-matrix.md)**: Choose optimal models
- **[Performance Optimisation](/DOCS/guides/best-practices.md)**: Speed and efficiency tips

### Contributing to JustSimpleChat
- **Code Contributions**: See [Contributing Guide](/DOCS/development/contributing.md)
- **Documentation**: Help improve these docs
- **Bug Reports**: Quality issue reports help everyone
- **Feature Ideas**: Share your vision for the platform
- **Community Support**: Help others in forums

### Join the Community
- **Monthly Meetups**: Virtual sessions on new features
- **Beta Testing**: Early access to new capabilities
- **Case Studies**: Share how you use JustSimpleChat
- **Developer Showcase**: Present your integrations

---

**Welcome to JustSimpleChat!** 🎆 Start with a simple chat and discover the power of 180+ AI models at your fingertips.