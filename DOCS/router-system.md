# Router System Documentation

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Router Logic](#router-logic)
- [Model Selection Process](#model-selection-process)
- [Configuration](#configuration)
- [Troubleshooting](#troubleshooting)

## Overview

The Router System is an intelligent AI-powered model selector that analyzes user queries and selects the most appropriate AI model based on task requirements, capabilities, and cost optimization.

### Key Features
- **LLM-based routing**: Uses Gemini 2.0 Flash for intelligent decisions
- **Multi-factor analysis**: Considers complexity, intent, capabilities, and cost
- **Web search detection**: Automatically identifies queries needing real-time data
- **Plan-based filtering**: Only shows models available in user's subscription plan

## Architecture

### Core Components

1. **EnhancedBedrockRouterV2** (`/src/lib/ai/bedrock-router-enhanced-v2.ts`)
   - Main router implementation
   - Singleton pattern for efficiency
   - Caches static prompts for performance

2. **Router Model**
   - **Current**: `gemini/gemini-2.0-flash` via LiteLLM
   - **Fallback**: None (direct routing only)
   - **Cost**: ~$0.00001 per routing decision

3. **Integration Points**
   - Called from `/src/app/api/chat/route.ts` (line 665)
   - Returns `RouterDecision` with model selection and metadata

## Router Logic

### Decision Flow
```
User Query → Router Analysis → Model Selection → Response Generation
                ↓
         Web Search Detection
                ↓
         Search Query Generation
```

### Analysis Dimensions

1. **Query Complexity**
   - Simple: FAQ, definitions, basic explanations
   - Medium: Analysis, comparisons, detailed explanations
   - Complex: Multi-step reasoning, mathematical proofs, code architecture

2. **Task Type Detection**
   - General conversation
   - Code generation/debugging
   - Creative writing
   - Data analysis
   - Mathematical reasoning
   - Visual analysis (if image attached)
   - Real-time information needs

3. **Capability Requirements**
   - Vision: Image analysis needed
   - Web Search: Current events, facts, prices
   - Function Calling: Structured outputs
   - Long Context: Document analysis
   - Reasoning: Complex problem-solving

4. **Cost Optimization**
   - Balances model capability with API costs
   - Prefers cheaper models for simple tasks
   - Uses premium models only when necessary

1. **Query Complexity** (0-10 scale)
   - Simple questions: 1-3
   - Moderate tasks: 4-7
   - Complex reasoning: 8-10

2. **Intent Classification**
   - `general`: Basic Q&A
   - `coding`: Programming tasks
   - `mathematical`: Calculations
   - `creative`: Writing, brainstorming
   - `research`: Information gathering
   - `analysis`: Data interpretation

3. **Special Requirements**
   - `vision`: Image analysis needed
   - `web_search`: Real-time information
   - `long_context`: Extended conversations
   - `tool_use`: Function calling
   - `json_mode`: Structured output

### Real-time Information Detection

The router detects real-time needs using patterns in `detectRealtimeInfoNeeds()`:

```typescript
// Keywords that trigger web search
const patterns = [
  'today', 'current', 'latest', 'now', 'recent',
  'news', 'weather', 'happening',
  '2024', '2025', // Current year references
  'status', 'update'
];
```

## Model Selection Process

### Router Prompt Structure

The router uses a sophisticated prompt that analyses:

```typescript
interface RouterAnalysis {
  intent: string;           // User's primary goal
  task_type: string;        // Category of task
  complexity: 'low' | 'medium' | 'high';
  capabilities_needed: string[];  // Required model features
  recommended_model: string;      // Selected model ID
  reasoning: string;              // Explanation of choice
  needs_web_search: boolean;      // Real-time data required
  search_queries?: string[];      // Generated search queries
}
```

### Model Selection Priority

1. **Reasoning Tasks** → O-series models (o4-mini, o3-mini)
2. **Web Search Needed** → Perplexity or Grok models
3. **Code Generation** → Codestral, GPT-4o, Claude 3.5
4. **Vision Tasks** → GPT-4o, Claude 3.5, Gemini Pro
5. **General Chat** → GPT-4o-mini, Claude Haiku, Gemini Flash
6. **Long Context** → Gemini models (up to 2M tokens)

### Example Routing Decisions

```json
// Complex reasoning task
{
  "intent": "Solve mathematical proof",
  "complexity": "high",
  "recommended_model": "o4-mini",
  "reasoning": "Complex mathematical reasoning requires O-series model"
}

// Current events query
{
  "intent": "Get latest news on AI developments",
  "needs_web_search": true,
  "recommended_model": "perplexity/sonar",
  "search_queries": ["AI news December 2024", "latest AI breakthroughs"]
}

// Simple chat
{
  "intent": "Casual conversation",
  "complexity": "low",
  "recommended_model": "gpt-4o-mini",
  "reasoning": "Simple query suits fast, cost-effective model"
}
```

### 1. Plan-Based Filtering
```typescript
// Get models available for user's plan
const availableModels = await getModelsForPlan(userPlan);
```

### 2. Capability Matching
- Filters models by required capabilities
- Considers model strengths (e.g., Claude for writing, GPT for coding)
- Checks for special features (vision, tools, etc.)

### 3. Cost Optimization
- Balances capability with credit usage
- Prefers cheaper models for simple tasks
- Uses premium models only when necessary

### 4. Manual Override
When user manually selects a model:
```typescript
if (manualModel) {
  return {
    model: { id: manualModel, name: manualModel },
    reason: "Manual selection",
    confidence: 1.0
  };
}
```

## Configuration

### Environment Variables

```env
# Router Configuration
ROUTER_MODEL="gemini/gemini-2.0-flash"  # Via LiteLLM
ROUTER_ENABLED=true                      # Enable/disable routing
ROUTER_CACHE_TTL=3600                   # Cache decisions for 1 hour
ROUTER_TIMEOUT=5000                     # 5 second timeout
```

### Model Availability by Plan

```typescript
const PLAN_MODELS = {
  FREE: [
    'gpt-4o-mini',
    'claude-3-5-haiku-********',
    'gemini/gemini-2.0-flash-8b',
    'groq/llama-3.1-8b-instant'
  ],
  PRO: [
    // All FREE models plus:
    'gpt-4o',
    'claude-3-5-sonnet-********',
    'gemini/gemini-2.0-flash',
    'mistral/mistral-large-2411'
  ],
  PREMIUM: [
    // All models including:
    'o4-mini',
    'claude-3-opus-********',
    'gemini/gemini-1.5-pro',
    'perplexity/sonar-pro'
  ]
};
```

### Router Initialization

```typescript
// In route.ts
const router = EnhancedBedrockRouterV2.getInstance();
await router.initialize();

const decision = await router.routeWithRetry({
  messages,
  userPlan: 'PRO',
  previousModel: lastUsedModel
});
```

### Environment Variables
```bash
# LiteLLM proxy endpoint
LITELLM_BASE_URL=http://litellm-proxy-alb-1992456982.us-east-1.elb.amazonaws.com/v1

# API key for LiteLLM
LITELLM_API_KEY=sk-simplechat-master-2025
```

### Router Prompt Structure
1. **System Context**: Available models and their capabilities
2. **User Query**: The message to analyze
3. **Decision Format**: JSON structure for response
4. **Scoring Guidelines**: How to evaluate models

### Model Availability by Plan

| Plan | Available Models |
|------|-----------------|
| FREE | Limited set (5-10 models) |
| FREEMIUM | Basic models (20-30) |
| PLUS | Extended set (50+) |
| ADVANCED | Most models (100+) |
| MAX | All models (180+) |

## Performance Optimization

### 1. Static Prefix Caching
- Pre-builds model list prompt section
- Reduces token usage by ~20%
- Updates only when model list changes

### 2. Singleton Pattern
- Single router instance per environment
- Prevents duplicate initializations
- Maintains consistent state

### 3. Prompt Optimization
- Simplified prompts for FREE users
- Focused model lists reduce tokens
- Efficient JSON response format

## Troubleshooting

### Common Issues

#### Router Timeout
**Problem**: Router takes too long to respond
```typescript
// Solution: Implement timeout with fallback
try {
  const decision = await Promise.race([
    router.route(query),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Timeout')), 5000)
    )
  ]);
} catch (error) {
  // Fallback to default model
  return { model: 'gpt-4o-mini', reason: 'Router timeout' };
}
```

#### Invalid Model Selection
**Problem**: Router selects unavailable model
```typescript
// Solution: Validate against available models
if (!availableModels.includes(decision.model)) {
  console.warn(`Router selected unavailable model: ${decision.model}`);
  decision.model = getFallbackModel(decision.task_type);
}
```

#### Web Search Not Triggering
**Problem**: Current events queries not getting web search
```typescript
// Solution: Check search query generation
if (decision.needs_web_search && !decision.search_queries?.length) {
  // Generate fallback search queries
  decision.search_queries = generateSearchQueries(query);
}
```

### Debugging Router Decisions

```typescript
// Enable verbose logging
if (process.env.ROUTER_DEBUG === 'true') {
  console.log('Router Input:', {
    query: messages[messages.length - 1].content,
    userPlan,
    availableModels
  });
  
  console.log('Router Decision:', {
    model: decision.recommended_model,
    reasoning: decision.reasoning,
    complexity: decision.complexity,
    webSearch: decision.needs_web_search
  });
}
```

### Performance Optimization

1. **Prompt Caching**: Static prompts loaded once at startup
2. **Decision Caching**: Recent routing decisions cached
3. **Parallel Processing**: Web search and routing in parallel
4. **Timeout Handling**: 5-second timeout with fallback

### Manual Override

Users can always bypass the router:
```typescript
if (request.manualModel) {
  // Skip router, use user's selection
  return {
    model: request.manualModel,
    reason: 'Manual selection',
    routed: false
  };
}
```

## Recent Updates

### December 2024 (v2)
- Added web search detection and query generation
- Improved reasoning model selection
- Enhanced cost optimization logic
- Added plan-based model filtering

### November 2024 (v1)
- Initial router implementation
- Basic complexity analysis
- Model capability matching

---

**See Also**:
- [Model Matrix](/DOCS/reference/model-matrix.md) - Available models
- [Web Search System](/DOCS/web-search-system.md) - Search integration
- [API Documentation](/DOCS/api/endpoints.md) - Chat endpoint details

### Common Issues

1. **Router returns empty response**
   - Check if Gemini model is accessible via LiteLLM
   - Verify API key is correct
   - Look for JSON parsing errors

2. **Wrong model selected**
   - Check plan-based filtering
   - Verify model capabilities in prompt
   - Review router decision reasoning

3. **Web search not triggering**
   - Check `detectRealtimeInfoNeeds()` patterns
   - Verify router is generating `searchQueries`
   - Confirm web search override logic

### Debug Logging

Enable detailed logging:
```typescript
// In bedrock-router-enhanced-v2.ts
console.log('[Router] Decision:', decision);
console.log('[Router] Available models:', availableModels.length);
console.log('[Router] Search queries:', decision.searchQueries);
```

### Router Decision Example
```json
{
  "model": {
    "id": "claude-3-5-sonnet-********",
    "name": "Claude 3.5 Sonnet"
  },
  "reason": "Best for complex coding task with latest knowledge",
  "confidence": 0.95,
  "metrics": {
    "complexity": 8,
    "intent": "coding",
    "specialRequirements": ["code_generation"],
    "estimatedCredits": 5
  },
  "searchQueries": [
    "React 19 useOptimistic hook examples",
    "Next.js 15 best practices 2025"
  ]
}
```

## Recent Updates

### June 23, 2025
- Switched from Google API to LiteLLM proxy
- Updated router model to `gemini-2.0-flash`
- Fixed JSON parsing with markdown wrapper removal
- Added web search override capability
- Improved plan-based model filtering

---
*Last Updated: June 24, 2025*