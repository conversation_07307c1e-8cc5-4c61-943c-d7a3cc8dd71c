# JustSimpleChat Documentation

Welcome to the comprehensive documentation for JustSimpleChat - a multi-provider AI chat platform supporting 180+ models with intelligent routing.

## 🏛️ System Architecture Overview

![JustSimpleChat System Architecture](../system_diagram.svg)

The system consists of six core layers working together to provide a seamless AI chat experience:
- **Frontend Layer**: React 19 + Next.js 15 with modern UI components
- **API Layer**: RESTful endpoints with real-time streaming capabilities
- **Core Services**: AI routing, fallback systems, and business logic
- **Data Layer**: MySQL database with Redis caching for performance
- **AI Providers**: 11+ providers offering 182+ AI models
- **External Systems**: Infrastructure, payments, monitoring, and security

## 📚 Documentation Structure

This documentation is organized into seven main sections:

### 🏗️ [Architecture](./architecture/)
Technical architecture and system design documentation
- [Overview](./architecture/overview.md) - High-level system architecture
- [System Design](./architecture/system-design.md) - Detailed design patterns and decisions
- [Data Flow](./architecture/data-flow.md) - Request/response flows and data pipelines
- [Infrastructure](./architecture/infrastructure.md) - Server and deployment architecture

### 🔌 [API Reference](./api/)
Complete API documentation and integration guides
- [Overview](./api/overview.md) - API design principles and general usage
- [Authentication](./api/authentication.md) - Authentication mechanisms and security
- [Endpoints](./api/endpoints.md) - Complete endpoint reference
- [WebSockets & SSE](./api/websockets.md) - Real-time communication APIs

### 💻 [Development](./development/)
Developer guides and contribution documentation
- [Setup Guide](./development/setup.md) - Local development environment setup
- [Contributing](./development/contributing.md) - Contribution guidelines and workflows
- [Debugging](./development/debugging.md) - Debugging tools and techniques
- [Provider Integration](./development/providers.md) - AI provider integration guide

### 🚀 [Deployment](./deployment/)
Deployment procedures and operational guides
- [Overview](./deployment/overview.md) - Deployment strategy and environments
- [Checklist](./deployment/checklist.md) - Pre and post-deployment checklists
- [Configuration](./deployment/configuration.md) - Environment and service configuration
- [Monitoring](./deployment/monitoring.md) - Production monitoring and alerting

### 🧪 [Testing](./testing/)
Testing strategies and quality assurance
- [Overview](./testing/overview.md) - Testing philosophy and tools
- [Unit Tests](./testing/unit-tests.md) - Component and service testing
- [Integration Tests](./testing/integration-tests.md) - System integration testing
- [AI Testing](./testing/ai-testing.md) - AI-specific testing strategies

### 📖 [User Guides](./guides/)
End-user documentation and best practices
- [Getting Started](./guides/getting-started.md) - Quick start for new users
- [Features](./guides/features.md) - Complete feature documentation
- [Best Practices](./guides/best-practices.md) - Usage tips and optimization
- [FAQ](./guides/faq.md) - Frequently asked questions

### 📋 [Reference](./reference/)
Technical references and specifications
- [Database Schema](./reference/database-schema.md) - Complete database documentation
- [Configuration](./reference/configuration.md) - All configuration options
- [Model Matrix](./reference/model-matrix.md) - Supported models and capabilities
- [Error Codes](./reference/error-codes.md) - Error reference and troubleshooting

## 🎯 Quick Links

### For Users
- [Getting Started Guide](./guides/getting-started.md)
- [Feature Overview](./guides/features.md)
- [FAQ](./guides/faq.md)

### For Developers
- [Development Setup](./development/setup.md)
- [API Documentation](./api/overview.md)
- [Contributing Guide](./development/contributing.md)

### For Operations
- [Deployment Guide](./deployment/overview.md)
- [Monitoring Setup](./deployment/monitoring.md)
- [Configuration Reference](./reference/configuration.md)

## 📝 Documentation Status

This documentation is actively being developed. Each file contains:
- A clear title and description
- Section headings as placeholders
- TODO markers for content to be added

## 🤝 Contributing to Documentation

To contribute to the documentation:

1. Pick a file with TODO sections
2. Research the topic using the codebase and existing documentation
3. Replace TODO sections with actual content
4. Submit a pull request

See the [Contributing Guide](./development/contributing.md) for detailed instructions.

## 📞 Support

For questions not covered in the documentation:
- Check the [FAQ](./guides/faq.md)
- Review the [Error Codes](./reference/error-codes.md)
- Contact support (details in FAQ)

---

**Last Updated**: June 2025  
**Version**: 1.0.0